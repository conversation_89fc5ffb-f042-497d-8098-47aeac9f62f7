(()=>{var e,t,o,r={9293:(e,t,o)=>{"use strict";const r=window.wp.hooks,c=window.wp.components,s=JSON.parse('{"title":"Product Title","description":"Display the title of a product."}');var n=o(8992);const i=window.wp.blocks;function a(e,{blockDescription:t,blockIcon:o,blockTitle:r,variationName:c,scope:s}){(0,i.registerBlockVariation)(e,{description:t,name:c,title:r,isActive:e=>e.__woocommerceNamespace===c,icon:{src:o},attributes:{__woocommerceNamespace:c},scope:s})}var l=o(790);const m="core/post-title",d="woocommerce/product-query/product-title";a(m,{blockDescription:s.description,blockIcon:(0,l.jsx)(c.I<PERSON>,{icon:n.A}),blockTitle:s.title,variationName:d,scope:["block"]});var u=o(7715),p=o(7723),h=o(4530);const g=(0,p.__)("Product Summary","woocommerce"),w=(h.A,u.A,"core/post-excerpt");a(w,{blockDescription:(0,p.__)("Display a short description about a product.","woocommerce"),blockIcon:(0,l.jsx)(c.Icon,{icon:u.A}),blockTitle:g,variationName:"woocommerce/product-query/product-summary",scope:[]});var _=o(5534);const b="core/post-template",k="woocommerce/product-query/product-template";a(b,{blockDescription:(0,p.__)("Contains the block elements used to render a product, like its name, featured image, rating, and more.","woocommerce"),blockIcon:(0,l.jsx)(c.Icon,{icon:_.A}),blockTitle:(0,p.__)("Product template","woocommerce"),variationName:k,scope:["block","inserter"]});const y=window.wp.blockEditor,f=window.wp.data,x=window.wc.wcTypes;var S=o(6087);const v=window.wc.wcSettings,j=e=>"core/query"===e.name&&"woocommerce/product-query"===e.attributes.namespace,C=(e,t)=>{let o=[];return e.forEach((e=>{t(e)&&(o=[...o,e.clientId]),o=[...o,...C(e.innerBlocks,t)]})),o},N=(0,v.getSettingWithCoercion)("postTemplateHasSupportForGridView",!1,x.isBoolean);function I(e,t){const{[t]:o,...r}=e;return r}const A=JSON.parse('{"name":"woocommerce/product-collection"}');let E=function(e){return e.GRID="flex",e.STACK="list",e.CAROUSEL="carousel",e}({}),T=function(e){return e.FILL="fill",e.FIXED="fixed",e}({}),O=function(e){return e.SINGLE="single",e.THUMBNAIL="thumbnail",e}({});A.name;const P=(0,v.getSetting)("stockStatusOptions",[]),B={query:{perPage:9,pages:0,offset:0,postType:"product",order:"asc",orderBy:"title",search:"",exclude:[],inherit:!1,taxQuery:{},isProductCollectionBlock:!0,featured:!1,woocommerceOnSale:!1,woocommerceStockStatus:(0,v.getSetting)("hideOutOfStockItems",!1)?Object.keys(I(P,"outofstock")):Object.keys(P),woocommerceAttributes:[],woocommerceHandPickedProducts:[],timeFrame:void 0,priceRange:void 0,filterable:!1,relatedBy:{categories:!0,tags:!0}},tagName:"div",displayLayout:{type:E.GRID,columns:3,shrinkColumns:!0},dimensions:{widthType:T.FILL},queryContextIncludes:["collection"],forcePageReload:!1},L=(O.THUMBNAIL,({name:e,attributes:t})=>"core/post-template"===e&&"woocommerce/product-query/product-template"===t.__woocommerceNamespace),R=e=>"grid"===e?E.GRID:"default"===e?E.STACK:E.GRID,q=e=>e.map((e=>{const{name:t,attributes:o}=e,r=q(e.innerBlocks);return L(e)?((e,t)=>{const{__woocommerceNamespace:o,className:r,layout:c,...s}=e.attributes;return(0,i.createBlock)("woocommerce/product-template",s,t)})(e,r):(({name:e,attributes:t})=>"core/post-title"===e&&"woocommerce/product-query/product-title"===t.__woocommerceNamespace)(e)?((e,t)=>{const{__woocommerceNamespace:o,...r}=e.attributes;return(0,i.createBlock)("core/post-title",{__woocommerceNamespace:"woocommerce/product-collection/product-title",...r},t)})(e,r):(({name:e,attributes:t})=>"core/post-excerpt"===e&&"woocommerce/product-query/product-summary"===t.__woocommerceNamespace)(e)?((e,t)=>{const{__woocommerceNamespace:o,...r}=e.attributes;return(0,i.createBlock)("core/post-excerpt",{__woocommerceNamespace:"woocommerce/product-collection/product-summary",...r},t)})(e,r):(0,i.createBlock)(t,o,r)})),F=e=>{const t=(0,f.select)("core/block-editor").getBlock(e),o=(e=>{const t=(0,f.select)("core/block-editor").getBlockRootClientId(e)||void 0;return(0,f.select)("core/block-editor").canInsertBlockType("woocommerce/product-collection",t)})(e);if(t&&o){const{attributes:o={},innerBlocks:r=[]}=t,c=((e,t)=>{const o=t.find(L),{layout:r}=o?.attributes||{};return N?(e=>{if(void 0===e)return B.displayLayout;const{type:t,columnCount:o}=e;return{type:R(t),columns:o}})(r):e.displayLayout})(o,r),s=(e=>{const{query:t,namespace:o,...r}=e,{__woocommerceAttributes:c,__woocommerceStockStatus:s,__woocommerceOnSale:n,include:i,...a}=t;return{...r,query:{woocommerceAttributes:c,woocommerceStockStatus:s,woocommerceOnSale:n,woocommerceHandPickedProducts:i,taxQuery:{},isProductCollectionBlock:!0,...a},convertedFromProducts:!0}})({...o,displayLayout:c}),n=q(r),a=(0,i.createBlock)("woocommerce/product-collection",s,n);return(0,f.dispatch)("core/block-editor").replaceBlock(e,a),!0}return!1},$=()=>{var e;e={status:"notseen",time:Date.now(),displayCount:0},window.localStorage.setItem("wc-blocks_upgraded-products-to-product-collection",JSON.stringify(e)),(()=>{if(0===(0,f.select)("core/block-editor").getGlobalBlockCount("core/query"))return;const e=(t=(0,f.select)("core/block-editor").getBlocks(),C(t,j));var t;0!==e.length&&(e=>{const t=e.map(F);t.length&&t.every((e=>!!e))})(e)})()},D=e=>{if(!(e=>null===e)(t=e)&&t instanceof Object&&t.constructor===Object){const t=e.getEditedPostType();return"wp_template"===t||"wp_template_part"===t}var t;return!1},V="woocommerce/product-query",G="core/query",M=["attributes","presets","productSelector","onSale","stockStatus","wooInherit"],z=["taxQuery","search",...M],H=(0,v.getSetting)("stockStatusOptions",[]),U=(0,v.getSetting)("hideOutOfStockItems",!1),W={allowedControls:z,displayLayout:{type:"flex",columns:3},query:{perPage:9,pages:0,offset:0,postType:"product",order:"asc",orderBy:"title",author:"",search:"",exclude:[],sticky:"",inherit:!1,__woocommerceAttributes:[],__woocommerceStockStatus:U?Object.keys(I(H,"outofstock")):Object.keys(H)}},Q=[["core/post-template",{__woocommerceNamespace:k,className:"products-block-post-template",...(0,v.getSettingWithCoercion)("postTemplateHasSupportForGridView",!1,x.isBoolean)&&{layout:{type:"grid",columnCount:3}}},[["woocommerce/product-image",{imageSizing:O.THUMBNAIL}],["core/post-title",{textAlign:"center",level:3,fontSize:"medium",style:{spacing:{margin:{bottom:"0.75rem",top:"0"}}},isLink:!0,__woocommerceNamespace:d}],["woocommerce/product-price",{textAlign:"center",fontSize:"small"}],["woocommerce/product-button",{textAlign:"center",fontSize:"small"}]]],["core/query-pagination",{layout:{type:"flex",justifyContent:"center"}}],["core/query-no-results"]];let J=function(e){return e.PRODUCT_QUERY="woocommerce/product-query",e.RELATED_PRODUCTS="woocommerce/related-products",e}({});function K(e,t){const{query:o}=e.attributes;e.setAttributes({query:{...o,...t}})}var Z=o(4921);function X(e,t,o){const r=new Set(t.map((e=>e[o])));return e.filter((e=>!r.has(e[o])))}const Y=window.wp.htmlEntities,ee={clear:(0,p.__)("Clear all selected items","woocommerce"),noItems:(0,p.__)("No items found.","woocommerce"),
/* Translators: %s search term */
noResults:(0,p.__)("No results for %s","woocommerce"),search:(0,p.__)("Search for items","woocommerce"),selected:e=>(0,p.sprintf)(/* translators: Number of items selected from list. */ /* translators: Number of items selected from list. */
(0,p._n)("%d item selected","%d items selected",e,"woocommerce"),e),updated:(0,p.__)("Search results updated.","woocommerce")},te=(e,t=e)=>{const o=e.reduce(((e,t)=>{const o=t.parent||0;return e[o]||(e[o]=[]),e[o].push(t),e}),{}),r=t.reduce(((e,t)=>(e[String(t.id)]=t,e)),{});const c=["0"],s=(e={})=>e.parent?[...s(r[e.parent]),e.name]:e.name?[e.name]:[],n=e=>e.map((e=>{const t=o[e.id];return c.push(""+e.id),{...e,breadcrumbs:s(r[e.parent]),children:t&&t.length?n(t):[]}})),i=n(o[0]||[]);return Object.entries(o).forEach((([e,t])=>{c.includes(e)||i.push(...n(t||[]))})),i},oe=(e,t)=>{if(!t)return e;const o=new RegExp(`(${t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")})`,"ig");return e.split(o).map(((e,t)=>o.test(e)?(0,l.jsx)("strong",{children:e},t):(0,l.jsx)(S.Fragment,{children:e},t)))},re=({label:e})=>(0,l.jsx)("span",{className:"woocommerce-search-list__item-count",children:e}),ce=e=>{const{item:t,search:o}=e,r=t.breadcrumbs&&t.breadcrumbs.length;return(0,l.jsxs)("span",{className:"woocommerce-search-list__item-label",children:[r?(0,l.jsx)("span",{className:"woocommerce-search-list__item-prefix",children:(c=t.breadcrumbs,1===c.length?c.slice(0,1).toString():2===c.length?c.slice(0,1).toString()+" › "+c.slice(-1).toString():c.slice(0,1).toString()+" … "+c.slice(-1).toString())}):null,(0,l.jsx)("span",{className:"woocommerce-search-list__item-name",children:oe((0,Y.decodeEntities)(t.name),o)})]});var c},se=({countLabel:e,className:t,depth:o=0,controlId:r="",item:s,isSelected:n,isSingle:i,onSelect:a,search:m="",selected:d,useExpandedPanelId:u,...p})=>{const[h,g]=u,w=null!=e&&void 0!==s.count&&null!==s.count,_=!!s.breadcrumbs?.length,b=!!s.children?.length,k=h===s.id,y=(0,Z.A)(["woocommerce-search-list__item",`depth-${o}`,t],{"has-breadcrumbs":_,"has-children":b,"has-count":w,"is-expanded":k,"is-radio-button":i});(0,S.useEffect)((()=>{b&&n&&g(s.id)}),[s,b,n,g]);const f=p.name||`search-list-item-${r}`,x=`${f}-${s.id}`,v=(0,S.useCallback)((()=>{g(k?-1:Number(s.id))}),[k,s.id,g]);return b?(0,l.jsx)("div",{className:y,onClick:v,onKeyDown:e=>"Enter"===e.key||" "===e.key?v():null,role:"treeitem",tabIndex:0,children:i?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("input",{type:"radio",id:x,name:f,value:s.value,onChange:a(s),onClick:e=>e.stopPropagation(),checked:n,className:"woocommerce-search-list__item-input",...p}),(0,l.jsx)(ce,{item:s,search:m}),w?(0,l.jsx)(re,{label:e||s.count}):null]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(c.CheckboxControl,{className:"woocommerce-search-list__item-input",checked:n,...!n&&s.children.some((e=>d.find((t=>t.id===e.id))))?{indeterminate:!0}:{},label:oe((0,Y.decodeEntities)(s.name),m),onChange:()=>{n?a(X(d,s.children,"id"))():a(function(e,t){const o=X(t,e,"id");return[...e,...o]}(d,s.children))()},onClick:e=>e.stopPropagation()}),w?(0,l.jsx)(re,{label:e||s.count}):null]})}):(0,l.jsxs)("label",{htmlFor:x,className:y,children:[i?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("input",{...p,type:"radio",id:x,name:f,value:s.value,onChange:a(s),checked:n,className:"woocommerce-search-list__item-input"}),(0,l.jsx)(ce,{item:s,search:m})]}):(0,l.jsx)(c.CheckboxControl,{...p,id:x,name:f,className:"woocommerce-search-list__item-input",value:(0,Y.decodeEntities)(s.value),label:oe((0,Y.decodeEntities)(s.name),m),onChange:a(s),checked:n}),w?(0,l.jsx)(re,{label:e||s.count}):null]})},ne=se;var ie=o(2624),ae=o(9491),le=o(3028);o(5022);const me=({id:e,label:t,popoverContents:o,remove:r,screenReaderLabel:s,className:n=""})=>{const[i,a]=(0,S.useState)(!1),m=(0,ae.useInstanceId)(me);if(s=s||t,!t)return null;t=(0,Y.decodeEntities)(t);const d=(0,Z.A)("woocommerce-tag",n,{"has-remove":!!r}),u=`woocommerce-tag__label-${m}`,g=(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("span",{className:"screen-reader-text",children:s}),(0,l.jsx)("span",{"aria-hidden":"true",children:t})]});return(0,l.jsxs)("span",{className:d,children:[o?(0,l.jsx)(c.Button,{className:"woocommerce-tag__text",id:u,onClick:()=>a(!0),children:g}):(0,l.jsx)("span",{className:"woocommerce-tag__text",id:u,children:g}),o&&i&&(0,l.jsx)(c.Popover,{onClose:()=>a(!1),children:o}),r&&(0,l.jsx)(c.Button,{className:"woocommerce-tag__remove",onClick:r(e),label:(0,p.sprintf)(
// Translators: %s label.
// Translators: %s label.
(0,p.__)("Remove %s","woocommerce"),t),"aria-describedby":u,children:(0,l.jsx)(h.A,{icon:le.A,size:20,className:"clear-icon",role:"img"})})]})},de=me;o(1939);const ue=e=>(0,l.jsx)(ne,{...e}),pe=e=>{const{list:t,selected:o,renderItem:r,depth:c=0,onSelect:s,instanceId:n,isSingle:i,search:a,useExpandedPanelId:m}=e,[d]=m;return t?(0,l.jsx)(l.Fragment,{children:t.map((t=>{const u=t.children?.length&&!i?t.children.every((({id:e})=>o.find((t=>t.id===e)))):!!o.find((({id:e})=>e===t.id)),p=t.children?.length&&d===t.id;return(0,l.jsxs)(S.Fragment,{children:[(0,l.jsx)("li",{children:r({item:t,isSelected:u,onSelect:s,isSingle:i,selected:o,search:a,depth:c,useExpandedPanelId:m,controlId:n})}),p?(0,l.jsx)(pe,{...e,list:t.children,depth:c+1}):null]},t.id)}))}):null},he=({isLoading:e,isSingle:t,selected:o,messages:r,onChange:s,onRemove:n})=>{if(e||t||!o)return null;const i=o.length;return(0,l.jsxs)("div",{className:"woocommerce-search-list__selected",children:[(0,l.jsxs)("div",{className:"woocommerce-search-list__selected-header",children:[(0,l.jsx)("strong",{children:r.selected(i)}),i>0?(0,l.jsx)(c.Button,{variant:"link",isDestructive:!0,onClick:()=>s([]),"aria-label":r.clear,children:(0,p.__)("Clear all","woocommerce")}):null]}),i>0?(0,l.jsx)("ul",{children:o.map(((e,t)=>(0,l.jsx)("li",{children:(0,l.jsx)(de,{label:e.name,id:e.id,remove:n})},t)))}):null]})},ge=({filteredList:e,search:t,onSelect:o,instanceId:r,useExpandedPanelId:c,...s})=>{const{messages:n,renderItem:i,selected:a,isSingle:m}=s,d=i||ue;return 0===e.length?(0,l.jsxs)("div",{className:"woocommerce-search-list__list is-not-found",children:[(0,l.jsx)("span",{className:"woocommerce-search-list__not-found-icon",children:(0,l.jsx)(h.A,{icon:ie.A,role:"img"})}),(0,l.jsx)("span",{className:"woocommerce-search-list__not-found-text",children:t?(0,p.sprintf)(n.noResults,t):n.noItems})]}):(0,l.jsx)("ul",{className:"woocommerce-search-list__list",children:(0,l.jsx)(pe,{useExpandedPanelId:c,list:e,selected:a,renderItem:d,onSelect:o,instanceId:r,isSingle:m,search:t})})},we=e=>{const{className:t="",isCompact:o,isHierarchical:r,isLoading:s,isSingle:n,list:i,messages:a=ee,onChange:m,onSearch:d,selected:u,type:h="text",debouncedSpeak:g}=e,[w,_]=(0,S.useState)(""),b=(0,S.useState)(-1),k=(0,ae.useInstanceId)(we),y=(0,S.useMemo)((()=>({...ee,...a})),[a]),f=(0,S.useMemo)((()=>((e,t,o)=>{if(!t)return o?te(e):e;const r=new RegExp(t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"),"i"),c=e.map((e=>!!r.test(e.name)&&e)).filter(Boolean);return o?te(c,e):c})(i,w,r)),[i,w,r]);(0,S.useEffect)((()=>{g&&g(y.updated)}),[g,y]),(0,S.useEffect)((()=>{"function"==typeof d&&d(w)}),[w,d]);const x=(0,S.useCallback)((e=>()=>{n&&m([]);const t=u.findIndex((({id:t})=>t===e));m([...u.slice(0,t),...u.slice(t+1)])}),[n,u,m]),v=(0,S.useCallback)((e=>()=>{Array.isArray(e)?m(e):-1===u.findIndex((({id:t})=>t===e.id))?m(n?[e]:[...u,e]):x(e.id)()}),[n,x,m,u]),j=(0,S.useCallback)((e=>{const[t]=u.filter((t=>!e.find((e=>t.id===e.id))));x(t.id)()}),[x,u]);return(0,l.jsxs)("div",{className:(0,Z.A)("woocommerce-search-list",t,{"is-compact":o,"is-loading":s,"is-token":"token"===h}),children:["text"===h&&(0,l.jsx)(he,{...e,onRemove:x,messages:y}),(0,l.jsx)("div",{className:"woocommerce-search-list__search",children:"text"===h?(0,l.jsx)(c.TextControl,{label:y.search,type:"search",value:w,onChange:e=>_(e)}):(0,l.jsx)(c.FormTokenField,{disabled:s,label:y.search,onChange:j,onInputChange:e=>_(e),suggestions:[],__experimentalValidateInput:()=>!1,value:s?[(0,p.__)("Loading…","woocommerce")]:u.map((e=>({...e,value:e.name}))),__experimentalShowHowTo:!1})}),s?(0,l.jsx)("div",{className:"woocommerce-search-list__list",children:(0,l.jsx)(c.Spinner,{})}):(0,l.jsx)(ge,{...e,search:w,filteredList:f,messages:y,onSelect:v,instanceId:k,useExpandedPanelId:b})]})},_e=((0,c.withSpokenMessages)(we),window.wp.url),be=window.wp.apiFetch;var ke=o.n(be);const ye=(0,v.getSetting)("wcBlocksConfig",{pluginUrl:"",productCount:0,defaultAvatar:"",restApiRoutes:{},wordCountType:"words"}),fe=(ye.pluginUrl,ye.pluginUrl,v.STORE_PAGES.shop,v.STORE_PAGES.checkout,v.STORE_PAGES.checkout,v.STORE_PAGES.privacy,v.STORE_PAGES.privacy,v.STORE_PAGES.terms,v.STORE_PAGES.terms,v.STORE_PAGES.cart,v.STORE_PAGES.cart,v.STORE_PAGES.myaccount?.permalink?v.STORE_PAGES.myaccount.permalink:(0,v.getSetting)("wpLoginUrl","/wp-login.php"),(0,v.getSetting)("localPickupEnabled",!1),(0,v.getSetting)("shippingMethodsExist",!1),(0,v.getSetting)("shippingEnabled",!0),(0,v.getSetting)("countries",{})),xe=(0,v.getSetting)("countryData",{}),Se={...Object.fromEntries(Object.keys(xe).filter((e=>!0===xe[e].allowBilling)).map((e=>[e,fe[e]||""]))),...Object.fromEntries(Object.keys(xe).filter((e=>!0===xe[e].allowShipping)).map((e=>[e,fe[e]||""])))},ve=(Object.fromEntries(Object.keys(Se).map((e=>[e,xe[e].states||{}]))),Object.fromEntries(Object.keys(Se).map((e=>[e,xe[e].locale||{}]))),{address:["first_name","last_name","company","address_1","address_2","city","postcode","country","state","phone"],contact:["email"],order:[]}),je=((0,v.getSetting)("addressFieldsLocations",ve).address,(0,v.getSetting)("addressFieldsLocations",ve).contact,(0,v.getSetting)("addressFieldsLocations",ve).order,(0,v.getSetting)("additionalOrderFields",{}),(0,v.getSetting)("additionalContactFields",{}),(0,v.getSetting)("additionalAddressFields",{}),(e,t)=>{const o=new Map;return e.filter((e=>{const r=t(e);return!o.has(r)&&(o.set(r,e),!0)}))}),Ce=e=>ke()({path:`wc/store/v1/products/attributes/${e}/terms`});const Ne=window.wp.escapeHtml,Ie=({message:e,type:t})=>e?"general"===t?(0,l.jsxs)("span",{children:[(0,p.__)("The following error was returned","woocommerce"),(0,l.jsx)("br",{}),(0,l.jsx)("code",{children:(0,Ne.escapeHTML)(e)})]}):"api"===t?(0,l.jsxs)("span",{children:[(0,p.__)("The following error was returned from the API","woocommerce"),(0,l.jsx)("br",{}),(0,l.jsx)("code",{children:(0,Ne.escapeHTML)(e)})]}):e:(0,p.__)("An error has prevented the block from being updated.","woocommerce"),Ae=({error:e})=>(0,l.jsx)("div",{className:"wc-block-error-message",children:Ie(e)});var Ee=o(1609);const Te=({className:e,item:t,isSelected:o,isLoading:r,onSelect:s,disabled:n,...i})=>(0,l.jsxs)(l.Fragment,{children:[(0,Ee.createElement)(se,{...i,key:t.id,className:e,isSelected:o,item:t,onSelect:s,disabled:n}),o&&r&&(0,l.jsx)("div",{className:(0,Z.A)("woocommerce-search-list__item","woocommerce-product-attributes__item","depth-1","is-loading","is-not-active"),children:(0,l.jsx)(c.Spinner,{})},"loading")]}),Oe=((0,v.getSetting)("attributes",[]).reduce(((e,t)=>{const o=(r=t)&&r.attribute_name?{id:parseInt(r.attribute_id,10),name:r.attribute_name,taxonomy:"pa_"+r.attribute_name,label:r.attribute_label,orderby:r.attribute_orderby}:null;var r;return o&&o.id&&e.push(o),e}),[]),e=>{const{count:t,id:o,name:r,parent:c}=e;return{count:t,id:o,name:r,parent:c,breadcrumbs:[],children:[],value:(0,x.isAttributeTerm)(e)?e.attr_slug:""}});o(2663);const Pe=(0,ae.withInstanceId)((({onChange:e,onOperatorChange:t,instanceId:o,isCompact:r=!1,messages:s={},operator:n="any",selected:i,type:a="text"})=>{const{errorLoadingAttributes:m,isLoadingAttributes:d,productsAttributes:u}=function(e){const[t,o]=(0,S.useState)(null),[r,c]=(0,S.useState)(!1),[s,n]=(0,S.useState)([]),i=(0,S.useRef)(!1);return(0,S.useEffect)((()=>{if(e&&!r&&!i.current)return async function(){c(!0);try{const e=await ke()({path:"wc/store/v1/products/attributes"}),t=[];for(const o of e){const e=await Ce(o.id);t.push({...o,parent:0,terms:e.map((e=>({...e,attr_slug:o.taxonomy,parent:o.id})))})}n(t),i.current=!0}catch(e){e instanceof Error&&o(await(async e=>{if(!("json"in e))return{code:e.code||"",message:e.message,type:e.type||"general"};try{const t=await e.json();return{code:t.code||"",message:t.message,type:t.type||"api"}}catch(e){return{message:e.message,type:"general"}}})(e))}finally{c(!1)}}(),()=>{i.current=!0}}),[r,e]),{errorLoadingAttributes:t,isLoadingAttributes:r,productsAttributes:s}}(!0),h=u.reduce(((e,t)=>{const{terms:o,...r}=t;return[...e,Oe(r),...o.map(Oe)]}),[]);return s={clear:(0,p.__)("Clear all product attributes","woocommerce"),noItems:(0,p.__)("Your store doesn't have any product attributes.","woocommerce"),search:(0,p.__)("Search for product attributes","woocommerce"),selected:e=>(0,p.sprintf)(/* translators: %d is the count of attributes selected. */ /* translators: %d is the count of attributes selected. */
(0,p._n)("%d attribute selected","%d attributes selected",e,"woocommerce"),e),updated:(0,p.__)("Product attribute search results updated.","woocommerce"),...s},m?(0,l.jsx)(Ae,{error:m}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(we,{className:"woocommerce-product-attributes",isCompact:r,isHierarchical:!0,isLoading:d,isSingle:!1,list:h,messages:s,onChange:e,renderItem:e=>{const{item:t,search:r,depth:c=0}=e,s=t.count||0,n=["woocommerce-product-attributes__item","woocommerce-search-list__item",{"is-searching":r.length>0,"is-skip-level":0===c&&0!==t.parent}];if(!t.breadcrumbs.length)return(0,l.jsx)(Te,{...e,className:(0,Z.A)(n),item:t,isLoading:d,disabled:0===t.count,name:`attributes-${o}`,countLabel:(0,p.sprintf)(/* translators: %d is the count of terms. */ /* translators: %d is the count of terms. */
(0,p._n)("%d term","%d terms",s,"woocommerce"),s),"aria-label":(0,p.sprintf)(/* translators: %1$s is the item name, %2$d is the count of terms for the item. */ /* translators: %1$s is the item name, %2$d is the count of terms for the item. */
(0,p._n)("%1$s, has %2$d term","%1$s, has %2$d terms",s,"woocommerce"),t.name,s)});const i=`${t.breadcrumbs[0]}: ${t.name}`;return(0,l.jsx)(se,{...e,name:`terms-${o}`,className:(0,Z.A)(...n,"has-count"),countLabel:(0,p.sprintf)(/* translators: %d is the count of products. */ /* translators: %d is the count of products. */
(0,p._n)("%d product","%d products",s,"woocommerce"),s),"aria-label":(0,p.sprintf)(/* translators: %1$s is the attribute name, %2$d is the count of products for that attribute. */ /* translators: %1$s is the attribute name, %2$d is the count of products for that attribute. */
(0,p._n)("%1$s, has %2$d product","%1$s, has %2$d products",s,"woocommerce"),i,s)})},selected:i.map((({id:e})=>h.find((t=>t.id===e)))).filter(Boolean),type:a}),!!t&&(0,l.jsx)("div",{hidden:i.length<2,children:(0,l.jsx)(c.SelectControl,{className:"woocommerce-product-attributes__operator",label:(0,p.__)("Display products matching","woocommerce"),help:(0,p.__)("Pick at least two attributes to use this setting.","woocommerce"),value:n,onChange:t,options:[{label:(0,p.__)("Any selected attributes","woocommerce"),value:"any"},{label:(0,p.__)("All selected attributes","woocommerce"),value:"all"}]})})]})})),Be=[{key:"title/asc",name:(0,p.__)("Sorted by title","woocommerce")},{key:"date/desc",name:(0,p.__)("Newest","woocommerce")},{key:"popularity/desc",name:(0,p.__)("Best Selling","woocommerce")},{key:"rating/desc",name:(0,p.__)("Top Rated","woocommerce")}];function Le(e){const{query:t}=e.attributes;return(0,l.jsxs)(c.PanelBody,{className:"woocommerce-product-query-panel__sort",title:(0,p.__)("Popular Filters","woocommerce"),initialOpen:!0,children:[(0,l.jsx)("p",{children:(0,p.__)("Arrange products by popular pre-sets.","woocommerce")}),(0,l.jsx)(c.CustomSelectControl,{hideLabelFromVision:!0,label:(0,p.__)("Choose among these pre-sets","woocommerce"),onChange:t=>{if(!t.selectedItem?.key)return;const[o,r]=t.selectedItem?.key?.split("/");K(e,{order:r,orderBy:o})},options:Be,value:Be.find((e=>e.key===`${t.orderBy}/${t.order}`))})]})}var Re=o(1244),qe=o.n(Re);qe()("wc-admin:tracks:stats");const Fe=qe()("wc-admin:tracks");function $e({children:e,className:t,actionLabel:o,onActionClick:r,...s}){return(0,l.jsx)(c.Notice,{...s,className:(0,Z.$)("wc-block-editor-components-upgrade-downgrade-notice",t),actions:[{label:o,onClick:r,noDefaultClasses:!0,variant:"link"}],children:(0,l.jsx)("div",{className:"wc-block-editor-components-upgrade-downgrade-notice__text",children:e})})}o(9969);const De=e=>{const t=(0,S.createInterpolateElement)((0,p.__)("Upgrade all Products (Beta) blocks on this page to <strongText /> for more features!","woocommerce"),{strongText:(0,l.jsx)("strong",{children:(0,p.__)("Product Collection","woocommerce")})}),o=(0,p.__)("Upgrade to Product Collection","woocommerce");return(0,l.jsx)($e,{isDismissible:!1,actionLabel:o,onActionClick:()=>{e.upgradeBlock(),function(e,t){if(Fe("recordevent %s %o","wcadmin_"+e,t,{_tqk:window._tkq,shouldRecord:!!window._tkq&&!!window.wcTracks&&!!window.wcTracks.isEnabled}),!window.wcTracks||"function"!=typeof window.wcTracks.recordEvent)return!1;window.wcTracks.recordEvent(e,t)}("blocks_product_collection_migration_between_products_beta",{transform_to:"product_collection"})},children:t})};o(1404);const Ve=M.map((e=>`__woocommerce${e[0].toUpperCase()}${e.slice(1)}`));function Ge(e){const t="string"==typeof e?e:e.value;return Object.entries(H).find((([,e])=>e===t))?.[0]}const Me={attributes:e=>{const{query:t}=e.attributes,[o,r]=(0,S.useState)([]);return(0,S.useEffect)((()=>{t.__woocommerceAttributes&&r(t.__woocommerceAttributes.map((({termId:e})=>({id:e}))))}),[t.__woocommerceAttributes]),(0,l.jsxs)(c.__experimentalToolsPanelItem,{label:(0,p.__)("Product Attributes","woocommerce"),hasValue:()=>t.__woocommerceAttributes?.length,children:[(0,l.jsx)(Pe,{messages:{search:(0,p.__)("Attributes","woocommerce")},selected:o,onChange:t=>{const o=t.map((({id:e,value:t})=>({termId:e,taxonomy:t})));K(e,{__woocommerceAttributes:o})},operator:"any",isCompact:!0,type:"token"}),(0,l.jsx)(c.ExternalLink,{className:"woocommerce-product-query-panel__external-link",href:"/wp-admin/edit.php?post_type=product&page=product_attributes",children:(0,p.__)("Manage attributes","woocommerce")})]})},onSale:e=>{const{query:t}=e.attributes;return(0,l.jsx)(c.__experimentalToolsPanelItem,{label:(0,p.__)("Sale status","woocommerce"),hasValue:()=>t.__woocommerceOnSale,children:(0,l.jsx)(c.ToggleControl,{label:(0,p.__)("Show only products on sale","woocommerce"),checked:t.__woocommerceOnSale||!1,onChange:t=>{K(e,{__woocommerceOnSale:t})}})})},productSelector:e=>{const{query:t}=e.attributes,o=function(){const[e,t]=(0,S.useState)([]);return(0,S.useEffect)((()=>{(({selected:e=[],search:t="",queryArgs:o={}})=>{const r=(({selected:e=[],search:t="",queryArgs:o={}})=>{const r=ye.productCount>100,c={per_page:r?100:0,catalog_visibility:"any",search:t,orderby:"title",order:"asc"},s=[(0,_e.addQueryArgs)("/wc/store/v1/products",{...c,...o})];return r&&e.length&&s.push((0,_e.addQueryArgs)("/wc/store/v1/products",{catalog_visibility:"any",include:e,per_page:0})),s})({selected:e,search:t,queryArgs:o});return Promise.all(r.map((e=>ke()({path:e})))).then((e=>{const t=e.flat(),o=je(t,(e=>e.id));return o.map((e=>({...e,parent:0})))})).catch((e=>{throw e}))})({selected:[]}).then((e=>{t(e)}))}),[]),e}();return(0,l.jsx)(c.__experimentalToolsPanelItem,{label:(0,p.__)("Hand-picked Products","woocommerce"),hasValue:()=>t.include?.length,children:(0,l.jsx)(c.FormTokenField,{disabled:!o.length,displayTransform:e=>Number.isNaN(Number(e))?e:o.find((t=>t.id===Number(e)))?.name||"",label:(0,p.__)("Pick some products","woocommerce"),onChange:t=>{const r=t.map((e=>o.find((t=>t.name===e||t.id===Number(e)))?.id)).filter(Boolean).map(String);if(!r.length&&e.attributes.query.include){const t=I(e.attributes.query,"include");K({...e,attributes:{...e.attributes,query:t}},{})}else K(e,{include:r})},suggestions:o.map((e=>e.name)),validateInput:e=>o.find((t=>t.name===e)),value:o.length?t?.include||[]:[(0,p.__)("Loading…","woocommerce")],__experimentalExpandOnFocus:!0})})},stockStatus:e=>{const{query:t}=e.attributes;return(0,l.jsx)(c.__experimentalToolsPanelItem,{label:(0,p.__)("Stock status","woocommerce"),hasValue:()=>t.__woocommerceStockStatus,children:(0,l.jsx)(c.FormTokenField,{label:(0,p.__)("Stock status","woocommerce"),onChange:t=>{const o=t.map(Ge).filter(Boolean);K(e,{__woocommerceStockStatus:o})},suggestions:Object.values(H),validateInput:e=>Object.values(H).includes(e),value:t?.__woocommerceStockStatus?.map((e=>H[e]))||[],__experimentalExpandOnFocus:!0})})},wooInherit:e=>{const t=function(e,t){const o=(0,S.useRef)();return(0,S.useEffect)((()=>{o.current===e||t&&!t(e,o.current)||(o.current=e)}),[e,t]),o.current}(e.attributes.query,(e=>!1===e.inherit));return(0,l.jsx)(c.ToggleControl,{className:"woo-inherit-query-toggle",label:(0,p.__)("Inherit query from template","woocommerce"),help:(0,p.__)("Toggle to use the global query context that is set with the current template, such as variations of the product catalog or search. Disable to customize the filtering independently.","woocommerce"),checked:e.attributes.query.inherit||!1,onChange:o=>{const r={inherit:o};o&&(r.perPage=(0,v.getSettingWithCoercion)("loopShopPerPage",12,x.isNumber)),K(e,{...e.defaultWooQueryParams,...r,...!1===o&&{...t}})}})}},ze=e=>{const t=function(e){const t=(0,f.useSelect)("core/edit-site"),o=(0,f.useSelect)((t=>t(i.store).getActiveBlockVariation(G,e)?.allowedControls),[e]);return D(t)?function(e){return e.query.inherit}(e)?o.filter((e=>"wooInherit"===e)):o:o.filter((e=>"wooInherit"!==e))}(e.attributes),o=function(e){const t=(0,f.useSelect)((t=>t("core/blocks").getBlockVariations(G).find((t=>t.name===e))?.attributes));return t?Object.assign({},...Ve.map((e=>({[e]:t.query[e]})))):{}}(e.attributes.namespace),r=!((s=e).name===G&&s.attributes.namespace===J.RELATED_PRODUCTS);var s;return(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)(y.InspectorControls,{children:[r&&(0,l.jsx)(De,{upgradeBlock:$}),t?.includes("presets")&&(0,l.jsx)(Le,{...e}),r&&(0,l.jsx)(c.__experimentalToolsPanel,{className:"woocommerce-product-query-toolspanel",label:(0,p.__)("Advanced Filters","woocommerce"),resetAll:()=>{K(e,o)},children:Object.entries(Me).map((([r,c])=>t?.includes(r)?(0,Ee.createElement)(c,{...e,defaultWooQueryParams:o,key:r}):null))})]})})};(0,r.addFilter)("editor.BlockEdit",G,(e=>t=>{return(o=t).name===G&&Object.values(J).includes(o.attributes.namespace)?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(ze,{...t}),(0,l.jsx)(e,{...t})]}):(0,l.jsx)(e,{...t});var o})),o(8286);var He=o(5573);const Ue=(0,l.jsx)(He.SVG,{xmlns:"http://www.w3.org/2000/SVG",viewBox:"0 0 24 24",children:(0,l.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M4.5 19.375L4.5 7.625C4.5 7.55596 4.55596 7.5 4.625 7.5L16.375 7.5C16.444 7.5 16.5 7.55596 16.5 7.625L16.5 19.375C16.5 19.444 16.444 19.5 16.375 19.5L4.625 19.5C4.55596 19.5 4.5 19.444 4.5 19.375ZM4.625 21C3.72754 21 3 20.2725 3 19.375L3 7.625C3 6.72754 3.72754 6 4.625 6L16.375 6C17.2725 6 18 6.72754 18 7.625L18 19.375C18 20.2725 17.2725 21 16.375 21L4.625 21ZM19 3.75L8 3.75L8 2.25L19 2.25C20.5183 2.25 21.75 3.4796 21.75 4.99891L21.75 18L20.25 18L20.25 4.99891C20.25 4.30909 19.6909 3.75 19 3.75Z"})}),We=["woocommerce/woocommerce//archive-product","woocommerce/woocommerce//taxonomy-product_cat","woocommerce/woocommerce//taxonomy-product_tag","woocommerce/woocommerce//taxonomy-product_attribute","woocommerce/woocommerce//product-search-results"],Qe=e=>{(0,i.registerBlockVariation)(G,{description:(0,p.__)("A block that displays a selection of products in your store.","woocommerce"),name:V,
/* translators: "Products" is the name of the block. */
title:(0,p.__)("Products (Beta)","woocommerce"),isActive:e=>e.namespace===V,icon:(0,l.jsx)(c.Icon,{icon:Ue,className:"wc-block-editor-components-block-icon wc-block-editor-components-block-icon--stacks"}),attributes:{...e,namespace:V},allowedControls:z,innerBlocks:Q,scope:[]})};let Je;(0,f.subscribe)((()=>{const e=Je,t=(0,f.select)("core/edit-site");if(Je=t?.getEditedPostId(),e!==Je&&D(t)){const e=We.includes(Je),t={inherit:e};e&&(t.perPage=(0,v.getSettingWithCoercion)("loopShopPerPage",12,x.isNumber));const o={...W,query:{...W.query,...t}};(0,i.unregisterBlockVariation)(G,V),Qe(o)}}),"core/edit-site");let Ke=!1;(0,f.subscribe)((()=>{Ke||(Ke=!0,Qe(W))}),"core/edit-post");class Ze{blocks=new Map;initialized=!1;attemptedRegisteredBlocks=new Set;constructor(){this.initializeSubscriptions()}static getInstance(){return Ze.instance||(Ze.instance=new Ze),Ze.instance}parseTemplateId(e){const t=(0,x.isNumber)(e)?void 0:e;return t?.split("//")[1]}initializeSubscriptions(){if(this.initialized)return;const e=(0,f.subscribe)((()=>{const t=(0,f.select)("core/edit-site"),o=(0,f.select)("core/edit-post");if(t||o)if(t){const o=t.getEditedPostId();e(),this.currentTemplateId="string"==typeof o?this.parseTemplateId(o):void 0,(0,f.subscribe)((()=>{const e=this.currentTemplateId;this.currentTemplateId=this.parseTemplateId(t.getEditedPostId()),e!==this.currentTemplateId&&this.handleTemplateChange(e)}),"core/edit-site"),this.initialized=!0}else o&&(e(),this.blocks.forEach((e=>{if(e.isAvailableOnPostEditor){const t=e.variationName||e.blockName;this.hasAttemptedRegistration(t)||this.registerBlock(e)}})),this.initialized=!0)}))}handleTemplateChange(e){(this.currentTemplateId?.includes("single-product")||e?.includes("single-product"))&&this.blocks.forEach((e=>{this.unregisterBlock(e),this.registerBlock(e)}))}hasAttemptedRegistration(e){return this.attemptedRegisteredBlocks.has(e)}unregisterBlock(e){const{blockName:t,isVariationBlock:o,variationName:r}=e;try{o&&r?((0,i.unregisterBlockVariation)(t,r),this.attemptedRegisteredBlocks.delete(r)):((0,i.unregisterBlockType)(t),this.attemptedRegisteredBlocks.delete(t))}catch(e){console.debug(`Failed to unregister block ${t}:`,e)}}registerBlock(e){const{blockName:t,settings:o,isVariationBlock:r,variationName:c,isAvailableOnPostEditor:s}=e;try{const e=c||t;if(this.hasAttemptedRegistration(e))return;const n=(0,f.select)("core/edit-site");if(!n&&!s)return;if(r)(0,i.registerBlockVariation)(t,o);else{const e=(0,x.isEmpty)(o?.ancestor)?["woocommerce/single-product"]:o?.ancestor,r=n&&this.currentTemplateId?.includes("single-product");(0,i.registerBlockType)(t,{...o,ancestor:r?void 0:e})}this.attemptedRegisteredBlocks.add(e)}catch(e){console.error(`Failed to register block ${t}:`,e)}}registerBlockConfig(e){const t=e.variationName||e.blockName;this.blocks.set(t,e),this.registerBlock(e)}}const Xe="woocommerce/related-products",Ye={namespace:Xe,allowedControls:[],displayLayout:{type:"flex",columns:5},query:{perPage:5,pages:0,offset:0,postType:"product",order:"asc",orderBy:"title",author:"",search:"",exclude:[],sticky:"",inherit:!1},lock:{remove:!0,move:!0}},et=(0,v.getSettingWithCoercion)("postTemplateHasSupportForGridView",!1,x.isBoolean),tt=[["core/heading",{level:2,content:(0,p.__)("Related products","woocommerce"),style:{spacing:{margin:{top:"1rem",bottom:"1rem"}}}}],["core/post-template",{__woocommerceNamespace:k,...et&&{layout:{type:"grid",columnCount:5}}},[["woocommerce/product-image",{productId:0,imageSizing:"cropped"}],["core/post-title",{textAlign:"center",level:3,fontSize:"medium",isLink:!0,__woocommerceNamespace:d},[]],["woocommerce/product-price",{textAlign:"center",fontSize:"small",style:{spacing:{margin:{bottom:"1rem"}}}},[]],["woocommerce/product-button",{textAlign:"center",fontSize:"small",style:{spacing:{margin:{bottom:"1rem"}}}},[]]]]];((e,t)=>{const o=e.name;if(!o)return void console.error("registerProductBlockType: Block name is required for registration");const r=(({name:e,...t})=>t)(e),{isVariationBlock:c,variationName:s,isAvailableOnPostEditor:n,...i}={...r,...t||{}},a={blockName:o,settings:{...i},isVariationBlock:null!=c&&c,variationName:null!=s?s:void 0,isAvailableOnPostEditor:null!=n&&n};Ze.getInstance().registerBlockConfig(a)})({name:G,description:(0,p.__)("Display related products.","woocommerce"),title:(0,p.__)("Related Products Controls","woocommerce"),isActive:e=>e.namespace===Xe,icon:(0,l.jsx)(c.Icon,{icon:Ue,className:"wc-block-editor-components-block-icon wc-block-editor-components-block-icon--stacks"}),attributes:Ye,allowedControls:[],innerBlocks:tt,scope:["block"]},{isVariationBlock:!0,variationName:Xe,isAvailableOnPostEditor:!1});const ot=[w,b,m];(0,r.addFilter)("blocks.registerBlockType","core/custom-class-name/attribute",(function(e,t){return ot.includes(t)&&(e.attributes={...e.attributes,__woocommerceNamespace:{type:"string"}}),e}))},1404:()=>{},8286:()=>{},2663:()=>{},1939:()=>{},5022:()=>{},9969:()=>{},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},9491:e=>{"use strict";e.exports=window.wp.compose},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},5573:e=>{"use strict";e.exports=window.wp.primitives}},c={};function s(e){var t=c[e];if(void 0!==t)return t.exports;var o=c[e]={exports:{}};return r[e].call(o.exports,o,o.exports,s),o.exports}s.m=r,e=[],s.O=(t,o,r,c)=>{if(!o){var n=1/0;for(m=0;m<e.length;m++){for(var[o,r,c]=e[m],i=!0,a=0;a<o.length;a++)(!1&c||n>=c)&&Object.keys(s.O).every((e=>s.O[e](o[a])))?o.splice(a--,1):(i=!1,c<n&&(n=c));if(i){e.splice(m--,1);var l=r();void 0!==l&&(t=l)}}return t}c=c||0;for(var m=e.length;m>0&&e[m-1][2]>c;m--)e[m]=e[m-1];e[m]=[o,r,c]},s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,s.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var c=Object.create(null);s.r(c);var n={};t=t||[null,o({}),o([]),o(o)];for(var i=2&r&&e;"object"==typeof i&&!~t.indexOf(i);i=o(i))Object.getOwnPropertyNames(i).forEach((t=>n[t]=()=>e[t]));return n.default=()=>e,s.d(c,n),c},s.d=(e,t)=>{for(var o in t)s.o(t,o)&&!s.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.j=3315,(()=>{var e={3315:0};s.O.j=t=>0===e[t];var t=(t,o)=>{var r,c,[n,i,a]=o,l=0;if(n.some((t=>0!==e[t]))){for(r in i)s.o(i,r)&&(s.m[r]=i[r]);if(a)var m=a(s)}for(t&&t(o);l<n.length;l++)c=n[l],s.o(e,c)&&e[c]&&e[c][0](),e[c]=0;return s.O(m)},o=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var n=s.O(void 0,[94],(()=>s(9293)));n=s.O(n),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["product-query"]=n})();