"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[264],{7240:(t,e,o)=>{o.r(e),o.d(e,{default:()=>u});var s=o(8331),n=o(9874),l=o(371),c=o(4921);const a=(0,o(7723).__)("Start shopping","woocommerce");var r=o(2805),i=o(790);const u=({className:t,startShoppingButtonLabel:e,style:o,textColor:u,backgroundColor:d})=>{const k=(0,l.p)({style:o,textColor:u,backgroundColor:d});return s.Jn?(0,i.jsx)("div",{className:"wp-block-button has-text-align-center",children:(0,i.jsx)(n.A,{className:(0,c.A)(t,k.className,"wc-block-mini-cart__shopping-button"),style:k.style,variant:(0,r.I)(t,"contained"),href:s.Jn,children:e||a})}):null}},2805:(t,e,o)=>{o.d(e,{G:()=>l,I:()=>n});var s=o(3993);const n=(t="",e)=>t.includes("is-style-outline")?"outlined":t.includes("is-style-fill")?"contained":e,l=t=>t.some((t=>Array.isArray(t)?l(t):(0,s.isObject)(t)&&null!==t.key))}}]);