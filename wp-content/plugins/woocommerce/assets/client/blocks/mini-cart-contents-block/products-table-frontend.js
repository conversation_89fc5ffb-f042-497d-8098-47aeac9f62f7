"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[88],{5842:(c,s,a)=>{a.r(s),a.d(s,{default:()=>l});var t=a(5460),e=a(5058),n=a(4921),o=a(790);const l=({className:c})=>{const{cartItems:s,cartIsLoading:a}=(0,t.V)();return(0,o.jsx)("div",{className:(0,n.A)(c,"wc-block-mini-cart__products-table"),children:(0,o.jsx)(e.A,{lineItems:s,isLoading:a,className:"wc-block-mini-cart-items"})})}}}]);