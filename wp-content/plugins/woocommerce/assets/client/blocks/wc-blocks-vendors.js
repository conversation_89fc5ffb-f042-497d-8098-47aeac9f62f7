(globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[]).push([[94],{9044:(e,t,n)=>{"use strict";n.d(t,{A:()=>x});var r=n(2509),o=n(6087),i=n(7697),s=n.n(i),a=n(8468),c=n(4040),l=n.n(c),u=n(9491),d=n(7276),f=n(8596);const p=(0,o.createElement)("div",{className:"event-catcher"}),m=({eventHandlers:e,child:t,childrenWithPopover:n})=>(0,o.cloneElement)((0,o.createElement)("span",{className:"disabled-element-wrapper"},(0,o.cloneElement)(p,e),(0,o.cloneElement)(t,{children:n}),","),e),h=({child:e,eventHandlers:t,childrenWithPopover:n})=>(0,o.cloneElement)(e,{...t,children:n}),v=(e,t,n)=>{if(1!==o.Children.count(e))return;const r=o.Children.only(e);"function"==typeof r.props[t]&&r.props[t](n)},g=function({children:e,position:t,text:n,shortcut:r}){const[i,s]=(0,o.useState)(!1),[c,l]=(0,o.useState)(!1),p=(0,u.useDebounce)(l,700),g=t=>{v(e,"onMouseDown",t),document.addEventListener("mouseup",b),s(!0)},w=t=>{v(e,"onMouseUp",t),document.removeEventListener("mouseup",b),s(!1)},y=e=>"mouseUp"===e?w:"mouseDown"===e?g:void 0,b=y("mouseUp"),E=(t,n)=>r=>{if(v(e,t,r),r.currentTarget.disabled)return;if("focus"===r.type&&i)return;p.cancel();const o=(0,a.includes)(["focus","mouseenter"],r.type);o!==c&&(n?p(o):l(o))},C=()=>{p.cancel(),document.removeEventListener("mouseup",b)};if((0,o.useEffect)((()=>C),[]),1!==o.Children.count(e))return e;const x={onMouseEnter:E("onMouseEnter",!0),onMouseLeave:E("onMouseLeave"),onClick:E("onClick"),onFocus:E("onFocus"),onBlur:E("onBlur"),onMouseDown:y("mouseDown")},A=o.Children.only(e),{children:S,disabled:M}=A.props,T=M?m:h,k=(({grandchildren:e,isOver:t,position:n,text:r,shortcut:i})=>(0,o.concatChildren)(e,t&&(0,o.createElement)(d.A,{focusOnMount:!1,position:n,className:"components-tooltip","aria-hidden":"true",animate:!1,noArrow:!0},r,(0,o.createElement)(f.A,{className:"components-tooltip__shortcut",shortcut:i}))))({grandchildren:S,isOver:c,position:t,text:n,shortcut:r});return T({child:A,eventHandlers:x,childrenWithPopover:k})};var w=n(5573),y=n(1011);const b=function({icon:e=null,size:t=24,...n}){if("string"==typeof e)return(0,o.createElement)(y.A,(0,r.A)({icon:e},n));if((0,o.isValidElement)(e)&&y.A===e.type)return(0,o.cloneElement)(e,{...n});if("function"==typeof e)return e.prototype instanceof o.Component?(0,o.createElement)(e,{size:t,...n}):e({size:t,...n});if(e&&("svg"===e.type||e.type===w.SVG)){const r={width:t,height:t,...e.props,...n};return(0,o.createElement)(w.SVG,r)}return(0,o.isValidElement)(e)?(0,o.cloneElement)(e,{size:t,...n}):e};var E=n(606);const C=["onMouseDown","onClick"],x=(0,o.forwardRef)((function(e,t){const{href:n,target:i,isSmall:c,isPressed:u,isBusy:d,isDestructive:f,className:p,disabled:m,icon:h,iconPosition:v="left",iconSize:w,showTooltip:y,tooltipPosition:x,shortcut:A,label:S,children:M,text:T,variant:k,__experimentalIsFocusable:L,describedBy:V,...z}=function({isDefault:e,isPrimary:t,isSecondary:n,isTertiary:r,isLink:o,variant:i,...s}){let a=i;var c,u,d,f,p;return t&&(null!==(c=a)&&void 0!==c||(a="primary")),r&&(null!==(u=a)&&void 0!==u||(a="tertiary")),n&&(null!==(d=a)&&void 0!==d||(a="secondary")),e&&(l()("Button isDefault prop",{since:"5.4",alternative:'variant="secondary"'}),null!==(f=a)&&void 0!==f||(a="secondary")),o&&(null!==(p=a)&&void 0!==p||(a="link")),{...s,variant:a}}(e),O=s()("components-button",p,{"is-secondary":"secondary"===k,"is-primary":"primary"===k,"is-small":c,"is-tertiary":"tertiary"===k,"is-pressed":u,"is-busy":d,"is-link":"link"===k,"is-destructive":f,"has-text":!!h&&!!M,"has-icon":!!h}),F=m&&!L,R=void 0===n||F?"button":"a",N="a"===R?{href:n,target:i}:{type:"button",disabled:F,"aria-pressed":u};if(m&&L){N["aria-disabled"]=!0;for(const e of C)z[e]=e=>{e.stopPropagation(),e.preventDefault()}}const _=!F&&(y&&S||A||!!S&&(!M||(0,a.isArray)(M)&&!M.length)&&!1!==y),H=V?(0,a.uniqueId)():null,I=z["aria-describedby"]||H,P=(0,o.createElement)(R,(0,r.A)({},N,z,{className:O,"aria-label":z["aria-label"]||S,"aria-describedby":I,ref:t}),h&&"left"===v&&(0,o.createElement)(b,{icon:h,size:w}),T&&(0,o.createElement)(o.Fragment,null,T),h&&"right"===v&&(0,o.createElement)(b,{icon:h,size:w}),M);return _?(0,o.createElement)(o.Fragment,null,(0,o.createElement)(g,{text:V||S,shortcut:A,position:x},P),V&&(0,o.createElement)(E.A,null,(0,o.createElement)("span",{id:H},V))):(0,o.createElement)(o.Fragment,null,P,V&&(0,o.createElement)(E.A,null,(0,o.createElement)("span",{id:H},V)))}))},1011:(e,t,n)=>{"use strict";if(n.d(t,{A:()=>i}),/^(2432|5454|8915)$/.test(n.j))var r=n(2509);var o=n(6087);const i=/^(2432|5454|8915)$/.test(n.j)?function({icon:e,className:t,...n}){const i=["dashicon","dashicons","dashicons-"+e,t].filter(Boolean).join(" ");return(0,o.createElement)("span",(0,r.A)({className:i},n))}:null},4642:(e,t,n)=>{"use strict";n.d(t,{A:()=>T});var r=n(6087),o=n(8468),i=n(7697),s=n.n(i),a=n(7723),c=n(9491),l=n(8558),u=n(923),d=n.n(u),f=n(5521),p=n(9044),m=n(606);function h({value:e,status:t,title:n,displayTransform:i,isBorderless:l=!1,disabled:u=!1,onClickRemove:d=o.noop,onMouseEnter:v,onMouseLeave:g,messages:w,termPosition:y,termsCount:b}){const E=(0,c.useInstanceId)(h),C=s()("components-form-token-field__token",{"is-error":"error"===t,"is-success":"success"===t,"is-validating":"validating"===t,"is-borderless":l,"is-disabled":u}),x=i(e),A=(0,a.sprintf)(
/* translators: 1: term name, 2: term position in a set of terms, 3: total term set count. */
/* translators: 1: term name, 2: term position in a set of terms, 3: total term set count. */
(0,a.__)("%1$s (%2$s of %3$s)"),x,y,b);return(0,r.createElement)("span",{className:C,onMouseEnter:v,onMouseLeave:g,title:n},(0,r.createElement)("span",{className:"components-form-token-field__token-text",id:`components-form-token-field__token-text-${E}`},(0,r.createElement)(m.A,{as:"span"},A),(0,r.createElement)("span",{"aria-hidden":"true"},x)),(0,r.createElement)(p.A,{className:"components-form-token-field__remove-token",icon:f.A,onClick:!u&&(()=>d({value:e})),label:w.remove,"aria-describedby":`components-form-token-field__token-text-${E}`}))}var v=n(2509);class g extends r.Component{constructor(){super(...arguments),this.onChange=this.onChange.bind(this),this.bindInput=this.bindInput.bind(this)}focus(){this.input.focus()}hasFocus(){return this.input===this.input.ownerDocument.activeElement}bindInput(e){this.input=e}onChange(e){this.props.onChange({value:e.target.value})}render(){const{value:e,isExpanded:t,instanceId:n,selectedSuggestionIndex:o,className:i,...a}=this.props,c=e?e.length+1:0;return(0,r.createElement)("input",(0,v.A)({ref:this.bindInput,id:`components-form-token-input-${n}`,type:"text"},a,{value:e||"",onChange:this.onChange,size:c,className:s()(i,"components-form-token-field__input"),autoComplete:"off",role:"combobox","aria-expanded":t,"aria-autocomplete":"list","aria-owns":t?`components-form-token-suggestions-${n}`:void 0,"aria-activedescendant":-1!==o?`components-form-token-suggestions-${n}-${o}`:void 0,"aria-describedby":`components-form-token-suggestions-howto-${n}`}))}}const w=g;var y=n(6406),b=n.n(y);class E extends r.Component{constructor(){super(...arguments),this.handleMouseDown=this.handleMouseDown.bind(this),this.bindList=this.bindList.bind(this)}componentDidUpdate(){this.props.selectedIndex>-1&&this.props.scrollIntoView&&this.list.children[this.props.selectedIndex]&&(this.scrollingIntoView=!0,b()(this.list.children[this.props.selectedIndex],this.list,{onlyScrollIfNeeded:!0}),this.props.setTimeout((()=>{this.scrollingIntoView=!1}),100))}bindList(e){this.list=e}handleHover(e){return()=>{this.scrollingIntoView||this.props.onHover(e)}}handleClick(e){return()=>{this.props.onSelect(e)}}handleMouseDown(e){e.preventDefault()}computeSuggestionMatch(e){const t=this.props.displayTransform(this.props.match||"").toLocaleLowerCase();if(0===t.length)return null;const n=(e=this.props.displayTransform(e)).toLocaleLowerCase().indexOf(t);return{suggestionBeforeMatch:e.substring(0,n),suggestionMatch:e.substring(n,n+t.length),suggestionAfterMatch:e.substring(n+t.length)}}render(){return(0,r.createElement)("ul",{ref:this.bindList,className:"components-form-token-field__suggestions-list",id:`components-form-token-suggestions-${this.props.instanceId}`,role:"listbox"},(0,o.map)(this.props.suggestions,((e,t)=>{const n=this.computeSuggestionMatch(e),o=s()("components-form-token-field__suggestion",{"is-selected":t===this.props.selectedIndex});return(0,r.createElement)("li",{id:`components-form-token-suggestions-${this.props.instanceId}-${t}`,role:"option",className:o,key:null!=e&&e.value?e.value:this.props.displayTransform(e),onMouseDown:this.handleMouseDown,onClick:this.handleClick(e),onMouseEnter:this.handleHover(e),"aria-selected":t===this.props.selectedIndex},n?(0,r.createElement)("span",{"aria-label":this.props.displayTransform(e)},n.suggestionBeforeMatch,(0,r.createElement)("strong",{className:"components-form-token-field__suggestion-match"},n.suggestionMatch),n.suggestionAfterMatch):this.props.displayTransform(e))})))}}E.defaultProps={match:"",onHover:()=>{},onSelect:()=>{},suggestions:Object.freeze([])};const C=(0,c.withSafeTimeout)(E);var x=n(195);const A=(0,c.createHigherOrderComponent)((e=>t=>(0,r.createElement)(e,(0,v.A)({},t,{speak:x.speak,debouncedSpeak:(0,c.useDebounce)(x.speak,500)}))),"withSpokenMessages"),S={incompleteTokenValue:"",inputOffsetFromEnd:0,isActive:!1,isExpanded:!1,selectedSuggestionIndex:-1,selectedSuggestionScroll:!1};class M extends r.Component{constructor(){super(...arguments),this.state=S,this.onKeyDown=this.onKeyDown.bind(this),this.onKeyPress=this.onKeyPress.bind(this),this.onFocus=this.onFocus.bind(this),this.onBlur=this.onBlur.bind(this),this.deleteTokenBeforeInput=this.deleteTokenBeforeInput.bind(this),this.deleteTokenAfterInput=this.deleteTokenAfterInput.bind(this),this.addCurrentToken=this.addCurrentToken.bind(this),this.onContainerTouched=this.onContainerTouched.bind(this),this.renderToken=this.renderToken.bind(this),this.onTokenClickRemove=this.onTokenClickRemove.bind(this),this.onSuggestionHovered=this.onSuggestionHovered.bind(this),this.onSuggestionSelected=this.onSuggestionSelected.bind(this),this.onInputChange=this.onInputChange.bind(this),this.bindInput=this.bindInput.bind(this),this.bindTokensAndInput=this.bindTokensAndInput.bind(this),this.updateSuggestions=this.updateSuggestions.bind(this)}componentDidUpdate(e){this.state.isActive&&!this.input.hasFocus()&&this.input.focus();const{suggestions:t,value:n}=this.props,r=!d()(t,e.suggestions);(r||n!==e.value)&&this.updateSuggestions(r)}static getDerivedStateFromProps(e,t){return e.disabled&&t.isActive?{isActive:!1,incompleteTokenValue:""}:null}bindInput(e){this.input=e}bindTokensAndInput(e){this.tokensAndInput=e}onFocus(e){const{__experimentalExpandOnFocus:t}=this.props;this.input.hasFocus()||e.target===this.tokensAndInput?this.setState({isActive:!0,isExpanded:!!t||this.state.isExpanded}):this.setState({isActive:!1}),"function"==typeof this.props.onFocus&&this.props.onFocus(e)}onBlur(){this.inputHasValidValue()?this.setState({isActive:!1}):this.setState(S)}onKeyDown(e){let t=!1;switch(e.keyCode){case l.BACKSPACE:t=this.handleDeleteKey(this.deleteTokenBeforeInput);break;case l.ENTER:t=this.addCurrentToken();break;case l.LEFT:t=this.handleLeftArrowKey();break;case l.UP:t=this.handleUpArrowKey();break;case l.RIGHT:t=this.handleRightArrowKey();break;case l.DOWN:t=this.handleDownArrowKey();break;case l.DELETE:t=this.handleDeleteKey(this.deleteTokenAfterInput);break;case l.SPACE:this.props.tokenizeOnSpace&&(t=this.addCurrentToken());break;case l.ESCAPE:t=this.handleEscapeKey(e),e.stopPropagation()}t&&e.preventDefault()}onKeyPress(e){let t=!1;44===e.charCode&&(t=this.handleCommaKey()),t&&e.preventDefault()}onContainerTouched(e){e.target===this.tokensAndInput&&this.state.isActive&&e.preventDefault()}onTokenClickRemove(e){this.deleteToken(e.value),this.input.focus()}onSuggestionHovered(e){const t=this.getMatchingSuggestions().indexOf(e);t>=0&&this.setState({selectedSuggestionIndex:t,selectedSuggestionScroll:!1})}onSuggestionSelected(e){this.addNewToken(e)}onInputChange(e){const t=e.value,n=this.props.tokenizeOnSpace?/[ ,\t]+/:/[,\t]+/,r=t.split(n),i=(0,o.last)(r)||"";r.length>1&&this.addNewTokens(r.slice(0,-1)),this.setState({incompleteTokenValue:i},this.updateSuggestions),this.props.onInputChange(i)}handleDeleteKey(e){let t=!1;return this.input.hasFocus()&&this.isInputEmpty()&&(e(),t=!0),t}handleLeftArrowKey(){let e=!1;return this.isInputEmpty()&&(this.moveInputBeforePreviousToken(),e=!0),e}handleRightArrowKey(){let e=!1;return this.isInputEmpty()&&(this.moveInputAfterNextToken(),e=!0),e}handleUpArrowKey(){return this.setState(((e,t)=>({selectedSuggestionIndex:(0===e.selectedSuggestionIndex?this.getMatchingSuggestions(e.incompleteTokenValue,t.suggestions,t.value,t.maxSuggestions,t.saveTransform).length:e.selectedSuggestionIndex)-1,selectedSuggestionScroll:!0}))),!0}handleDownArrowKey(){return this.setState(((e,t)=>({selectedSuggestionIndex:(e.selectedSuggestionIndex+1)%this.getMatchingSuggestions(e.incompleteTokenValue,t.suggestions,t.value,t.maxSuggestions,t.saveTransform).length,selectedSuggestionScroll:!0}))),!0}handleEscapeKey(e){return this.setState({incompleteTokenValue:e.target.value,isExpanded:!1,selectedSuggestionIndex:-1,selectedSuggestionScroll:!1}),!0}handleCommaKey(){return this.inputHasValidValue()&&this.addNewToken(this.state.incompleteTokenValue),!0}moveInputToIndex(e){this.setState(((t,n)=>({inputOffsetFromEnd:n.value.length-Math.max(e,-1)-1})))}moveInputBeforePreviousToken(){this.setState(((e,t)=>({inputOffsetFromEnd:Math.min(e.inputOffsetFromEnd+1,t.value.length)})))}moveInputAfterNextToken(){this.setState((e=>({inputOffsetFromEnd:Math.max(e.inputOffsetFromEnd-1,0)})))}deleteTokenBeforeInput(){const e=this.getIndexOfInput()-1;e>-1&&this.deleteToken(this.props.value[e])}deleteTokenAfterInput(){const e=this.getIndexOfInput();e<this.props.value.length&&(this.deleteToken(this.props.value[e]),this.moveInputToIndex(e))}addCurrentToken(){let e=!1;const t=this.getSelectedSuggestion();return t?(this.addNewToken(t),e=!0):this.inputHasValidValue()&&(this.addNewToken(this.state.incompleteTokenValue),e=!0),e}addNewTokens(e){const t=(0,o.uniq)(e.map(this.props.saveTransform).filter(Boolean).filter((e=>!this.valueContainsToken(e))));if(t.length>0){const e=(0,o.clone)(this.props.value);e.splice.apply(e,[this.getIndexOfInput(),0].concat(t)),this.props.onChange(e)}}addNewToken(e){const{__experimentalExpandOnFocus:t,__experimentalValidateInput:n}=this.props;n(e)?(this.addNewTokens([e]),this.props.speak(this.props.messages.added,"assertive"),this.setState({incompleteTokenValue:"",selectedSuggestionIndex:-1,selectedSuggestionScroll:!1,isExpanded:!t}),this.state.isActive&&this.input.focus()):this.props.speak(this.props.messages.__experimentalInvalid,"assertive")}deleteToken(e){const t=this.props.value.filter((t=>this.getTokenValue(t)!==this.getTokenValue(e)));this.props.onChange(t),this.props.speak(this.props.messages.removed,"assertive")}getTokenValue(e){return"object"==typeof e?e.value:e}getMatchingSuggestions(e=this.state.incompleteTokenValue,t=this.props.suggestions,n=this.props.value,r=this.props.maxSuggestions,i=this.props.saveTransform){let s=i(e);const a=[],c=[];return 0===s.length?t=(0,o.difference)(t,n):(s=s.toLocaleLowerCase(),(0,o.each)(t,(e=>{const t=e.toLocaleLowerCase().indexOf(s);-1===n.indexOf(e)&&(0===t?a.push(e):t>0&&c.push(e))})),t=a.concat(c)),(0,o.take)(t,r)}getSelectedSuggestion(){if(-1!==this.state.selectedSuggestionIndex)return this.getMatchingSuggestions()[this.state.selectedSuggestionIndex]}valueContainsToken(e){return(0,o.some)(this.props.value,(t=>this.getTokenValue(e)===this.getTokenValue(t)))}getIndexOfInput(){return this.props.value.length-this.state.inputOffsetFromEnd}isInputEmpty(){return 0===this.state.incompleteTokenValue.length}inputHasValidValue(){return this.props.saveTransform(this.state.incompleteTokenValue).length>0}updateSuggestions(e=!0){const{__experimentalExpandOnFocus:t}=this.props,{incompleteTokenValue:n}=this.state,r=n.trim().length>1,o=this.getMatchingSuggestions(n),i=o.length>0,s={isExpanded:t||r&&i};if(e&&(s.selectedSuggestionIndex=-1,s.selectedSuggestionScroll=!1),this.setState(s),r){const{debouncedSpeak:e}=this.props;e(i?(0,a.sprintf)(
/* translators: %d: number of results. */
/* translators: %d: number of results. */
(0,a._n)("%d result found, use up and down arrow keys to navigate.","%d results found, use up and down arrow keys to navigate.",o.length),o.length):(0,a.__)("No results."),"assertive")}}renderTokensAndInput(){const e=(0,o.map)(this.props.value,this.renderToken);return e.splice(this.getIndexOfInput(),0,this.renderInput()),e}renderToken(e,t,n){const o=this.getTokenValue(e),i=e.status?e.status:void 0,s=t+1,a=n.length;return(0,r.createElement)(h,{key:"token-"+o,value:o,status:i,title:e.title,displayTransform:this.props.displayTransform,onClickRemove:this.onTokenClickRemove,isBorderless:e.isBorderless||this.props.isBorderless,onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,disabled:"error"!==i&&this.props.disabled,messages:this.props.messages,termsCount:a,termPosition:s})}renderInput(){const{autoCapitalize:e,autoComplete:t,maxLength:n,placeholder:o,value:i,instanceId:s}=this.props;let a={instanceId:s,autoCapitalize:e,autoComplete:t,placeholder:0===i.length?o:"",ref:this.bindInput,key:"input",disabled:this.props.disabled,value:this.state.incompleteTokenValue,onBlur:this.onBlur,isExpanded:this.state.isExpanded,selectedSuggestionIndex:this.state.selectedSuggestionIndex};return n&&i.length>=n||(a={...a,onChange:this.onInputChange}),(0,r.createElement)(w,a)}render(){const{disabled:e,label:t=(0,a.__)("Add item"),instanceId:n,className:o,__experimentalShowHowTo:i}=this.props,{isExpanded:c}=this.state,l=s()(o,"components-form-token-field__input-container",{"is-active":this.state.isActive,"is-disabled":e});let u={className:"components-form-token-field",tabIndex:"-1"};const d=this.getMatchingSuggestions();return e||(u=Object.assign({},u,{onKeyDown:this.onKeyDown,onKeyPress:this.onKeyPress,onFocus:this.onFocus})),(0,r.createElement)("div",u,(0,r.createElement)("label",{htmlFor:`components-form-token-input-${n}`,className:"components-form-token-field__label"},t),(0,r.createElement)("div",{ref:this.bindTokensAndInput,className:l,tabIndex:"-1",onMouseDown:this.onContainerTouched,onTouchStart:this.onContainerTouched},this.renderTokensAndInput(),c&&(0,r.createElement)(C,{instanceId:n,match:this.props.saveTransform(this.state.incompleteTokenValue),displayTransform:this.props.displayTransform,suggestions:d,selectedIndex:this.state.selectedSuggestionIndex,scrollIntoView:this.state.selectedSuggestionScroll,onHover:this.onSuggestionHovered,onSelect:this.onSuggestionSelected})),i&&(0,r.createElement)("p",{id:`components-form-token-suggestions-howto-${n}`,className:"components-form-token-field__help"},this.props.tokenizeOnSpace?(0,a.__)("Separate with commas, spaces, or the Enter key."):(0,a.__)("Separate with commas or the Enter key.")))}}M.defaultProps={suggestions:Object.freeze([]),maxSuggestions:100,value:Object.freeze([]),displayTransform:o.identity,saveTransform:e=>e.trim(),onChange:()=>{},onInputChange:()=>{},isBorderless:!1,disabled:!1,tokenizeOnSpace:!1,messages:{added:(0,a.__)("Item added."),removed:(0,a.__)("Item removed."),remove:(0,a.__)("Remove item"),__experimentalInvalid:(0,a.__)("Invalid item")},__experimentalExpandOnFocus:!1,__experimentalValidateInput:()=>!0,__experimentalShowHowTo:!0};const T=A((0,c.withInstanceId)(M))},7276:(e,t,n)=>{"use strict";n.d(t,{A:()=>I});var r=n(2509),o=n(6087),i=n(7697),s=n.n(i),a=n(8107),c=n(4040),l=n.n(c),u=n(9491),d=n(5573);const f=(0,o.createElement)(d.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,o.createElement)(d.Path,{d:"M13 11.8l6.1-6.3-1-1-6.1 6.2-6.1-6.2-1 1 6.1 6.3-6.5 6.7 1 1 6.5-6.6 6.5 6.6 1-1z"}));var p=n(7723);function m(e,t,n){const{defaultView:r}=t,{frameElement:o}=r;if(!o||t===n.ownerDocument)return e;const i=o.getBoundingClientRect();return new r.DOMRect(e.left+i.left,e.top+i.top,e.width,e.height)}var h=n(9044);let v=0;function g(e){const t=document.scrollingElement||document.body;e&&(v=t.scrollTop);const n=e?"add":"remove";t.classList[n]("lockscroll"),document.documentElement.classList[n]("lockscroll"),e||(t.scrollTop=v)}let w=0;function y(){return(0,o.useEffect)((()=>(0===w&&g(!0),++w,()=>{1===w&&g(!1),--w})),[]),null}var b=n(5947),E=n(5291),C=n(8468),x=n(4855);class A extends o.Component{constructor(){super(...arguments),this.isUnmounted=!1,this.bindNode=this.bindNode.bind(this)}componentDidMount(){const{registerSlot:e}=this.props;e(this.props.name,this)}componentWillUnmount(){const{unregisterSlot:e}=this.props;this.isUnmounted=!0,e(this.props.name,this)}componentDidUpdate(e){const{name:t,unregisterSlot:n,registerSlot:r}=this.props;e.name!==t&&(n(e.name),r(t,this))}bindNode(e){this.node=e}forceUpdate(){this.isUnmounted||super.forceUpdate()}render(){const{children:e,name:t,fillProps:n={},getFills:r}=this.props,i=(0,C.map)(r(t,this),(e=>{const t=(0,C.isFunction)(e.children)?e.children(n):e.children;return o.Children.map(t,((e,t)=>{if(!e||(0,C.isString)(e))return e;const n=e.key||t;return(0,o.cloneElement)(e,{key:n})}))})).filter((0,C.negate)(o.isEmptyElement));return(0,o.createElement)(o.Fragment,null,(0,C.isFunction)(e)?e(i):i)}}const S=e=>(0,o.createElement)(x.A.Consumer,null,(({registerSlot:t,unregisterSlot:n,getFills:i})=>(0,o.createElement)(A,(0,r.A)({},e,{registerSlot:t,unregisterSlot:n,getFills:i}))));var M=n(9364),T=n(8493);const k=(0,o.forwardRef)((function({name:e,fillProps:t={},as:n="div",...i},s){const a=(0,o.useContext)(T.A),c=(0,o.useRef)();return(0,o.useLayoutEffect)((()=>(a.registerSlot(e,c,t),()=>{a.unregisterSlot(e,c)})),[a.registerSlot,a.unregisterSlot,e]),(0,o.useLayoutEffect)((()=>{a.updateSlot(e,t)})),(0,o.createElement)(n,(0,r.A)({ref:(0,u.useMergeRefs)([s,c])},i))}));function L(e){return(0,o.createElement)(o.Fragment,null,(0,o.createElement)(E.A,e),(0,o.createElement)(M.A,e))}const V=(0,o.forwardRef)((({bubblesVirtually:e,...t},n)=>e?(0,o.createElement)(k,(0,r.A)({},t,{ref:n})):(0,o.createElement)(S,t)));function z(e){return"appear"===e?"top":"left"}const O="Popover";function F(e,t){const{paddingTop:n,paddingBottom:r,paddingLeft:o,paddingRight:i}=(s=t).ownerDocument.defaultView.getComputedStyle(s);var s;const a=n?parseInt(n,10):0,c=r?parseInt(r,10):0,l=o?parseInt(o,10):0,u=i?parseInt(i,10):0;return{x:e.left+l,y:e.top+a,width:e.width-l-u,height:e.height-a-c,left:e.left+l,right:e.right-u,top:e.top+a,bottom:e.bottom-c}}function R(e,t,n){n?e.getAttribute(t)!==n&&e.setAttribute(t,n):e.hasAttribute(t)&&e.removeAttribute(t)}function N(e,t,n=""){e.style[t]!==n&&(e.style[t]=n)}function _(e,t,n){n?e.classList.contains(t)||e.classList.add(t):e.classList.contains(t)&&e.classList.remove(t)}const H=(0,o.forwardRef)((({headerTitle:e,onClose:t,children:n,className:i,noArrow:c=!0,isAlternate:d,position:v="bottom right",range:g,focusOnMount:w="firstElement",anchorRef:E,shouldAnchorIncludePadding:C,anchorRect:x,getAnchorRect:A,expandOnMobile:S,animate:M=!0,onClickOutside:T,onFocusOutside:k,__unstableStickyBoundaryElement:V,__unstableSlotName:H=O,__unstableObserveElement:I,__unstableBoundaryParent:P,__unstableForcePosition:j,__unstableForceXAlignment:D,...B},$)=>{const G=(0,o.useRef)(null),W=(0,o.useRef)(null),U=(0,o.useRef)(),Z=(0,u.useViewportMatch)("medium","<"),[K,q]=(0,o.useState)(),Y=(0,b.A)(H),J=S&&Z,[X,Q]=(0,u.useResizeObserver)();c=J||c,(0,o.useLayoutEffect)((()=>{if(J)return _(U.current,"is-without-arrow",c),_(U.current,"is-alternate",d),R(U.current,"data-x-axis"),R(U.current,"data-y-axis"),N(U.current,"top"),N(U.current,"left"),N(W.current,"maxHeight"),void N(W.current,"maxWidth");const e=()=>{if(!U.current||!W.current)return;let e=function(e,t,n,r=!1,o,i){if(t)return t;if(n){if(!e.current)return;const t=n(e.current);return m(t,t.ownerDocument||e.current.ownerDocument,i)}if(!1!==r){if(!(r&&window.Range&&window.Element&&window.DOMRect))return;if("function"==typeof(null==r?void 0:r.cloneRange))return m((0,a.getRectangleFromRange)(r),r.endContainer.ownerDocument,i);if("function"==typeof(null==r?void 0:r.getBoundingClientRect)){const e=m(r.getBoundingClientRect(),r.ownerDocument,i);return o?e:F(e,r)}const{top:e,bottom:t}=r,n=e.getBoundingClientRect(),s=t.getBoundingClientRect(),c=m(new window.DOMRect(n.left,n.top,n.width,s.bottom-n.top),e.ownerDocument,i);return o?c:F(c,r)}if(!e.current)return;const{parentNode:s}=e.current,c=s.getBoundingClientRect();return o?c:F(c,s)}(G,x,A,E,C,U.current);if(!e)return;const{offsetParent:t,ownerDocument:n}=U.current;let r,o=0;if(t&&t!==n.body){const n=t.getBoundingClientRect();o=n.top,e=new window.DOMRect(e.left-n.left,e.top-n.top,e.width,e.height)}var i;P&&(r=null===(i=U.current.closest(".popover-slot"))||void 0===i?void 0:i.parentNode);const s=Q.height?Q:W.current.getBoundingClientRect(),{popoverTop:l,popoverLeft:u,xAxis:f,yAxis:h,contentHeight:g,contentWidth:w}=function(e,t,n="top",r,o,i,s,a,c){const[l,u="center",d]=n.split(" "),f=function(e,t,n,r,o,i,s,a){const{height:c}=t;if(o){const t=o.getBoundingClientRect().top+c-s;if(e.top<=t)return{yAxis:n,popoverTop:Math.min(e.bottom,t)}}let l=e.top+e.height/2;"bottom"===r?l=e.bottom:"top"===r&&(l=e.top);const u={popoverTop:l,contentHeight:(l-c/2>0?c/2:l)+(l+c/2>window.innerHeight?window.innerHeight-l:c/2)},d={popoverTop:e.top,contentHeight:e.top-10-c>0?c:e.top-10},f={popoverTop:e.bottom,contentHeight:e.bottom+10+c>window.innerHeight?window.innerHeight-10-e.bottom:c};let p,m=n,h=null;if(!o&&!a)if("middle"===n&&u.contentHeight===c)m="middle";else if("top"===n&&d.contentHeight===c)m="top";else if("bottom"===n&&f.contentHeight===c)m="bottom";else{m=d.contentHeight>f.contentHeight?"top":"bottom";const e="top"===m?d.contentHeight:f.contentHeight;h=e!==c?e:null}return p="middle"===m?u.popoverTop:"top"===m?d.popoverTop:f.popoverTop,{yAxis:m,popoverTop:p,contentHeight:h}}(e,t,l,d,r,0,i,a),m=function(e,t,n,r,o,i,s,a,c){const{width:l}=t;"left"===n&&(0,p.isRTL)()?n="right":"right"===n&&(0,p.isRTL)()&&(n="left"),"left"===r&&(0,p.isRTL)()?r="right":"right"===r&&(0,p.isRTL)()&&(r="left");const u=Math.round(e.left+e.width/2),d={popoverLeft:u,contentWidth:(u-l/2>0?l/2:u)+(u+l/2>window.innerWidth?window.innerWidth-u:l/2)};let f=e.left;"right"===r?f=e.right:"middle"===i||c||(f=u);let m=e.right;"left"===r?m=e.left:"middle"===i||c||(m=u);const h={popoverLeft:f,contentWidth:f-l>0?l:f},v={popoverLeft:m,contentWidth:m+l>window.innerWidth?window.innerWidth-m:l};let g,w=n,y=null;if(!o&&!a)if("center"===n&&d.contentWidth===l)w="center";else if("left"===n&&h.contentWidth===l)w="left";else if("right"===n&&v.contentWidth===l)w="right";else{w=h.contentWidth>v.contentWidth?"left":"right";const e="left"===w?h.contentWidth:v.contentWidth;l>window.innerWidth&&(y=window.innerWidth),e!==l&&(w="center",d.popoverLeft=window.innerWidth/2)}if(g="center"===w?d.popoverLeft:"left"===w?h.popoverLeft:v.popoverLeft,s){const e=s.getBoundingClientRect();g=Math.min(g,e.right-l),(0,p.isRTL)()||(g=Math.max(g,0))}return{xAxis:w,popoverLeft:g,contentWidth:y}}(e,t,u,d,r,f.yAxis,s,a,c);return{...m,...f}}(e,s,v,V,U.current,o,r,j,D);"number"==typeof l&&"number"==typeof u&&(N(U.current,"top",l+"px"),N(U.current,"left",u+"px")),_(U.current,"is-without-arrow",c||"center"===f&&"middle"===h),_(U.current,"is-alternate",d),R(U.current,"data-x-axis",f),R(U.current,"data-y-axis",h),N(W.current,"maxHeight","number"==typeof g?g+"px":""),N(W.current,"maxWidth","number"==typeof w?w+"px":""),q(({left:"right",right:"left"}[f]||"center")+" "+({top:"bottom",bottom:"top"}[h]||"middle"))};e();const{ownerDocument:t}=U.current,{defaultView:n}=t,r=n.setInterval(e,500);let o;const i=()=>{n.cancelAnimationFrame(o),o=n.requestAnimationFrame(e)};n.addEventListener("click",i),n.addEventListener("resize",e),n.addEventListener("scroll",e,!0);const s=function(e){if(e)return e.endContainer?e.endContainer.ownerDocument:e.top?e.top.ownerDocument:e.ownerDocument}(E);let l;return s&&s!==t&&(s.defaultView.addEventListener("resize",e),s.defaultView.addEventListener("scroll",e,!0)),I&&(l=new n.MutationObserver(e),l.observe(I,{attributes:!0})),()=>{n.clearInterval(r),n.removeEventListener("resize",e),n.removeEventListener("scroll",e,!0),n.removeEventListener("click",i),n.cancelAnimationFrame(o),s&&s!==t&&(s.defaultView.removeEventListener("resize",e),s.defaultView.removeEventListener("scroll",e,!0)),l&&l.disconnect()}}),[J,x,A,E,C,v,Q,V,I,P]);const ee=(e,n)=>{if("focus-outside"===e&&k)k(n);else if("focus-outside"===e&&T){const e=new window.MouseEvent("click");Object.defineProperty(e,"target",{get:()=>n.relatedTarget}),l()("Popover onClickOutside prop",{since:"5.3",alternative:"onFocusOutside"}),T(e)}else t&&t()},[te,ne]=(0,u.__experimentalUseDialog)({focusOnMount:w,__unstableOnClose:ee,onClose:ee}),re=(0,u.useMergeRefs)([U,te,$]),oe=Boolean(M&&K)&&function(e){if("loading"===e.type)return s()("components-animate__loading");const{type:t,origin:n=z(t)}=e;if("appear"===t){const[e,t="center"]=n.split(" ");return s()("components-animate__appear",{["is-from-"+t]:"center"!==t,["is-from-"+e]:"middle"!==e})}return"slide-in"===t?s()("components-animate__slide-in","is-from-"+n):void 0}({type:"appear",origin:K});let ie=(0,o.createElement)("div",(0,r.A)({className:s()("components-popover",i,oe,{"is-expanded":J,"is-without-arrow":c,"is-alternate":d})},B,{ref:re},ne,{tabIndex:"-1"}),J&&(0,o.createElement)(y,null),J&&(0,o.createElement)("div",{className:"components-popover__header"},(0,o.createElement)("span",{className:"components-popover__header-title"},e),(0,o.createElement)(h.A,{className:"components-popover__close",icon:f,onClick:t})),(0,o.createElement)("div",{ref:W,className:"components-popover__content"},(0,o.createElement)("div",{style:{position:"relative"}},X,n)));return Y.ref&&(ie=(0,o.createElement)(L,{name:H},ie)),E||x?ie:(0,o.createElement)("span",{ref:G},ie)}));H.Slot=(0,o.forwardRef)((function({name:e=O},t){return(0,o.createElement)(V,{bubblesVirtually:!0,name:e,className:"popover-slot",ref:t})}));const I=H},8596:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(6087),o=n(8468);const i=/^(2432|5454|8915)$/.test(n.j)?function({shortcut:e,className:t}){if(!e)return null;let n,i;return(0,o.isString)(e)&&(n=e),(0,o.isObject)(e)&&(n=e.display,i=e.ariaLabel),(0,r.createElement)("span",{className:t,"aria-label":i},n)}:null},9364:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(6087);if(/^(2432|5454|8915)$/.test(n.j))var o=n(5947);function i(){const[,e]=(0,r.useState)({}),t=(0,r.useRef)(!0);return(0,r.useEffect)((()=>()=>{t.current=!1}),[]),()=>{t.current&&e({})}}function s({name:e,children:t}){const n=(0,o.A)(e),s=(0,r.useRef)({rerender:i()});return(0,r.useEffect)((()=>(n.registerFill(s),()=>{n.unregisterFill(s)})),[n.registerFill,n.unregisterFill]),n.ref&&n.ref.current?("function"==typeof t&&(t=t(n.fillProps)),(0,r.createPortal)(t,n.ref.current)):null}},8493:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(6087);n(979);const o=(0,r.createContext)({slots:{},fills:{},registerSlot:()=>{"undefined"!=typeof process&&process.env},updateSlot:()=>{},unregisterSlot:()=>{},registerFill:()=>{},unregisterFill:()=>{}}),i=/^(2432|5454|8915)$/.test(n.j)?o:null},5947:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(6087);if(/^(2432|5454|8915)$/.test(n.j))var o=n(8493);function i(e){const t=(0,r.useContext)(o.A),n=t.slots[e]||{},i=t.fills[e],s=(0,r.useMemo)((()=>i||[]),[i]);return{...n,updateSlot:(0,r.useCallback)((n=>{t.updateSlot(e,n)}),[e,t.updateSlot]),unregisterSlot:(0,r.useCallback)((n=>{t.unregisterSlot(e,n)}),[e,t.unregisterSlot]),fills:s,registerFill:(0,r.useCallback)((n=>{t.registerFill(e,n)}),[e,t.registerFill]),unregisterFill:(0,r.useCallback)((n=>{t.unregisterFill(e,n)}),[e,t.unregisterFill])}}},4855:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});const r=(0,n(6087).createContext)({registerSlot:()=>{},unregisterSlot:()=>{},registerFill:()=>{},unregisterFill:()=>{},getSlot:()=>{},getFills:()=>{},subscribe:()=>{}}),o=/^(2432|5454|8915)$/.test(n.j)?r:null},5291:(e,t,n)=>{"use strict";if(n.d(t,{A:()=>l}),/^(2432|5454|8915)$/.test(n.j))var r=n(2509);var o=n(6087),i=n(8468);if(/^(2432|5454|8915)$/.test(n.j))var s=n(4855);if(/^(2432|5454|8915)$/.test(n.j))var a=n(4120);function c({name:e,children:t,registerFill:n,unregisterFill:r}){const s=(0,a.A)(e),c=(0,o.useRef)({name:e,children:t});return(0,o.useLayoutEffect)((()=>(n(e,c.current),()=>r(e,c.current))),[]),(0,o.useLayoutEffect)((()=>{c.current.children=t,s&&s.forceUpdate()}),[t]),(0,o.useLayoutEffect)((()=>{e!==c.current.name&&(r(c.current.name,c.current),c.current.name=e,n(e,c.current))}),[e]),s&&s.node?((0,i.isFunction)(t)&&(t=t(s.props.fillProps)),(0,o.createPortal)(t,s.node)):null}const l=/^(2432|5454|8915)$/.test(n.j)?e=>(0,o.createElement)(s.A.Consumer,null,(({registerFill:t,unregisterFill:n})=>(0,o.createElement)(c,(0,r.A)({},e,{registerFill:t,unregisterFill:n})))):null},4120:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(6087);if(/^(2432|5454|8915)$/.test(n.j))var o=n(4855);const i=/^(2432|5454|8915)$/.test(n.j)?e=>{const{getSlot:t,subscribe:n}=(0,r.useContext)(o.A),[i,s]=(0,r.useState)(t(e));return(0,r.useEffect)((()=>(s(t(e)),n((()=>{s(t(e))})))),[e]),i}:null},606:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(7697),o=n.n(r),i=n(6087);const s=(0,i.forwardRef)((function({as:e="div",className:t,...n},r){return function({as:e="div",...t}){return"function"==typeof t.children?t.children(t):(0,i.createElement)(e,t)}({as:e,className:o()("components-visually-hidden",t),...n,ref:r})}))},5521:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(6087),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})),s=/^(2432|5454|8915)$/.test(n.j)?i:null},4530:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=n(6087);const o=(0,r.forwardRef)((function({icon:e,size:t=24,...n},o){return(0,r.cloneElement)(e,{width:t,height:t,...n,ref:o})}))},556:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"m16.5 13.5-3.7 3.7V4h-1.5v13.2l-3.8-3.7-1 1 5.5 5.6 5.5-5.6z"})),s=6609==n.j?i:null},5181:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M20 11.2H6.8l3.7-3.7-1-1L3.9 12l5.6 5.5 1-1-3.7-3.7H20z"})),s=251==n.j?i:null},4476:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"m14.5 6.5-1 1 3.7 3.7H4v1.6h13.2l-3.7 3.7 1 1 5.6-5.5z"})),s=6609==n.j?i:null},5634:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{d:"M12.5939 21C14.1472 21 16.1269 20.5701 17.0711 20.1975L16.6447 18.879C16.0964 19.051 14.3299 19.6242 12.6548 19.6242C7.4467 19.6242 4.67513 16.8726 4.67513 12C4.67513 7.21338 7.50762 4.34713 12.2893 4.34713C17.132 4.34713 19.4162 7.55732 19.4162 10.7675C19.4162 14.035 19.0508 15.4968 17.4975 15.4968C16.5838 15.4968 16.0964 14.7803 16.0964 13.9777V7.5H14.4822V8.30255H14.3909C14.1777 7.67198 12.9898 7.12739 11.467 7.2707C9.18274 7.5 7.4467 9.27707 7.4467 11.8567C7.4467 14.5796 8.81726 16.672 11.467 16.758C13.203 16.8153 14.1168 16.0127 14.4822 15.1815H14.5736C14.7563 16.414 16.401 16.8439 17.467 16.8439C20.6954 16.8439 21 13.5764 21 10.7962C21 6.86943 18.0761 3 12.3807 3C6.50254 3 3 6.3535 3 11.9427C3 17.7325 6.38071 21 12.5939 21ZM11.7107 15.2962C9.73096 15.2962 9.03046 13.6051 9.03046 11.7707C9.03046 10.1083 10.0355 8.67516 11.7716 8.67516C13.599 8.67516 14.5736 9.36306 14.5736 11.7707C14.5736 14.1497 13.7513 15.2962 11.7107 15.2962Z"})),s=251==n.j?i:null},1465:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M5.5 12h1.75l-2.5 3-2.5-3H4a8 8 0 113.134 6.35l.907-1.194A6.5 6.5 0 105.5 12zm9.53 1.97l-2.28-2.28V8.5a.75.75 0 00-1.5 0V12a.747.747 0 00.218.529l1.282-.84-1.28.842 2.5 2.5a.75.75 0 101.06-1.061z"})),s=5065==n.j?i:null},9143:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M19 8h-1V6h-5v2h-2V6H6v2H5c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-8c0-1.1-.9-2-2-2zm.5 10c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5v-8c0-.3.2-.5.5-.5h14c.3 0 .5.2.5.5v8z"})),s=251==n.j?i:null},8471:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{fillRule:"evenodd",d:"M5 5.5h14a.5.5 0 01.5.5v1.5a.5.5 0 01-.5.5H5a.5.5 0 01-.5-.5V6a.5.5 0 01.5-.5zM4 9.232A2 2 0 013 7.5V6a2 2 0 012-2h14a2 2 0 012 2v1.5a2 2 0 01-1 1.732V18a2 2 0 01-2 2H6a2 2 0 01-2-2V9.232zm1.5.268V18a.5.5 0 00.5.5h12a.5.5 0 00.5-.5V9.5h-13z",clipRule:"evenodd"})),s=/^(1001|2733|5454|5819)$/.test(n.j)?i:null},6012:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{d:"M8 12.5h8V11H8v1.5Z M19 6.5H5a2 2 0 0 0-2 2V15a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.5a2 2 0 0 0-2-2ZM5 8h14a.5.5 0 0 1 .5.5V15a.5.5 0 0 1-.5.5H5a.5.5 0 0 1-.5-.5V8.5A.5.5 0 0 1 5 8Z"})),s=/^(1(001|028|276)|2(|100|475|51)|40|7435|7949|8851|9122|9860)$/.test(n.j)?i:null},1331:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{d:"M14.5 17.5H9.5V16H14.5V17.5Z M14.5 8H9.5V6.5H14.5V8Z M7 3.5H17C18.1046 3.5 19 4.39543 19 5.5V9C19 10.1046 18.1046 11 17 11H7C5.89543 11 5 10.1046 5 9V5.5C5 4.39543 5.89543 3.5 7 3.5ZM17 5H7C6.72386 5 6.5 5.22386 6.5 5.5V9C6.5 9.27614 6.72386 9.5 7 9.5H17C17.2761 9.5 17.5 9.27614 17.5 9V5.5C17.5 5.22386 17.2761 5 17 5Z M7 13H17C18.1046 13 19 13.8954 19 15V18.5C19 19.6046 18.1046 20.5 17 20.5H7C5.89543 20.5 5 19.6046 5 18.5V15C5 13.8954 5.89543 13 7 13ZM17 14.5H7C6.72386 14.5 6.5 14.7239 6.5 15V18.5C6.5 18.7761 6.72386 19 7 19H17C17.2761 19 17.5 18.7761 17.5 18.5V15C17.5 14.7239 17.2761 14.5 17 14.5Z"})),s=277==n.j?i:null},3174:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm.5 16c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5V7h15v12zM9 10H7v2h2v-2zm0 4H7v2h2v-2zm4-4h-2v2h2v-2zm4 0h-2v2h2v-2zm-4 4h-2v2h2v-2zm4 0h-2v2h2v-2z"})),s=5201==n.j?i:null},3028:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{d:"M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21ZM15.5303 8.46967C15.8232 8.76256 15.8232 9.23744 15.5303 9.53033L13.0607 12L15.5303 14.4697C15.8232 14.7626 15.8232 15.2374 15.5303 15.5303C15.2374 15.8232 14.7626 15.8232 14.4697 15.5303L12 13.0607L9.53033 15.5303C9.23744 15.8232 8.76256 15.8232 8.46967 15.5303C8.17678 15.2374 8.17678 14.7626 8.46967 14.4697L10.9393 12L8.46967 9.53033C8.17678 9.23744 8.17678 8.76256 8.46967 8.46967C8.76256 8.17678 9.23744 8.17678 9.53033 8.46967L12 10.9393L14.4697 8.46967C14.7626 8.17678 15.2374 8.17678 15.5303 8.46967Z"})),s=/^(1(001|235|85|890)|(331|506|959)5|(410|520|646|893)1|2272|2432|6273|7686|8848|9918)$/.test(n.j)?i:null},5536:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{d:"M6 5.5h3a.5.5 0 01.5.5v3a.5.5 0 01-.5.5H6a.5.5 0 01-.5-.5V6a.5.5 0 01.5-.5zM4 6a2 2 0 012-2h3a2 2 0 012 2v3a2 2 0 01-2 2H6a2 2 0 01-2-2V6zm11-.5h3a.5.5 0 01.5.5v3a.5.5 0 01-.5.5h-3a.5.5 0 01-.5-.5V6a.5.5 0 01.5-.5zM13 6a2 2 0 012-2h3a2 2 0 012 2v3a2 2 0 01-2 2h-3a2 2 0 01-2-2V6zm5 8.5h-3a.5.5 0 00-.5.5v3a.5.5 0 00.5.5h3a.5.5 0 00.5-.5v-3a.5.5 0 00-.5-.5zM15 13a2 2 0 00-2 2v3a2 2 0 002 2h3a2 2 0 002-2v-3a2 2 0 00-2-2h-3zm-9 1.5h3a.5.5 0 01.5.5v3a.5.5 0 01-.5.5H6a.5.5 0 01-.5-.5v-3a.5.5 0 01.5-.5zM4 15a2 2 0 012-2h3a2 2 0 012 2v3a2 2 0 01-2 2H6a2 2 0 01-2-2v-3z",fillRule:"evenodd",clipRule:"evenodd"})),s=/^(2432|5201|5819|8848)$/.test(n.j)?i:null},8226:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{fillRule:"evenodd",d:"M11.25 5h1.5v15h-1.5V5zM6 10h1.5v10H6V10zm12 4h-1.5v6H18v-6z",clipRule:"evenodd"})),s=5201==n.j?i:null},8034:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})),s=/^(251|4950)$/.test(n.j)?i:null},2174:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})),s=/^(2432|251|5454|7949|8915)$/.test(n.j)?i:null},559:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{d:"M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"})),s=251==n.j?i:null},1924:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})),s=/^(2507|6609)$/.test(n.j)?i:null},5614:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M13 11.8l6.1-6.3-1-1-6.1 6.2-6.1-6.2-1 1 6.1 6.3-6.5 6.7 1 1 6.5-6.6 6.5 6.6 1-1z"})),s=/^(251|4950|5870)$/.test(n.j)?i:null},4782:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M19 6H6c-1.1 0-2 .9-2 2v9c0 1.1.9 2 2 2h13c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zM6 17.5c-.3 0-.5-.2-.5-.5V8c0-.3.2-.5.5-.5h3v10H6zm13.5-.5c0 .3-.2.5-.5.5h-3v-10h3c.3 0 .5.2.5.5v9z"})),s=/^(251|7949)$/.test(n.j)?i:null},3627:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{d:"M18 4H6c-1.1 0-2 .9-2 2v12.9c0 .6.5 1.1 1.1 1.1.3 0 .5-.1.8-.3L8.5 17H18c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm.5 11c0 .3-.2.5-.5.5H7.9l-2.4 2.4V6c0-.3.2-.5.5-.5h12c.3 0 .5.2.5.5v9z",fillRule:"evenodd",clipRule:"evenodd"}),(0,r.createElement)(o.Path,{d:"M15 15V15C15 13.8954 14.1046 13 13 13L11 13C9.89543 13 9 13.8954 9 15V15",fillRule:"evenodd",clipRule:"evenodd"}),(0,r.createElement)(o.Circle,{cx:"12",cy:"9",r:"2",fillRule:"evenodd",clipRule:"evenodd"})),s=8193==n.j?i:null},3791:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M6.68822 16.625L5.5 17.8145L5.5 5.5L18.5 5.5L18.5 16.625L6.68822 16.625ZM7.31 18.125L19 18.125C19.5523 18.125 20 17.6773 20 17.125L20 5C20 4.44772 19.5523 4 19 4H5C4.44772 4 4 4.44772 4 5V19.5247C4 19.8173 4.16123 20.086 4.41935 20.2237C4.72711 20.3878 5.10601 20.3313 5.35252 20.0845L7.31 18.125ZM16 9.99997H8V8.49997H16V9.99997ZM8 14H13V12.5H8V14Z"})),s=/^(1890|251|2753|6273|7949)$/.test(n.j)?i:null},3462:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M16.5 7.8v7H18v-7c0-1-.8-1.8-1.8-1.8h-7v1.5h7c.2 0 .3.1.3.3zm-8.7 8.7c-.1 0-.2-.1-.2-.2V2H6v4H2v1.5h4v8.8c0 1 .8 1.8 1.8 1.8h8.8v4H18v-4h4v-1.5H7.8z"})),s=/^(8931|9918)$/.test(n.j)?i:null},8486:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M3.25 12a8.75 8.75 0 1117.5 0 8.75 8.75 0 01-17.5 0zM12 4.75a7.25 7.25 0 100 14.5 7.25 7.25 0 000-14.5zm-1.338 4.877c-.314.22-.412.452-.412.623 0 .171.098.403.412.623.312.218.783.377 1.338.377.825 0 1.605.233 2.198.648.59.414 1.052 1.057 1.052 1.852 0 .795-.461 1.438-1.052 1.852-.41.286-.907.486-1.448.582v.316a.75.75 0 01-1.5 0v-.316a3.64 3.64 0 01-1.448-.582c-.59-.414-1.052-1.057-1.052-1.852a.75.75 0 011.5 0c0 .171.098.403.412.623.312.218.783.377 1.338.377s1.026-.159 1.338-.377c.314-.22.412-.452.412-.623 0-.171-.098-.403-.412-.623-.312-.218-.783-.377-1.338-.377-.825 0-1.605-.233-2.198-.648-.59-.414-1.052-1.057-1.052-1.852 0-.795.461-1.438 1.052-1.852a3.64 3.64 0 011.448-.582V7.5a.75.75 0 011.5 0v.316c.54.096 1.039.296 1.448.582.59.414 1.052 1.057 1.052 1.852a.75.75 0 01-1.5 0c0-.171-.098-.403-.412-.623-.312-.218-.783-.377-1.338-.377s-1.026.159-1.338.377z"})),s=/^(1001|1493|251|5819|7949)$/.test(n.j)?i:null},7223:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M4 20h9v-1.5H4V20zm0-5.5V16h16v-1.5H4zm.8-4l.7.7 2-2V12h1V9.2l2 2 .7-.7-2-2H12v-1H9.2l2-2-.7-.7-2 2V4h-1v2.8l-2-2-.7.7 2 2H4v1h2.8l-2 2z"})),s=251==n.j?i:null},5200:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M18 11.3l-1-1.1-4 4V3h-1.5v11.3L7 10.2l-1 1.1 6.2 5.8 5.8-5.8zm.5 3.7v3.5h-13V15H4v5h16v-5h-1.5z"})),s=/^(1323|6181)$/.test(n.j)?i:null},7035:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})),s=/^(1001|1493|2432|251|5065|7949)$/.test(n.j)?i:null},8042:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{d:"M19 6.2h-5.9l-.6-1.1c-.3-.7-1-1.1-1.8-1.1H5c-1.1 0-2 .9-2 2v11.8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V8.2c0-1.1-.9-2-2-2zm.5 11.6c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5V6c0-.3.2-.5.5-.5h5.8c.2 0 .4.1.4.3l1 2H19c.3 0 .5.2.5.5v9.5z"})),s=4101==n.j?i:null},9784:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{d:"M16.375 4.5H4.625a.125.125 0 0 0-.125.125v8.254l2.859-1.54a.75.75 0 0 1 .68-.016l2.384 1.142 2.89-2.074a.75.75 0 0 1 .874 0l2.313 1.66V4.625a.125.125 0 0 0-.125-.125Zm.125 9.398-2.75-1.975-2.813 2.02a.75.75 0 0 1-.76.067l-2.444-1.17L4.5 14.583v1.792c0 .***************.125h11.75a.125.125 0 0 0 .125-.125v-2.477ZM4.625 3C3.728 3 3 3.728 3 4.625v11.75C3 17.273 3.728 18 4.625 18h11.75c.898 0 1.625-.727 1.625-1.625V4.625C18 3.728 17.273 3 16.375 3H4.625ZM20 8v11c0 .69-.31 1-.999 1H6v1.5h13.001c1.52 0 2.499-.982 2.499-2.5V8H20Z",fillRule:"evenodd",clipRule:"evenodd"})),s=1001==n.j?i:null},9264:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"m3 5c0-1.10457.89543-2 2-2h13.5c1.1046 0 2 .89543 2 2v13.5c0 1.1046-.8954 2-2 2h-13.5c-1.10457 0-2-.8954-2-2zm2-.5h6v6.5h-6.5v-6c0-.27614.22386-.5.5-.5zm-.5 8v6c0 .2761.22386.5.5.5h6v-6.5zm8 0v6.5h6c.2761 0 .5-.2239.5-.5v-6zm0-8v6.5h6.5v-6c0-.27614-.2239-.5-.5-.5z",fillRule:"evenodd",clipRule:"evenodd"})),s=/^10(01|28)$/.test(n.j)?i:null},8992:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M6 5V18.5911L12 13.8473L18 18.5911V5H6Z"})),s=/^(1(001|028|861)|2344|2733|3315|5065|5201)$/.test(n.j)?i:null},4003:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM5 4.5h14c.3 0 .5.2.5.5v8.4l-3-2.9c-.3-.3-.8-.3-1 0L11.9 14 9 12c-.3-.2-.6-.2-.8 0l-3.6 2.6V5c-.1-.3.1-.5.4-.5zm14 15H5c-.3 0-.5-.2-.5-.5v-2.4l4.1-3 3 1.9c.*******.9-.1L16 12l3.5 3.4V19c0 .3-.2.5-.5.5z"})),s=1001==n.j?i:null},2624:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M12 3.2c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8 0-4.8-4-8.8-8.8-8.8zm0 16c-4 0-7.2-3.3-7.2-7.2C4.8 8 8 4.8 12 4.8s7.2 3.3 7.2 7.2c0 4-3.2 7.2-7.2 7.2zM11 17h2v-6h-2v6zm0-8h2V7h-2v2z"})),s=/^(1((00|2|63)1|235|85|890)|2(272|432|448|51)|7(064|155|341|686)|(306|884|991)8|(331|506|959)5|(410|520|646|893)1|4950|6273)$/.test(n.j)?i:null},6600:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{fillRule:"evenodd",d:"M18.646 9H20V8l-1-.5L12 4 5 7.5 4 8v1h14.646zm-3-1.5L12 5.677 8.354 7.5h7.292zm-7.897 9.44v-6.5h-1.5v6.5h1.5zm5-6.5v6.5h-1.5v-6.5h1.5zm5 0v6.5h-1.5v-6.5h1.5zm2.252 8.81c0 .414-.334.75-.748.75H4.752a.75.75 0 010-1.5h14.5a.75.75 0 01.749.75z",clipRule:"evenodd"})),s=/^(251|7949)$/.test(n.j)?i:null},5534:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})),s=/^(1001|3315|6732)$/.test(n.j)?i:null},9822:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M12 11v1.5h8V11h-8zm-6-1c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"})),s=9759==n.j?i:null},3196:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{d:"M3 6h11v1.5H3V6Zm3.5 5.5h11V13h-11v-1.5ZM21 17H10v1.5h11V17Z"})),s=2845==n.j?i:null},8940:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{d:"M4 4v1.5h16V4H4zm8 8.5h8V11h-8v1.5zM4 20h16v-1.5H4V20zm4-8c0-1.1-.9-2-2-2s-2 .9-2 2 .9 2 2 2 2-.9 2-2z"})),s=1028==n.j?i:null},5350:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{d:"M18.1823 11.6392C18.1823 13.0804 17.0139 14.2487 15.5727 14.2487C14.3579 14.2487 13.335 13.4179 13.0453 12.2922L13.0377 12.2625L13.0278 12.2335L12.3985 10.377L12.3942 10.3785C11.8571 8.64997 10.246 7.39405 8.33961 7.39405C5.99509 7.39405 4.09448 9.29465 4.09448 11.6392C4.09448 13.9837 5.99509 15.8843 8.33961 15.8843C8.88499 15.8843 9.40822 15.781 9.88943 15.5923L9.29212 14.0697C8.99812 14.185 8.67729 14.2487 8.33961 14.2487C6.89838 14.2487 5.73003 13.0804 5.73003 11.6392C5.73003 10.1979 6.89838 9.02959 8.33961 9.02959C9.55444 9.02959 10.5773 9.86046 10.867 10.9862L10.8772 10.9836L11.4695 12.7311C11.9515 14.546 13.6048 15.8843 15.5727 15.8843C17.9172 15.8843 19.8178 13.9837 19.8178 11.6392C19.8178 9.29465 17.9172 7.39404 15.5727 7.39404C15.0287 7.39404 14.5066 7.4968 14.0264 7.6847L14.6223 9.20781C14.9158 9.093 15.2358 9.02959 15.5727 9.02959C17.0139 9.02959 18.1823 10.1979 18.1823 11.6392Z"})),s=/^5(201|838)$/.test(n.j)?i:null},2216:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M12 9c-.8 0-1.5.7-1.5 1.5S11.2 12 12 12s1.5-.7 1.5-1.5S12.8 9 12 9zm0-5c-3.6 0-6.5 2.8-6.5 6.2 0 .8.3 1.8.9 3.1.5 1.1 1.2 2.3 2 3.6.7 1 3 3.8 3.2 3.9l.4.5.4-.5c.2-.2 2.6-2.9 3.2-3.9.8-1.2 1.5-2.5 2-3.6.6-1.3.9-2.3.9-3.1C18.5 6.8 15.6 4 12 4zm4.3 8.7c-.5 1-1.1 2.2-1.9 3.4-.5.7-1.7 2.2-2.4 3-.7-.8-1.9-2.3-2.4-3-.8-1.2-1.4-2.3-1.9-3.3-.6-1.4-.7-2.2-.7-2.5 0-2.6 2.2-4.7 5-4.7s5 2.1 5 4.7c0 .2-.1 1-.7 2.4z"})),s=/^(251|3806|6571|7293|7492)$/.test(n.j)?i:null},3492:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M3 6v11.5h8V6H3Zm11 3h7V7.5h-7V9Zm7 3.5h-7V11h7v1.5ZM14 16h7v-1.5h-7V16Z"})),s=5065==n.j?i:null},4144:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{fillRule:"evenodd",d:"M6.863 13.644L5 13.25h-.5a.5.5 0 01-.5-.5v-3a.5.5 0 01.5-.5H5L18 6.5h2V16h-2l-3.854-.815.026.008a3.75 3.75 0 01-7.31-1.549zm1.477.313a2.251 2.251 0 004.356.921l-4.356-.921zm-2.84-3.28L18.157 8h.343v6.5h-.343L5.5 11.823v-1.146z",clipRule:"evenodd"})),s=/^(251|4950)$/.test(n.j)?i:null},7715:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M7 5.5h10a.5.5 0 01.5.5v12a.5.5 0 01-.5.5H7a.5.5 0 01-.5-.5V6a.5.5 0 01.5-.5zM17 4H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2zm-1 3.75H8v1.5h8v-1.5zM8 11h8v1.5H8V11zm6 3.25H8v1.5h6v-1.5z"})),s=/^((100|25|520)1|3315|7960)$/.test(n.j)?i:null},6208:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{fillRule:"evenodd",d:"M5.5 9.5v-2h13v2h-13zm0 3v4h13v-4h-13zM4 7a1 1 0 011-1h14a1 1 0 011 1v10a1 1 0 01-1 1H5a1 1 0 01-1-1V7z",clipRule:"evenodd"})),s=/^(1028|251|7949)$/.test(n.j)?i:null},6099:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M15.5 9.5a1 1 0 100-2 1 1 0 000 2zm0 1.5a2.5 2.5 0 100-5 2.5 2.5 0 000 5zm-2.25 6v-2a2.75 2.75 0 00-2.75-2.75h-4A2.75 2.75 0 003.75 15v2h1.5v-2c0-.69.56-1.25 1.25-1.25h4c.69 0 1.25.56 1.25 1.25v2h1.5zm7-2v2h-1.5v-2c0-.69-.56-1.25-1.25-1.25H15v-1.5h2.5A2.75 2.75 0 0120.25 15zM9.5 8.5a1 1 0 11-2 0 1 1 0 012 0zm1.5 0a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z",fillRule:"evenodd"})),s=5932==n.j?i:null},9771:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{fillRule:"evenodd",d:"M6.5 8a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0zM8 5a3 3 0 100 6 3 3 0 000-6zm6.5 11a1.5 1.5 0 103 0 1.5 1.5 0 00-3 0zm1.5-3a3 3 0 100 6 3 3 0 000-6zM5.47 17.41a.75.75 0 001.06 1.06L18.47 6.53a.75.75 0 10-1.06-1.06L5.47 17.41z",clipRule:"evenodd"})),s=/^((10|52)01|7686)$/.test(n.j)?i:null},7888:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M13 8H4v1.5h9V8zM4 4v1.5h16V4H4zm9 8H5c-.6 0-1 .4-1 1v8.3c0 .*******.*******.******* 0 .5-.1.6-.3l1.8-1.8H13c.6 0 1-.4 1-1V13c0-.6-.4-1-1-1zm-.5 6.6H6.7l-1.2 1.2v-6.3h7v5.1z"})),s=9596==n.j?i:null},3961:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M14 10.1V4c0-.6-.4-1-1-1H5c-.6 0-1 .4-1 1v8.3c0 .*******.*******.******* 0 .5-.1.6-.3l1.8-1.8H13c.6 0 1-.4 1-1zm-1.5-.5H6.7l-1.2 1.2V4.5h7v5.1zM19 12h-8c-.6 0-1 .4-1 1v6.1c0 .6.4 1 1 1h5.7l1.8 1.8c.*******.6.3.1 0 .2 0 .3-.1.4-.1.6-.5.6-.8V13c0-.6-.4-1-1-1zm-.5 7.8l-1.2-1.2h-5.8v-5.1h7v6.3z"})),s=848==n.j?i:null},7071:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M11.696 13.972c.356-.546.599-.958.728-1.235a1.79 1.79 0 00.203-.783c0-.264-.077-.47-.23-.618-.148-.153-.354-.23-.618-.23-.295 0-.569.07-.82.212a3.413 3.413 0 00-.738.571l-.147-1.188c.289-.234.59-.41.903-.526.313-.117.66-.175 1.041-.175.375 0 .695.08.959.24.264.153.46.362.59.626.135.265.203.556.203.876 0 .362-.08.734-.24 1.115-.154.381-.427.87-.82 1.466l-.756 1.152H14v1.106h-4l1.696-2.609z"}),(0,r.createElement)(o.Path,{d:"M19.5 7h-15v12a.5.5 0 00.5.5h14a.5.5 0 00.5-.5V7zM3 7V5a2 2 0 012-2h14a2 2 0 012 2v14a2 2 0 01-2 2H5a2 2 0 01-2-2V7z"})),s=2146==n.j?i:null},6081:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M5 13.5h3v-3H5v3zm5 0h3v-3h-3v3zM17 9l-1 1 2 2-2 2 1 1 3-3-3-3z"})),s=1789==n.j?i:null},6044:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M4 13.5h6v-3H4v3zm8.2-2.5.8-.3V14h1V9.3l-2.2.7.4 1zm7.1-1.2c-.5-.6-1.2-.5-1.7-.4-.3.1-.5.2-.7.3l.1 1.1c.2-.2.5-.4.8-.5.3-.1.6 0 .7.1.2.3 0 .8-.2 1.1-.5.8-.9 1.6-1.4 2.5h2.7v-1h-.9c.3-.6.8-1.4.9-2.1 0-.3-.1-.8-.3-1.1z"})),s=6364==n.j?i:null},3653:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M16 10.5v3h3v-3h-3zm-5 3h3v-3h-3v3zM7 9l-3 3 3 3 1-1-2-2 2-2-1-1z"})),s=1157==n.j?i:null},455:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M4 13.5h6v-3H4v3zm8 0h3v-3h-3v3zm5-3v3h3v-3h-3z"})),s=4257==n.j?i:null},7026:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{fillRule:"evenodd",d:"M16.83 6.342l.602.3.625-.25.443-.176v12.569l-.443-.178-.625-.25-.603.301-1.444.723-2.41-.804-.475-.158-.474.158-2.41.803-1.445-.722-.603-.3-.625.25-.443.177V6.215l.443.178.625.25.603-.301 1.444-.722 2.41.803.475.158.474-.158 2.41-.803 1.445.722zM20 4l-1.5.6-1 .4-2-1-3 1-3-1-2 1-1-.4L5 4v17l1.5-.6 1-.4 2 1 3-1 3 1 2-1 1 .4 1.5.6V4zm-3.5 6.25v-1.5h-8v1.5h8zm0 3v-1.5h-8v1.5h8zm-8 3v-1.5h8v1.5h-8z",clipRule:"evenodd"})),s=4118==n.j?i:null},3255:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M7 7.2h8.2L13.5 9l1.1 1.1 3.6-3.6-3.5-4-1.1 1 1.9 2.3H7c-.9 0-1.7.3-2.3.9-1.4 1.5-1.4 4.2-1.4 5.6v.2h1.5v-.3c0-1.1 0-3.5 1-4.5.3-.3.7-.5 1.2-.5zm13.8 4V11h-1.5v.3c0 1.1 0 3.5-1 4.5-.3.3-.7.5-1.3.5H8.8l1.7-1.7-1.1-1.1L5.9 17l3.5 4 1.1-1-1.9-2.3H17c.9 0 1.7-.3 2.3-.9 1.5-1.4 1.5-4.2 1.5-5.6z"})),s=5201==n.j?i:null},428:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M13 5c-3.3 0-6 2.7-6 6 0 1.4.5 2.7 1.3 3.7l-3.8 3.8 1.1 1.1 3.8-3.8c1 .8 2.3 1.3 3.7 1.3 3.3 0 6-2.7 6-6S16.3 5 13 5zm0 10.5c-2.5 0-4.5-2-4.5-4.5s2-4.5 4.5-4.5 4.5 2 4.5 4.5-2 4.5-4.5 4.5z"})),s=/^(1001|2545)$/.test(n.j)?i:null},4970:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M3 6.75C3 5.784 3.784 5 4.75 5H15V7.313l.05.027 5.056 2.73.394.212v3.468a1.75 1.75 0 01-1.75 1.75h-.012a2.5 2.5 0 11-4.975 0H9.737a2.5 2.5 0 11-4.975 0H3V6.75zM13.5 14V6.5H4.75a.25.25 0 00-.25.25V14h.965a2.493 2.493 0 011.785-.75c.7 0 1.332.287 1.785.75H13.5zm4.535 0h.715a.25.25 0 00.25-.25v-2.573l-4-2.16v4.568a2.487 2.487 0 011.25-.335c.7 0 1.332.287 1.785.75zM6.282 15.5a1.002 1.002 0 00.968 1.25 1 1 0 10-.968-1.25zm9 0a1 1 0 101.937.498 1 1 0 00-1.938-.498z"})),s=251==n.j?i:null},9113:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},(0,r.createElement)(o.Path,{d:"M16 4.2v1.5h2.5v12.5H16v1.5h4V4.2h-4zM4.2 19.8h4v-1.5H5.8V5.8h2.5V4.2h-4l-.1 15.6zm5.1-3.1l1.4.6 4-10-1.4-.6-4 10z"})),s=3035==n.j?i:null},3106:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M17.5 4v5a2 2 0 0 1-2 2h-7a2 2 0 0 1-2-2V4H8v5a.5.5 0 0 0 .5.5h7A.5.5 0 0 0 16 9V4h1.5Zm0 16v-5a2 2 0 0 0-2-2h-7a2 2 0 0 0-2 2v5H8v-5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 .5.5v5h1.5Z"})),s=6461==n.j?i:null},2108:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{fillRule:"evenodd",d:"M9.706 8.646a.25.25 0 01-.188.137l-4.626.672a.25.25 0 00-.139.427l3.348 3.262a.25.25 0 01.072.222l-.79 4.607a.25.25 0 00.362.264l4.138-2.176a.25.25 0 01.233 0l4.137 2.175a.25.25 0 00.363-.263l-.79-4.607a.25.25 0 01.072-.222l3.347-3.262a.25.25 0 00-.139-.427l-4.626-.672a.25.25 0 01-.188-.137l-2.069-4.192a.25.25 0 00-.448 0L9.706 8.646zM12 7.39l-.948 1.921a1.75 1.75 0 01-1.317.957l-2.12.308 1.534 1.495c.412.402.6.982.503 1.55l-.362 2.11 1.896-.997a1.75 1.75 0 011.629 0l1.895.997-.362-2.11a1.75 1.75 0 01.504-1.55l1.533-1.495-2.12-.308a1.75 1.75 0 01-1.317-.957L12 7.39z",clipRule:"evenodd"})),s=/^(89(15|31)|(10|52)01|5819)$/.test(n.j)?i:null},3129:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M11.776 4.454a.25.25 0 01.448 0l2.069 4.192a.25.25 0 00.188.137l4.626.672a.25.25 0 01.139.426l-3.348 3.263a.25.25 0 00-.072.222l.79 4.607a.25.25 0 01-.362.263l-4.138-2.175a.25.25 0 00-.232 0l-4.138 2.175a.25.25 0 01-.363-.263l.79-4.607a.25.25 0 00-.071-.222L4.754 9.881a.25.25 0 01.139-.426l4.626-.672a.25.25 0 00.188-.137l2.069-4.192z"})),s=/^(10|52)01$/.test(n.j)?i:null},6052:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M9.518 8.783a.25.25 0 00.188-.137l2.069-4.192a.25.25 0 01.448 0l2.07 4.192a.25.25 0 00.187.137l4.626.672a.25.25 0 01.139.427l-3.347 3.262a.25.25 0 00-.072.222l.79 4.607a.25.25 0 01-.363.264l-4.137-2.176a.25.25 0 00-.233 0l-4.138 2.175a.25.25 0 01-.362-.263l.79-4.607a.25.25 0 00-.072-.222L4.753 9.882a.25.25 0 01.14-.427l4.625-.672zM12 14.533c.28 0 .559.067.814.2l1.895.997-.362-2.11a1.75 1.75 0 01.504-1.55l1.533-1.495-2.12-.308a1.75 1.75 0 01-1.317-.957L12 7.39v7.143z"})),s=/^(1001|8833)$/.test(n.j)?i:null},8415:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{fillRule:"evenodd",d:"M19.75 11H21V8.667L19.875 4H4.125L3 8.667V11h1.25v8.75h15.5V11zm-1.5 0H5.75v7.25H10V13h4v5.25h4.25V11zm-5.5-5.5h2.067l.486 3.24.028.76H12.75v-4zm-3.567 0h2.067v4H8.669l.028-.76.486-3.24zm7.615 3.1l-.464-3.1h2.36l.806 3.345V9.5h-2.668l-.034-.9zM7.666 5.5h-2.36L4.5 8.845V9.5h2.668l.034-.9.464-3.1z",clipRule:"evenodd"})),s=251==n.j?i:null},1686:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M20.1 11.2l-6.7-6.7c-.1-.1-.3-.2-.5-.2H5c-.4-.1-.8.3-.8.7v7.8c0 .*******.5l6.7 6.7c.*******.7.5s.6.2.9.2c.3 0 .6-.1.9-.2.3-.1.5-.3.8-.5l5.6-5.6c.4-.4.7-1 .7-1.6.1-.6-.2-1.2-.6-1.6zM19 13.4L13.4 19c-.1.1-.2.1-.3.2-.2.1-.4.1-.6 0-.1 0-.2-.1-.3-.2l-6.5-6.5V5.8h6.8l6.5 6.5c.*******.2.6 0 .1 0 .3-.2.5zM9 8c-.6 0-1 .4-1 1s.4 1 1 1 1-.4 1-1-.4-1-1-1z"})),s=/^(1235|251|5201|7949)$/.test(n.j)?i:null},3316:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"m4 5.5h2v6.5h1.5v-6.5h2v-1.5h-5.5zm16 10.5h-16v-1.5h16zm-7 4h-9v-1.5h9z"})),s=8613==n.j?i:null},4807:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,r.createElement)(o.Path,{d:"M3.445 16.505a.75.75 0 001.06.05l5.005-4.55 4.024 3.521 4.716-4.715V14h1.5V8.25H14v1.5h3.19l-3.724 3.723L9.49 9.995l-5.995 5.45a.75.75 0 00-.05 1.06z"})),s=/^(2272|5201)$/.test(n.j)?i:null},2098:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var r=n(1609),o=n(5573);const i=(0,r.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24"},(0,r.createElement)(o.Path,{d:"M10 2c4.42 0 8 3.58 8 8s-3.58 8-8 8-8-3.58-8-8 3.58-8 8-8zm1.13 9.38l.35-6.46H8.52l.35 6.46h2.26zm-.09 3.36c.24-.23.37-.55.37-.96 0-.42-.12-.74-.36-.97s-.59-.35-1.06-.35-.82.12-1.07.35-.37.55-.37.97c0 .41.13.73.38.96.26.23.61.34 1.06.34s.8-.11 1.05-.34z"})),s=/^(1001|1890|5065|6273|848|8931|9918)$/.test(n.j)?i:null},7697:(e,t)=>{var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var i=typeof n;if("string"===i||"number"===i)e.push(n);else if(Array.isArray(n)){if(n.length){var s=o.apply(null,n);s&&e.push(s)}}else if("object"===i){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){e.push(n.toString());continue}for(var a in n)r.call(n,a)&&n[a]&&e.push(a)}}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()},1244:(e,t,n)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;t.splice(1,0,n,"color: inherit");let r=0,o=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(r++,"%c"===e&&(o=r))})),t.splice(o,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){return!("undefined"==typeof window||!window.process||"renderer"!==window.process.type&&!window.process.__nwjs)||("undefined"==typeof navigator||!navigator.userAgent||!navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=n(4099)(t);const{formatters:r}=e.exports;r.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},4099:(e,t,n)=>{e.exports=function(e){function t(e){let n,o,i,s=null;function a(...e){if(!a.enabled)return;const r=a,o=Number(new Date),i=o-(n||o);r.diff=i,r.prev=n,r.curr=o,n=o,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let s=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((n,o)=>{if("%%"===n)return"%";s++;const i=t.formatters[o];if("function"==typeof i){const t=e[s];n=i.call(r,t),e.splice(s,1),s--}return n})),t.formatArgs.call(r,e),(r.log||t.log).apply(r,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(o!==t.namespaces&&(o=t.namespaces,i=t.enabled(e)),i),set:e=>{s=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,n){const r=t(this.namespace+(void 0===n?":":n)+e);return r.log=this.log,r}function o(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){const e=[...t.names.map(o),...t.skips.map(o).map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){let n;t.save(e),t.namespaces=e,t.names=[],t.skips=[];const r=("string"==typeof e?e:"").split(/[\s,]+/),o=r.length;for(n=0;n<o;n++)r[n]&&("-"===(e=r[n].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.slice(1)+"$")):t.names.push(new RegExp("^"+e+"$")))},t.enabled=function(e){if("*"===e[e.length-1])return!0;let n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=n(9295),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((n=>{t[n]=e[n]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let n=0;for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n|=0;return t.colors[Math.abs(n)%t.colors.length]},t.enable(t.load()),t}},6513:(e,t,n)=>{"use strict";n.d(t,{A:()=>b});function r(e){return r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function o(e){return function(e){if(Array.isArray(e))return e}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return i(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var s={normalizePrecision:function(e){var t=e.reduce((function(e,t){return Math.max(e.getPrecision(),t.getPrecision())}));return e.map((function(e){return e.getPrecision()!==t?e.convertPrecision(t):e}))},minimum:function(e){var t=o(e),n=t[0],r=t.slice(1),i=n;return r.forEach((function(e){i=i.lessThan(e)?i:e})),i},maximum:function(e){var t=o(e),n=t[0],r=t.slice(1),i=n;return r.forEach((function(e){i=i.greaterThan(e)?i:e})),i}};function a(e){return!isNaN(parseInt(e))&&isFinite(e)}function c(e){return e%2==0}function l(e){return a(e)&&!Number.isInteger(e)}function u(e){return Math.abs(e)%1==.5}function d(e){return void 0===e}function f(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:".",n={};return Object.entries(e).forEach((function(e){if("object"===r(e[1])){var o=f(e[1]);Object.entries(o).forEach((function(r){n[e[0]+t+r[0]]=r[1]}))}else n[e[0]]=e[1]})),n}function p(){var e={HALF_ODD:function(e){var t=Math.round(e);return u(e)&&c(t)?t-1:t},HALF_EVEN:function(e){var t=Math.round(e);return u(e)?c(t)?t:t-1:t},HALF_UP:function(e){return Math.round(e)},HALF_DOWN:function(e){return u(e)?Math.floor(e):Math.round(e)},HALF_TOWARDS_ZERO:function(e){return u(e)?Math.sign(e)*Math.floor(Math.abs(e)):Math.round(e)},HALF_AWAY_FROM_ZERO:function(e){return u(e)?Math.sign(e)*Math.ceil(Math.abs(e)):Math.round(e)},DOWN:function(e){return Math.floor(e)}};return{add:function(e,t){return e+t},subtract:function(e,t){return e-t},multiply:function(e,t){return l(e)||l(t)?function(e,t){var n=function(e){return Math.pow(10,function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0).toString();if(e.indexOf("e-")>0)return parseInt(e.split("e-")[1]);var t=e.split(".")[1];return t?t.length:0}(e))},r=Math.max(n(e),n(t));return Math.round(e*r)*Math.round(t*r)/(r*r)}(e,t):e*t},divide:function(e,t){return e/t},modulo:function(e,t){return e%t},round:function(t){return e[arguments.length>1&&void 0!==arguments[1]?arguments[1]:"HALF_EVEN"](t)}}}var m=p();function h(e){var t=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1?arguments[1]:void 0;for(var n in t)e=e.replace("{{".concat(n,"}}"),t[n]);return e};return{getExchangeRate:function(n,o){return(i=e.endpoint,!Boolean(i)||"object"!==r(i)&&"function"!=typeof i||"function"!=typeof i.then?function(n,r){return function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new Promise((function(n,r){var o=Object.assign(new XMLHttpRequest,{onreadystatechange:function(){4===o.readyState&&(o.status>=200&&o.status<400?n(JSON.parse(o.responseText)):r(new Error(o.statusText)))},onerror:function(){r(new Error("Network error"))}});o.open("GET",e,!0),function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};for(var n in t)e.setRequestHeader(n,t[n])}(o,t.headers),o.send()}))}(t(e.endpoint,{from:n,to:r}),{headers:e.headers})}(n,o):e.endpoint).then((function(r){return f(r)[t(e.propertyPath,{from:n,to:o})]}));var i}}}function v(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Error;if(!e)throw new n(t)}function g(e){v(Number.isInteger(e),"You must provide an integer.",TypeError)}var w=p(),y=Object.assign((function e(t){var n=Object.assign({},{amount:e.defaultAmount,currency:e.defaultCurrency,precision:e.defaultPrecision},t),r=n.amount,o=n.currency,i=n.precision;g(r),g(i);var s=e.globalLocale,c=e.globalFormat,l=e.globalRoundingMode,u=e.globalFormatRoundingMode,f=Object.assign({},e.globalExchangeRatesApi),p=function(t){var n=Object.assign({},Object.assign({},{amount:r,currency:o,precision:i},t),Object.assign({},{locale:this.locale},t));return Object.assign(e({amount:n.amount,currency:n.currency,precision:n.precision}),{locale:n.locale})},y=function(e){v(this.hasSameCurrency(e),"You must provide a Dinero instance with the same currency.",TypeError)};return{getAmount:function(){return r},getCurrency:function(){return o},getLocale:function(){return this.locale||s},setLocale:function(e){return p.call(this,{locale:e})},getPrecision:function(){return i},convertPrecision:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u;g(e);var n=this.getPrecision(),r=e>n,o=r?w.multiply:w.divide,i=r?[e,n]:[n,e],s=Math.pow(10,w.subtract.apply(w,i));return p.call(this,{amount:w.round(o(this.getAmount(),s),t),precision:e})},add:function(t){y.call(this,t);var n=e.normalizePrecision([this,t]);return p.call(this,{amount:w.add(n[0].getAmount(),n[1].getAmount()),precision:n[0].getPrecision()})},subtract:function(t){y.call(this,t);var n=e.normalizePrecision([this,t]);return p.call(this,{amount:w.subtract(n[0].getAmount(),n[1].getAmount()),precision:n[0].getPrecision()})},multiply:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l;return p.call(this,{amount:w.round(w.multiply(this.getAmount(),e),t)})},divide:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l;return p.call(this,{amount:w.round(w.divide(this.getAmount(),e),t)})},percentage:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l;return v(function(e){return a(e)&&e<=100&&e>=0}(e),"You must provide a numeric value between 0 and 100.",RangeError),this.multiply(w.divide(e,100),t)},allocate:function(e){var t=this;!function(e){v(function(e){return e.length>0&&e.every((function(e){return e>=0}))&&e.some((function(e){return e>0}))}(e),"You must provide a non-empty array of numeric values greater than 0.",TypeError)}(e);for(var n=e.reduce((function(e,t){return w.add(e,t)})),r=this.getAmount(),o=e.map((function(e){var o=Math.floor(w.divide(w.multiply(t.getAmount(),e),n));return r=w.subtract(r,o),p.call(t,{amount:o})})),i=0;r>0;)e[i]>0&&(o[i]=o[i].add(p.call(this,{amount:1})),r=w.subtract(r,1)),i+=1;return o},convert:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.endpoint,o=void 0===r?f.endpoint:r,i=n.propertyPath,s=void 0===i?f.propertyPath||"rates.{{to}}":i,a=n.headers,c=void 0===a?f.headers:a,u=n.roundingMode,m=void 0===u?l:u,g=Object.assign({},{endpoint:o,propertyPath:s,headers:c,roundingMode:m});return h(g).getExchangeRate(this.getCurrency(),e).then((function(n){return v(!d(n),'No rate was found for the destination currency "'.concat(e,'".'),TypeError),p.call(t,{amount:w.round(w.multiply(t.getAmount(),parseFloat(n)),g.roundingMode),currency:e})}))},equalsTo:function(e){return this.hasSameAmount(e)&&this.hasSameCurrency(e)},lessThan:function(t){y.call(this,t);var n=e.normalizePrecision([this,t]);return n[0].getAmount()<n[1].getAmount()},lessThanOrEqual:function(t){y.call(this,t);var n=e.normalizePrecision([this,t]);return n[0].getAmount()<=n[1].getAmount()},greaterThan:function(t){y.call(this,t);var n=e.normalizePrecision([this,t]);return n[0].getAmount()>n[1].getAmount()},greaterThanOrEqual:function(t){y.call(this,t);var n=e.normalizePrecision([this,t]);return n[0].getAmount()>=n[1].getAmount()},isZero:function(){return 0===this.getAmount()},isPositive:function(){return this.getAmount()>=0},isNegative:function(){return this.getAmount()<0},hasSubUnits:function(){return 0!==w.modulo(this.getAmount(),Math.pow(10,i))},hasCents:function(){return 0!==w.modulo(this.getAmount(),Math.pow(10,i))},hasSameCurrency:function(e){return this.getCurrency()===e.getCurrency()},hasSameAmount:function(t){var n=e.normalizePrecision([this,t]);return n[0].getAmount()===n[1].getAmount()},toFormat:function(){var e,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u,n=(e=/^(?:(\$|USD)?0(?:(,)0)?(\.)?(0+)?|0(?:(,)0)?(\.)?(0+)?\s?(dollar)?)$/gm.exec(arguments.length>0&&void 0!==arguments[0]?arguments[0]:c),{getMatches:function(){return null!==e?e.slice(1).filter((function(e){return!d(e)})):[]},getMinimumFractionDigits:function(){var e=function(e){return"."===e};return d(this.getMatches().find(e))?0:this.getMatches()[m.add(this.getMatches().findIndex(e),1)].split("").length},getCurrencyDisplay:function(){return{USD:"code",dollar:"name",$:"symbol"}[this.getMatches().find((function(e){return"USD"===e||"dollar"===e||"$"===e}))]},getStyle:function(){return d(this.getCurrencyDisplay(this.getMatches()))?"decimal":"currency"},getUseGrouping:function(){return!d(this.getMatches().find((function(e){return","===e})))}});return this.toRoundedUnit(n.getMinimumFractionDigits(),t).toLocaleString(this.getLocale(),{currencyDisplay:n.getCurrencyDisplay(),useGrouping:n.getUseGrouping(),minimumFractionDigits:n.getMinimumFractionDigits(),style:n.getStyle(),currency:this.getCurrency()})},toUnit:function(){return w.divide(this.getAmount(),Math.pow(10,i))},toRoundedUnit:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:u,n=Math.pow(10,e);return w.divide(w.round(w.multiply(this.toUnit(),n),t),n)},toObject:function(){return{amount:r,currency:o,precision:i}},toJSON:function(){return this.toObject()}}}),{defaultAmount:0,defaultCurrency:"USD",defaultPrecision:2},{globalLocale:"en-US",globalFormat:"$0,0.00",globalRoundingMode:"HALF_EVEN",globalFormatRoundingMode:"HALF_AWAY_FROM_ZERO",globalExchangeRatesApi:{endpoint:void 0,headers:void 0,propertyPath:void 0}},s);const b=/^(1028|251|7949)$/.test(n.j)?y:null},9571:(e,t,n)=>{"use strict";var r=n(2984);e.exports=function(e,t,n){n=n||{},9===t.nodeType&&(t=r.getWindow(t));var o=n.allowHorizontalScroll,i=n.onlyScrollIfNeeded,s=n.alignWithTop,a=n.alignWithLeft,c=n.offsetTop||0,l=n.offsetLeft||0,u=n.offsetBottom||0,d=n.offsetRight||0;o=void 0===o||o;var f=r.isWindow(t),p=r.offset(e),m=r.outerHeight(e),h=r.outerWidth(e),v=void 0,g=void 0,w=void 0,y=void 0,b=void 0,E=void 0,C=void 0,x=void 0,A=void 0,S=void 0;f?(C=t,S=r.height(C),A=r.width(C),x={left:r.scrollLeft(C),top:r.scrollTop(C)},b={left:p.left-x.left-l,top:p.top-x.top-c},E={left:p.left+h-(x.left+A)+d,top:p.top+m-(x.top+S)+u},y=x):(v=r.offset(t),g=t.clientHeight,w=t.clientWidth,y={left:t.scrollLeft,top:t.scrollTop},b={left:p.left-(v.left+(parseFloat(r.css(t,"borderLeftWidth"))||0))-l,top:p.top-(v.top+(parseFloat(r.css(t,"borderTopWidth"))||0))-c},E={left:p.left+h-(v.left+w+(parseFloat(r.css(t,"borderRightWidth"))||0))+d,top:p.top+m-(v.top+g+(parseFloat(r.css(t,"borderBottomWidth"))||0))+u}),b.top<0||E.top>0?!0===s?r.scrollTop(t,y.top+b.top):!1===s?r.scrollTop(t,y.top+E.top):b.top<0?r.scrollTop(t,y.top+b.top):r.scrollTop(t,y.top+E.top):i||((s=void 0===s||!!s)?r.scrollTop(t,y.top+b.top):r.scrollTop(t,y.top+E.top)),o&&(b.left<0||E.left>0?!0===a?r.scrollLeft(t,y.left+b.left):!1===a?r.scrollLeft(t,y.left+E.left):b.left<0?r.scrollLeft(t,y.left+b.left):r.scrollLeft(t,y.left+E.left):i||((a=void 0===a||!!a)?r.scrollLeft(t,y.left+b.left):r.scrollLeft(t,y.left+E.left)))}},6406:(e,t,n)=>{"use strict";e.exports=n(9571)},2984:e=>{"use strict";var t=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e};function r(e,t){var n=e["page"+(t?"Y":"X")+"Offset"],r="scroll"+(t?"Top":"Left");if("number"!=typeof n){var o=e.document;"number"!=typeof(n=o.documentElement[r])&&(n=o.body[r])}return n}function o(e){return r(e)}function i(e){return r(e,!0)}function s(e){var t=function(e){var t,n=void 0,r=void 0,o=e.ownerDocument,i=o.body,s=o&&o.documentElement;return n=(t=e.getBoundingClientRect()).left,r=t.top,{left:n-=s.clientLeft||i.clientLeft||0,top:r-=s.clientTop||i.clientTop||0}}(e),n=e.ownerDocument,r=n.defaultView||n.parentWindow;return t.left+=o(r),t.top+=i(r),t}var a=new RegExp("^("+/[\-+]?(?:\d*\.|)\d+(?:[eE][\-+]?\d+|)/.source+")(?!px)[a-z%]+$","i"),c=/^(top|right|bottom|left)$/,l="currentStyle",u="runtimeStyle",d="left",f=void 0;function p(e,t){for(var n=0;n<e.length;n++)t(e[n])}function m(e){return"border-box"===f(e,"boxSizing")}"undefined"!=typeof window&&(f=window.getComputedStyle?function(e,t,n){var r="",o=e.ownerDocument,i=n||o.defaultView.getComputedStyle(e,null);return i&&(r=i.getPropertyValue(t)||i[t]),r}:function(e,t){var n=e[l]&&e[l][t];if(a.test(n)&&!c.test(t)){var r=e.style,o=r[d],i=e[u][d];e[u][d]=e[l][d],r[d]="fontSize"===t?"1em":n||0,n=r.pixelLeft+"px",r[d]=o,e[u][d]=i}return""===n?"auto":n});var h=["margin","border","padding"];function v(e,t,n){var r=0,o=void 0,i=void 0,s=void 0;for(i=0;i<t.length;i++)if(o=t[i])for(s=0;s<n.length;s++){var a;a="border"===o?o+n[s]+"Width":o+n[s],r+=parseFloat(f(e,a))||0}return r}function g(e){return null!=e&&e==e.window}var w={};function y(e,t,n){if(g(e))return"width"===t?w.viewportWidth(e):w.viewportHeight(e);if(9===e.nodeType)return"width"===t?w.docWidth(e):w.docHeight(e);var r="width"===t?["Left","Right"]:["Top","Bottom"],o="width"===t?e.offsetWidth:e.offsetHeight,i=(f(e),m(e)),s=0;(null==o||o<=0)&&(o=void 0,(null==(s=f(e,t))||Number(s)<0)&&(s=e.style[t]||0),s=parseFloat(s)||0),void 0===n&&(n=i?1:-1);var a=void 0!==o||i,c=o||s;if(-1===n)return a?c-v(e,["border","padding"],r):s;if(a){var l=2===n?-v(e,["border"],r):v(e,["margin"],r);return c+(1===n?0:l)}return s+v(e,h.slice(n),r)}p(["Width","Height"],(function(e){w["doc"+e]=function(t){var n=t.document;return Math.max(n.documentElement["scroll"+e],n.body["scroll"+e],w["viewport"+e](n))},w["viewport"+e]=function(t){var n="client"+e,r=t.document,o=r.body,i=r.documentElement[n];return"CSS1Compat"===r.compatMode&&i||o&&o[n]||i}}));var b={position:"absolute",visibility:"hidden",display:"block"};function E(e){var t=void 0,n=arguments;return 0!==e.offsetWidth?t=y.apply(void 0,n):function(e,r){var o={},i=e.style,s=void 0;for(s in r)r.hasOwnProperty(s)&&(o[s]=i[s],i[s]=r[s]);for(s in function(){t=y.apply(void 0,n)}.call(e),r)r.hasOwnProperty(s)&&(i[s]=o[s])}(e,b),t}function C(e,t,r){var o=r;if("object"!==(void 0===t?"undefined":n(t)))return void 0!==o?("number"==typeof o&&(o+="px"),void(e.style[t]=o)):f(e,t);for(var i in t)t.hasOwnProperty(i)&&C(e,i,t[i])}p(["width","height"],(function(e){var t=e.charAt(0).toUpperCase()+e.slice(1);w["outer"+t]=function(t,n){return t&&E(t,e,n?0:1)};var n="width"===e?["Left","Right"]:["Top","Bottom"];w[e]=function(t,r){return void 0===r?t&&E(t,e,-1):t?(f(t),m(t)&&(r+=v(t,["padding","border"],n)),C(t,e,r)):void 0}})),e.exports=t({getWindow:function(e){var t=e.ownerDocument||e;return t.defaultView||t.parentWindow},offset:function(e,t){if(void 0===t)return s(e);!function(e,t){"static"===C(e,"position")&&(e.style.position="relative");var n=s(e),r={},o=void 0,i=void 0;for(i in t)t.hasOwnProperty(i)&&(o=parseFloat(C(e,i))||0,r[i]=o+t[i]-n[i]);C(e,r)}(e,t)},isWindow:g,each:p,css:C,clone:function(e){var t={};for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);if(e.overflow)for(var n in e)e.hasOwnProperty(n)&&(t.overflow[n]=e.overflow[n]);return t},scrollLeft:function(e,t){if(g(e)){if(void 0===t)return o(e);window.scrollTo(t,i(e))}else{if(void 0===t)return e.scrollLeft;e.scrollLeft=t}},scrollTop:function(e,t){if(g(e)){if(void 0===t)return i(e);window.scrollTo(o(e),t)}else{if(void 0===t)return e.scrollTop;e.scrollTop=t}},viewportWidth:0,viewportHeight:0},w)},3240:function(e){e.exports=function(){"use strict";function e(t){return e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e(t)}function t(e,n){return t=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},t(e,n)}function n(e,r,o){return n=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}()?Reflect.construct:function(e,n,r){var o=[null];o.push.apply(o,n);var i=new(Function.bind.apply(e,o));return r&&t(i,r.prototype),i},n.apply(null,arguments)}function r(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var i=Object.hasOwnProperty,s=Object.setPrototypeOf,a=Object.isFrozen,c=Object.getPrototypeOf,l=Object.getOwnPropertyDescriptor,u=Object.freeze,d=Object.seal,f=Object.create,p="undefined"!=typeof Reflect&&Reflect,m=p.apply,h=p.construct;m||(m=function(e,t,n){return e.apply(t,n)}),u||(u=function(e){return e}),d||(d=function(e){return e}),h||(h=function(e,t){return n(e,r(t))});var v,g=k(Array.prototype.forEach),w=k(Array.prototype.pop),y=k(Array.prototype.push),b=k(String.prototype.toLowerCase),E=k(String.prototype.toString),C=k(String.prototype.match),x=k(String.prototype.replace),A=k(String.prototype.indexOf),S=k(String.prototype.trim),M=k(RegExp.prototype.test),T=(v=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return h(v,t)});function k(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return m(e,t,r)}}function L(e,t,n){var r;n=null!==(r=n)&&void 0!==r?r:b,s&&s(e,null);for(var o=t.length;o--;){var i=t[o];if("string"==typeof i){var c=n(i);c!==i&&(a(t)||(t[o]=c),i=c)}e[i]=!0}return e}function V(e){var t,n=f(null);for(t in e)!0===m(i,e,[t])&&(n[t]=e[t]);return n}function z(e,t){for(;null!==e;){var n=l(e,t);if(n){if(n.get)return k(n.get);if("function"==typeof n.value)return k(n.value)}e=c(e)}return function(e){return console.warn("fallback value for",e),null}}var O=u(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),F=u(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),R=u(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),N=u(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),_=u(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),H=u(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),I=u(["#text"]),P=u(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),j=u(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),D=u(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),B=u(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),$=d(/\{\{[\w\W]*|[\w\W]*\}\}/gm),G=d(/<%[\w\W]*|[\w\W]*%>/gm),W=d(/\${[\w\W]*}/gm),U=d(/^data-[\-\w.\u00B7-\uFFFF]/),Z=d(/^aria-[\-\w]+$/),K=d(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),q=d(/^(?:\w+script|data):/i),Y=d(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),J=d(/^html$/i),X=d(/^[a-z][.\w]*(-[.\w]+)+$/i),Q=function(){return"undefined"==typeof window?null:window};return function t(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Q(),o=function(e){return t(e)};if(o.version="2.5.7",o.removed=[],!n||!n.document||9!==n.document.nodeType)return o.isSupported=!1,o;var i=n.document,s=n.document,a=n.DocumentFragment,c=n.HTMLTemplateElement,l=n.Node,d=n.Element,f=n.NodeFilter,p=n.NamedNodeMap,m=void 0===p?n.NamedNodeMap||n.MozNamedAttrMap:p,h=n.HTMLFormElement,v=n.DOMParser,k=n.trustedTypes,ee=d.prototype,te=z(ee,"cloneNode"),ne=z(ee,"nextSibling"),re=z(ee,"childNodes"),oe=z(ee,"parentNode");if("function"==typeof c){var ie=s.createElement("template");ie.content&&ie.content.ownerDocument&&(s=ie.content.ownerDocument)}var se=function(t,n){if("object"!==e(t)||"function"!=typeof t.createPolicy)return null;var r=null,o="data-tt-policy-suffix";n.currentScript&&n.currentScript.hasAttribute(o)&&(r=n.currentScript.getAttribute(o));var i="dompurify"+(r?"#"+r:"");try{return t.createPolicy(i,{createHTML:function(e){return e},createScriptURL:function(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+i+" could not be created."),null}}(k,i),ae=se?se.createHTML(""):"",ce=s,le=ce.implementation,ue=ce.createNodeIterator,de=ce.createDocumentFragment,fe=ce.getElementsByTagName,pe=i.importNode,me={};try{me=V(s).documentMode?s.documentMode:{}}catch(e){}var he={};o.isSupported="function"==typeof oe&&le&&void 0!==le.createHTMLDocument&&9!==me;var ve,ge,we=$,ye=G,be=W,Ee=U,Ce=Z,xe=q,Ae=Y,Se=X,Me=K,Te=null,ke=L({},[].concat(r(O),r(F),r(R),r(_),r(I))),Le=null,Ve=L({},[].concat(r(P),r(j),r(D),r(B))),ze=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Oe=null,Fe=null,Re=!0,Ne=!0,_e=!1,He=!0,Ie=!1,Pe=!0,je=!1,De=!1,Be=!1,$e=!1,Ge=!1,We=!1,Ue=!0,Ze=!1,Ke=!0,qe=!1,Ye={},Je=null,Xe=L({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),Qe=null,et=L({},["audio","video","img","source","image","track"]),tt=null,nt=L({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),rt="http://www.w3.org/1998/Math/MathML",ot="http://www.w3.org/2000/svg",it="http://www.w3.org/1999/xhtml",st=it,at=!1,ct=null,lt=L({},[rt,ot,it],E),ut=["application/xhtml+xml","text/html"],dt=null,ft=s.createElement("form"),pt=function(e){return e instanceof RegExp||e instanceof Function},mt=function(t){dt&&dt===t||(t&&"object"===e(t)||(t={}),t=V(t),ve=ve=-1===ut.indexOf(t.PARSER_MEDIA_TYPE)?"text/html":t.PARSER_MEDIA_TYPE,ge="application/xhtml+xml"===ve?E:b,Te="ALLOWED_TAGS"in t?L({},t.ALLOWED_TAGS,ge):ke,Le="ALLOWED_ATTR"in t?L({},t.ALLOWED_ATTR,ge):Ve,ct="ALLOWED_NAMESPACES"in t?L({},t.ALLOWED_NAMESPACES,E):lt,tt="ADD_URI_SAFE_ATTR"in t?L(V(nt),t.ADD_URI_SAFE_ATTR,ge):nt,Qe="ADD_DATA_URI_TAGS"in t?L(V(et),t.ADD_DATA_URI_TAGS,ge):et,Je="FORBID_CONTENTS"in t?L({},t.FORBID_CONTENTS,ge):Xe,Oe="FORBID_TAGS"in t?L({},t.FORBID_TAGS,ge):{},Fe="FORBID_ATTR"in t?L({},t.FORBID_ATTR,ge):{},Ye="USE_PROFILES"in t&&t.USE_PROFILES,Re=!1!==t.ALLOW_ARIA_ATTR,Ne=!1!==t.ALLOW_DATA_ATTR,_e=t.ALLOW_UNKNOWN_PROTOCOLS||!1,He=!1!==t.ALLOW_SELF_CLOSE_IN_ATTR,Ie=t.SAFE_FOR_TEMPLATES||!1,Pe=!1!==t.SAFE_FOR_XML,je=t.WHOLE_DOCUMENT||!1,$e=t.RETURN_DOM||!1,Ge=t.RETURN_DOM_FRAGMENT||!1,We=t.RETURN_TRUSTED_TYPE||!1,Be=t.FORCE_BODY||!1,Ue=!1!==t.SANITIZE_DOM,Ze=t.SANITIZE_NAMED_PROPS||!1,Ke=!1!==t.KEEP_CONTENT,qe=t.IN_PLACE||!1,Me=t.ALLOWED_URI_REGEXP||Me,st=t.NAMESPACE||it,ze=t.CUSTOM_ELEMENT_HANDLING||{},t.CUSTOM_ELEMENT_HANDLING&&pt(t.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(ze.tagNameCheck=t.CUSTOM_ELEMENT_HANDLING.tagNameCheck),t.CUSTOM_ELEMENT_HANDLING&&pt(t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(ze.attributeNameCheck=t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),t.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(ze.allowCustomizedBuiltInElements=t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Ie&&(Ne=!1),Ge&&($e=!0),Ye&&(Te=L({},r(I)),Le=[],!0===Ye.html&&(L(Te,O),L(Le,P)),!0===Ye.svg&&(L(Te,F),L(Le,j),L(Le,B)),!0===Ye.svgFilters&&(L(Te,R),L(Le,j),L(Le,B)),!0===Ye.mathMl&&(L(Te,_),L(Le,D),L(Le,B))),t.ADD_TAGS&&(Te===ke&&(Te=V(Te)),L(Te,t.ADD_TAGS,ge)),t.ADD_ATTR&&(Le===Ve&&(Le=V(Le)),L(Le,t.ADD_ATTR,ge)),t.ADD_URI_SAFE_ATTR&&L(tt,t.ADD_URI_SAFE_ATTR,ge),t.FORBID_CONTENTS&&(Je===Xe&&(Je=V(Je)),L(Je,t.FORBID_CONTENTS,ge)),Ke&&(Te["#text"]=!0),je&&L(Te,["html","head","body"]),Te.table&&(L(Te,["tbody"]),delete Oe.tbody),u&&u(t),dt=t)},ht=L({},["mi","mo","mn","ms","mtext"]),vt=L({},["annotation-xml"]),gt=L({},["title","style","font","a","script"]),wt=L({},F);L(wt,R),L(wt,N);var yt=L({},_);L(yt,H);var bt=function(e){y(o.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){try{e.outerHTML=ae}catch(t){e.remove()}}},Et=function(e,t){try{y(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){y(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!Le[e])if($e||Ge)try{bt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Ct=function(e){var t,n;if(Be)e="<remove></remove>"+e;else{var r=C(e,/^[\r\n\t ]+/);n=r&&r[0]}"application/xhtml+xml"===ve&&st===it&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");var o=se?se.createHTML(e):e;if(st===it)try{t=(new v).parseFromString(o,ve)}catch(e){}if(!t||!t.documentElement){t=le.createDocument(st,"template",null);try{t.documentElement.innerHTML=at?ae:o}catch(e){}}var i=t.body||t.documentElement;return e&&n&&i.insertBefore(s.createTextNode(n),i.childNodes[0]||null),st===it?fe.call(t,je?"html":"body")[0]:je?t.documentElement:i},xt=function(e){return ue.call(e.ownerDocument||e,e,f.SHOW_ELEMENT|f.SHOW_COMMENT|f.SHOW_TEXT|f.SHOW_PROCESSING_INSTRUCTION|f.SHOW_CDATA_SECTION,null,!1)},At=function(e){return e instanceof h&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof m)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},St=function(t){return"object"===e(l)?t instanceof l:t&&"object"===e(t)&&"number"==typeof t.nodeType&&"string"==typeof t.nodeName},Mt=function(e,t,n){he[e]&&g(he[e],(function(e){e.call(o,t,n,dt)}))},Tt=function(e){var t;if(Mt("beforeSanitizeElements",e,null),At(e))return bt(e),!0;if(M(/[\u0080-\uFFFF]/,e.nodeName))return bt(e),!0;var n=ge(e.nodeName);if(Mt("uponSanitizeElement",e,{tagName:n,allowedTags:Te}),e.hasChildNodes()&&!St(e.firstElementChild)&&(!St(e.content)||!St(e.content.firstElementChild))&&M(/<[/\w]/g,e.innerHTML)&&M(/<[/\w]/g,e.textContent))return bt(e),!0;if("select"===n&&M(/<template/i,e.innerHTML))return bt(e),!0;if(7===e.nodeType)return bt(e),!0;if(Pe&&8===e.nodeType&&M(/<[/\w]/g,e.data))return bt(e),!0;if(!Te[n]||Oe[n]){if(!Oe[n]&&Lt(n)){if(ze.tagNameCheck instanceof RegExp&&M(ze.tagNameCheck,n))return!1;if(ze.tagNameCheck instanceof Function&&ze.tagNameCheck(n))return!1}if(Ke&&!Je[n]){var r=oe(e)||e.parentNode,i=re(e)||e.childNodes;if(i&&r)for(var s=i.length-1;s>=0;--s){var a=te(i[s],!0);a.__removalCount=(e.__removalCount||0)+1,r.insertBefore(a,ne(e))}}return bt(e),!0}return e instanceof d&&!function(e){var t=oe(e);t&&t.tagName||(t={namespaceURI:st,tagName:"template"});var n=b(e.tagName),r=b(t.tagName);return!!ct[e.namespaceURI]&&(e.namespaceURI===ot?t.namespaceURI===it?"svg"===n:t.namespaceURI===rt?"svg"===n&&("annotation-xml"===r||ht[r]):Boolean(wt[n]):e.namespaceURI===rt?t.namespaceURI===it?"math"===n:t.namespaceURI===ot?"math"===n&&vt[r]:Boolean(yt[n]):e.namespaceURI===it?!(t.namespaceURI===ot&&!vt[r])&&!(t.namespaceURI===rt&&!ht[r])&&!yt[n]&&(gt[n]||!wt[n]):!("application/xhtml+xml"!==ve||!ct[e.namespaceURI]))}(e)?(bt(e),!0):"noscript"!==n&&"noembed"!==n&&"noframes"!==n||!M(/<\/no(script|embed|frames)/i,e.innerHTML)?(Ie&&3===e.nodeType&&(t=e.textContent,t=x(t,we," "),t=x(t,ye," "),t=x(t,be," "),e.textContent!==t&&(y(o.removed,{element:e.cloneNode()}),e.textContent=t)),Mt("afterSanitizeElements",e,null),!1):(bt(e),!0)},kt=function(e,t,n){if(Ue&&("id"===t||"name"===t)&&(n in s||n in ft))return!1;if(Ne&&!Fe[t]&&M(Ee,t));else if(Re&&M(Ce,t));else if(!Le[t]||Fe[t]){if(!(Lt(e)&&(ze.tagNameCheck instanceof RegExp&&M(ze.tagNameCheck,e)||ze.tagNameCheck instanceof Function&&ze.tagNameCheck(e))&&(ze.attributeNameCheck instanceof RegExp&&M(ze.attributeNameCheck,t)||ze.attributeNameCheck instanceof Function&&ze.attributeNameCheck(t))||"is"===t&&ze.allowCustomizedBuiltInElements&&(ze.tagNameCheck instanceof RegExp&&M(ze.tagNameCheck,n)||ze.tagNameCheck instanceof Function&&ze.tagNameCheck(n))))return!1}else if(tt[t]);else if(M(Me,x(n,Ae,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==A(n,"data:")||!Qe[e])if(_e&&!M(xe,x(n,Ae,"")));else if(n)return!1;return!0},Lt=function(e){return"annotation-xml"!==e&&C(e,Se)},Vt=function(t){var n,r,i,s;Mt("beforeSanitizeAttributes",t,null);var a=t.attributes;if(a){var c={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Le};for(s=a.length;s--;){var l=n=a[s],u=l.name,d=l.namespaceURI;if(r="value"===u?n.value:S(n.value),i=ge(u),c.attrName=i,c.attrValue=r,c.keepAttr=!0,c.forceKeepAttr=void 0,Mt("uponSanitizeAttribute",t,c),r=c.attrValue,!c.forceKeepAttr&&(Et(u,t),c.keepAttr))if(He||!M(/\/>/i,r)){Ie&&(r=x(r,we," "),r=x(r,ye," "),r=x(r,be," "));var f=ge(t.nodeName);if(kt(f,i,r))if(!Ze||"id"!==i&&"name"!==i||(Et(u,t),r="user-content-"+r),Pe&&M(/((--!?|])>)|<\/(style|title)/i,r))Et(u,t);else{if(se&&"object"===e(k)&&"function"==typeof k.getAttributeType)if(d);else switch(k.getAttributeType(f,i)){case"TrustedHTML":r=se.createHTML(r);break;case"TrustedScriptURL":r=se.createScriptURL(r)}try{d?t.setAttributeNS(d,u,r):t.setAttribute(u,r),At(t)?bt(t):w(o.removed)}catch(e){}}}else Et(u,t)}Mt("afterSanitizeAttributes",t,null)}},zt=function e(t){var n,r=xt(t);for(Mt("beforeSanitizeShadowDOM",t,null);n=r.nextNode();)Mt("uponSanitizeShadowNode",n,null),Tt(n)||(n.content instanceof a&&e(n.content),Vt(n));Mt("afterSanitizeShadowDOM",t,null)};return o.sanitize=function(t){var r,s,c,u,d,f=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if((at=!t)&&(t="\x3c!--\x3e"),"string"!=typeof t&&!St(t)){if("function"!=typeof t.toString)throw T("toString is not a function");if("string"!=typeof(t=t.toString()))throw T("dirty is not a string, aborting")}if(!o.isSupported){if("object"===e(n.toStaticHTML)||"function"==typeof n.toStaticHTML){if("string"==typeof t)return n.toStaticHTML(t);if(St(t))return n.toStaticHTML(t.outerHTML)}return t}if(De||mt(f),o.removed=[],"string"==typeof t&&(qe=!1),qe){if(t.nodeName){var p=ge(t.nodeName);if(!Te[p]||Oe[p])throw T("root node is forbidden and cannot be sanitized in-place")}}else if(t instanceof l)1===(s=(r=Ct("\x3c!----\x3e")).ownerDocument.importNode(t,!0)).nodeType&&"BODY"===s.nodeName||"HTML"===s.nodeName?r=s:r.appendChild(s);else{if(!$e&&!Ie&&!je&&-1===t.indexOf("<"))return se&&We?se.createHTML(t):t;if(!(r=Ct(t)))return $e?null:We?ae:""}r&&Be&&bt(r.firstChild);for(var m=xt(qe?t:r);c=m.nextNode();)3===c.nodeType&&c===u||Tt(c)||(c.content instanceof a&&zt(c.content),Vt(c),u=c);if(u=null,qe)return t;if($e){if(Ge)for(d=de.call(r.ownerDocument);r.firstChild;)d.appendChild(r.firstChild);else d=r;return(Le.shadowroot||Le.shadowrootmod)&&(d=pe.call(i,d,!0)),d}var h=je?r.outerHTML:r.innerHTML;return je&&Te["!doctype"]&&r.ownerDocument&&r.ownerDocument.doctype&&r.ownerDocument.doctype.name&&M(J,r.ownerDocument.doctype.name)&&(h="<!DOCTYPE "+r.ownerDocument.doctype.name+">\n"+h),Ie&&(h=x(h,we," "),h=x(h,ye," "),h=x(h,be," ")),se&&We?se.createHTML(h):h},o.setConfig=function(e){mt(e),De=!0},o.clearConfig=function(){dt=null,De=!1},o.isValidAttribute=function(e,t,n){dt||mt({});var r=ge(e),o=ge(t);return kt(r,o,n)},o.addHook=function(e,t){"function"==typeof t&&(he[e]=he[e]||[],y(he[e],t))},o.removeHook=function(e){if(he[e])return w(he[e])},o.removeHooks=function(e){he[e]&&(he[e]=[])},o.removeAllHooks=function(){he={}},o}()}()},4420:(e,t,n)=>{"use strict";if(n.d(t,{a:()=>i}),/^(10(01|28)|251|5065|7949|8931|9759|9918)$/.test(n.j))var r=n(5029);if(/^(10(01|28)|251|5065|7949|8931|9759|9918)$/.test(n.j))var o=n(4738);function i(e,t){return void 0===t&&(t={}),(0,o.W)(e,(0,r.Cl)({delimiter:"."},t))}},1824:e=>{"use strict";e.exports=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var r,o,i;if(Array.isArray(t)){if((r=t.length)!=n.length)return!1;for(o=r;0!=o--;)if(!e(t[o],n[o]))return!1;return!0}if(t instanceof Map&&n instanceof Map){if(t.size!==n.size)return!1;for(o of t.entries())if(!n.has(o[0]))return!1;for(o of t.entries())if(!e(o[1],n.get(o[0])))return!1;return!0}if(t instanceof Set&&n instanceof Set){if(t.size!==n.size)return!1;for(o of t.entries())if(!n.has(o[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(n)){if((r=t.length)!=n.length)return!1;for(o=r;0!=o--;)if(t[o]!==n[o])return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((r=(i=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(o=r;0!=o--;)if(!Object.prototype.hasOwnProperty.call(n,i[o]))return!1;for(o=r;0!=o--;){var s=i[o];if(!e(t[s],n[s]))return!1}return!0}return t!=t&&n!=n}},385:(e,t,n)=>{"use strict";function r(e){return e.toLowerCase()}n.d(t,{g:()=>r})},9295:e=>{var t=1e3,n=60*t,r=60*n,o=24*r,i=7*o;function s(e,t,n,r){var o=t>=1.5*n;return Math.round(e/n)+" "+r+(o?"s":"")}e.exports=function(e,a){a=a||{};var c,l,u=typeof e;if("string"===u&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var s=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(s){var a=parseFloat(s[1]);switch((s[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*a;case"weeks":case"week":case"w":return a*i;case"days":case"day":case"d":return a*o;case"hours":case"hour":case"hrs":case"hr":case"h":return a*r;case"minutes":case"minute":case"mins":case"min":case"m":return a*n;case"seconds":case"second":case"secs":case"sec":case"s":return a*t;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return a;default:return}}}}(e);if("number"===u&&isFinite(e))return a.long?(c=e,(l=Math.abs(c))>=o?s(c,l,o,"day"):l>=r?s(c,l,r,"hour"):l>=n?s(c,l,n,"minute"):l>=t?s(c,l,t,"second"):c+" ms"):function(e){var i=Math.abs(e);return i>=o?Math.round(e/o)+"d":i>=r?Math.round(e/r)+"h":i>=n?Math.round(e/n)+"m":i>=t?Math.round(e/t)+"s":e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},4738:(e,t,n)=>{"use strict";if(n.d(t,{W:()=>s}),/^(10(01|28)|251|5065|7949|8931|9759|9918)$/.test(n.j))var r=n(385);var o=/^(10(01|28)|251|5065|7949|8931|9759|9918)$/.test(n.j)?[/([a-z0-9])([A-Z])/g,/([A-Z])([A-Z][a-z])/g]:null,i=/[^A-Z0-9]+/gi;function s(e,t){void 0===t&&(t={});for(var n=t.splitRegexp,s=void 0===n?o:n,c=t.stripRegexp,l=void 0===c?i:c,u=t.transform,d=void 0===u?r.g:u,f=t.delimiter,p=void 0===f?" ":f,m=a(a(e,s,"$1\0$2"),l,"\0"),h=0,v=m.length;"\0"===m.charAt(h);)h++;for(;"\0"===m.charAt(v-1);)v--;return m.slice(h,v).split("\0").map(d).join(p)}function a(e,t,n){return t instanceof RegExp?e.replace(t,n):t.reduce((function(e,t){return e.replace(t,n)}),e)}},7356:(e,t,n)=>{"use strict";if(n.d(t,{c:()=>i}),/^(10(01|28)|251|5065|7949|8931|9759|9918)$/.test(n.j))var r=n(5029);if(/^(10(01|28)|251|5065|7949|8931|9759|9918)$/.test(n.j))var o=n(4420);function i(e,t){return void 0===t&&(t={}),(0,o.a)(e,(0,r.Cl)({delimiter:"-"},t))}},7740:(e,t,n)=>{"use strict";if(n.d(t,{L:()=>i}),251==n.j)var r=n(5029);if(251==n.j)var o=n(4420);function i(e,t){return void 0===t&&(t={}),(0,o.a)(e,(0,r.Cl)({delimiter:"_"},t))}},4347:(e,t,n)=>{"use strict";n.d(t,{YQ:()=>o,d7:()=>a,dh:()=>c});var r=n(1609);function o(e,t,n){var o=this,i=(0,r.useRef)(null),s=(0,r.useRef)(0),a=(0,r.useRef)(null),c=(0,r.useRef)([]),l=(0,r.useRef)(),u=(0,r.useRef)(),d=(0,r.useRef)(e),f=(0,r.useRef)(!0);(0,r.useEffect)((function(){d.current=e}),[e]);var p=!t&&0!==t&&"undefined"!=typeof window;if("function"!=typeof e)throw new TypeError("Expected a function");t=+t||0;var m=!!(n=n||{}).leading,h=!("trailing"in n)||!!n.trailing,v="maxWait"in n,g=v?Math.max(+n.maxWait||0,t):null;(0,r.useEffect)((function(){return f.current=!0,function(){f.current=!1}}),[]);var w=(0,r.useMemo)((function(){var e=function(e){var t=c.current,n=l.current;return c.current=l.current=null,s.current=e,u.current=d.current.apply(n,t)},n=function(e,t){p&&cancelAnimationFrame(a.current),a.current=p?requestAnimationFrame(e):setTimeout(e,t)},r=function(e){if(!f.current)return!1;var n=e-i.current;return!i.current||n>=t||n<0||v&&e-s.current>=g},w=function(t){return a.current=null,h&&c.current?e(t):(c.current=l.current=null,u.current)},y=function e(){var o=Date.now();if(r(o))return w(o);if(f.current){var a=t-(o-i.current),c=v?Math.min(a,g-(o-s.current)):a;n(e,c)}},b=function(){var d=Date.now(),p=r(d);if(c.current=[].slice.call(arguments),l.current=o,i.current=d,p){if(!a.current&&f.current)return s.current=i.current,n(y,t),m?e(i.current):u.current;if(v)return n(y,t),e(i.current)}return a.current||n(y,t),u.current};return b.cancel=function(){a.current&&(p?cancelAnimationFrame(a.current):clearTimeout(a.current)),s.current=0,c.current=i.current=l.current=a.current=null},b.isPending=function(){return!!a.current},b.flush=function(){return a.current?w(Date.now()):u.current},b}),[m,v,t,g,h,p]);return w}function i(e,t){return e===t}function s(e){return"function"==typeof e?function(){return e}:e}function a(e,t,n){var a,c,l=n&&n.equalityFn||i,u=(a=(0,r.useState)(s(e)),c=a[1],[a[0],(0,r.useCallback)((function(e){return c(s(e))}),[])]),d=u[0],f=u[1],p=o((0,r.useCallback)((function(e){return f(e)}),[f]),t,n),m=(0,r.useRef)(e);return l(m.current,e)||(p(e),m.current=e),[d,p]}function c(e,t,n){var r=void 0===n?{}:n,i=r.leading,s=r.trailing;return o(e,t,{maxWait:t,leading:void 0===i||i,trailing:void 0===s||s})}},5929:(e,t,n)=>{"use strict";n.d(t,{Su:()=>o});var r=n(1609);const o=function(e){(0,r.useEffect)(e,[])};"undefined"!=typeof window?r.useLayoutEffect:r.useEffect},6985:(e,t,n)=>{"use strict";function r(e,t){return"function"==typeof Object.hasOwn?Object.hasOwn(e,t):Object.prototype.hasOwnProperty.call(e,t)}function o(e){return e.disabled||!0===e["aria-disabled"]||"true"===e["aria-disabled"]}function i(e){const t={};for(const n in e)void 0!==e[n]&&(t[n]=e[n]);return t}n.d(t,{$f:()=>o,HR:()=>i,mQ:()=>r})},754:(e,t,n)=>{"use strict";n.d(t,{Bm:()=>c,Sw:()=>o,bq:()=>s,cK:()=>u,gR:()=>a,kp:()=>f,mB:()=>p,zN:()=>d});var r,o="undefined"!=typeof window&&!!(null==(r=window.document)?void 0:r.createElement);function i(e){return e?e.ownerDocument||e:document}function s(e,t=!1){const{activeElement:n}=i(e);if(!(null==n?void 0:n.nodeName))return null;if("IFRAME"===n.tagName&&n.contentDocument)return s(n.contentDocument.body,t);if(t){const e=n.getAttribute("aria-activedescendant");if(e){const t=i(n).getElementById(e);if(t)return t}}return n}function a(e,t){return e===t||e.contains(t)}function c(e){const t=e.tagName.toLowerCase();return"button"===t||!("input"!==t||!e.type)&&-1!==l.indexOf(e.type)}var l=/^(1028|251|4950|5932|7949)$/.test(n.j)?["button","color","file","image","reset","submit"]:null;function u(e,t){return"matches"in e?e.matches(t):"msMatchesSelector"in e?e.msMatchesSelector(t):e.webkitMatchesSelector(t)}function d(e){const t=e;return t.offsetWidth>0||t.offsetHeight>0||e.getClientRects().length>0}function f(e,t){if("closest"in e)return e.closest(t);do{if(u(e,t))return e;e=e.parentElement||e.parentNode}while(null!==e&&1===e.nodeType);return null}function p(e){try{const t=e instanceof HTMLInputElement&&null!==e.selectionStart,n="TEXTAREA"===e.tagName;return t||n||!1}catch(e){return!1}}},4823:(e,t,n)=>{"use strict";n.d(t,{IA:()=>u,YG:()=>f,ko:()=>d});var r=Object.defineProperty,o=Object.defineProperties,i=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,a=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,l=(e,t,n)=>t in e?r(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,u=(e,t)=>{for(var n in t||(t={}))a.call(t,n)&&l(e,n,t[n]);if(s)for(var n of s(t))c.call(t,n)&&l(e,n,t[n]);return e},d=(e,t)=>o(e,i(t)),f=(e,t)=>{var n={};for(var r in e)a.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&s)for(var r of s(e))t.indexOf(r)<0&&c.call(e,r)&&(n[r]=e[r]);return n}},111:(e,t,n)=>{"use strict";n.d(t,{$:()=>R});var r=n(1609),o=(0,r.createContext)(!0),i=n(611),s=n(4951),a=n(4823),c=n(790);function l(e){const t=r.forwardRef(((t,n)=>e((0,a.ko)((0,a.IA)({},t),{ref:n}))));return t.displayName=e.displayName||e.name,t}function u(e,t){const n=t,{wrapElement:o,render:l}=n,u=(0,a.YG)(n,["wrapElement","render"]),d=(0,i.SV)(t.ref,(0,s.v1)(l));let f;if(r.isValidElement(l)){const e=(0,a.ko)((0,a.IA)({},l.props),{ref:d});f=r.cloneElement(l,(0,s.v6)(u,e))}else f=l?l(u):(0,c.jsx)(e,(0,a.IA)({},u));return o?o(f):f}function d(e){const t=(t={})=>e(t);return t.displayName=e.name,t}var f=n(754);function p(e){return e.target===e.currentTarget}function m(e,t){const n=new MouseEvent("click",t);return e.dispatchEvent(n)}function h(e,t,n){const r=requestAnimationFrame((()=>{e.removeEventListener(t,o,!0),n()})),o=()=>{cancelAnimationFrame(r),n()};return e.addEventListener(t,o,{once:!0,capture:!0}),r}function v(e,t,n,r=window){const o=[];try{r.document.addEventListener(e,t,n);for(const i of Array.from(r.frames))o.push(v(e,t,n,i))}catch(e){}return()=>{try{r.document.removeEventListener(e,t,n)}catch(e){}o.forEach((e=>e()))}}function g(e){return!!(0,f.cK)(e,"input:not([type='hidden']):not([disabled]), select:not([disabled]), textarea:not([disabled]), a[href], button:not([disabled]), [tabindex], summary, iframe, object, embed, area[href], audio[controls], video[controls], [contenteditable]:not([contenteditable='false'])")&&!!(0,f.zN)(e)&&!(0,f.kp)(e,"[inert]")}function w(e){const t=(0,f.bq)(e);if(!t)return!1;if(t===e)return!0;const n=t.getAttribute("aria-activedescendant");return!!n&&n===e.id}var y=n(6985),b=f.Sw&&!!f.Sw&&/mac|iphone|ipad|ipod/i.test(navigator.platform)&&/apple/i.test(navigator.vendor),E=["text","search","url","tel","email","password","number","date","month","week","time","datetime","datetime-local"];function C(e){return!("input"!==e.tagName.toLowerCase()||!e.type||"radio"!==e.type&&"checkbox"!==e.type)}function x(e,t,n,r,o){return e?t?n&&!r?-1:void 0:n?o:o||0:o}function A(e,t){return(0,i._q)((n=>{null==e||e(n),n.defaultPrevented||t&&(n.stopPropagation(),n.preventDefault())}))}var S=!0;function M(e){const t=e.target;t&&"hasAttribute"in t&&(t.hasAttribute("data-focus-visible")||(S=!1))}function T(e){e.metaKey||e.ctrlKey||e.altKey||(S=!0)}var k=d((function(e){var t=e,{focusable:n=!0,accessibleWhenDisabled:s,autoFocus:c,onFocusVisible:l}=t,u=(0,a.YG)(t,["focusable","accessibleWhenDisabled","autoFocus","onFocusVisible"]);const d=(0,r.useRef)(null);(0,r.useEffect)((()=>{n&&(v("mousedown",M,!0),v("keydown",T,!0))}),[n]),b&&(0,r.useEffect)((()=>{if(!n)return;const e=d.current;if(!e)return;if(!C(e))return;const t=function(e){return"labels"in e?e.labels:null}(e);if(!t)return;const r=()=>queueMicrotask((()=>e.focus()));return t.forEach((e=>e.addEventListener("mouseup",r))),()=>{t.forEach((e=>e.removeEventListener("mouseup",r)))}}),[n]);const m=n&&(0,y.$f)(u),k=!!m&&!s,[L,V]=(0,r.useState)(!1);(0,r.useEffect)((()=>{n&&k&&L&&V(!1)}),[n,k,L]),(0,r.useEffect)((()=>{if(!n)return;if(!L)return;const e=d.current;if(!e)return;if("undefined"==typeof IntersectionObserver)return;const t=new IntersectionObserver((()=>{g(e)||V(!1)}));return t.observe(e),()=>t.disconnect()}),[n,L]);const z=A(u.onKeyPressCapture,m),O=A(u.onMouseDownCapture,m),F=A(u.onClickCapture,m),R=u.onMouseDown,N=(0,i._q)((e=>{if(null==R||R(e),e.defaultPrevented)return;if(!n)return;const t=e.currentTarget;if(!b)return;if(function(e){return Boolean(e.currentTarget&&!(0,f.gR)(e.currentTarget,e.target))}(e))return;if(!(0,f.Bm)(t)&&!C(t))return;let r=!1;const o=()=>{r=!0};t.addEventListener("focusin",o,{capture:!0,once:!0}),h(t,"mouseup",(()=>{t.removeEventListener("focusin",o,!0),r||function(e){!function(e){const t=(0,f.bq)(e);if(!t)return!1;if((0,f.gR)(e,t))return!0;const n=t.getAttribute("aria-activedescendant");return!!n&&"id"in e&&(n===e.id||!!e.querySelector(`#${CSS.escape(n)}`))}(e)&&g(e)&&e.focus()}(t)}))})),_=(e,t)=>{if(t&&(e.currentTarget=t),!n)return;const r=e.currentTarget;r&&w(r)&&(null==l||l(e),e.defaultPrevented||V(!0))},H=u.onKeyDownCapture,I=(0,i._q)((e=>{if(null==H||H(e),e.defaultPrevented)return;if(!n)return;if(L)return;if(e.metaKey)return;if(e.altKey)return;if(e.ctrlKey)return;if(!p(e))return;const t=e.currentTarget;queueMicrotask((()=>_(e,t)))})),P=u.onFocusCapture,j=(0,i._q)((e=>{if(null==P||P(e),e.defaultPrevented)return;if(!n)return;if(!p(e))return void V(!1);const t=e.currentTarget,r=()=>_(e,t);S||function(e){const{tagName:t,readOnly:n,type:r}=e;return"TEXTAREA"===t&&!n||"SELECT"===t&&!n||("INPUT"!==t||n?!!e.isContentEditable:E.includes(r))}(e.target)?queueMicrotask(r):function(e){return"combobox"===e.getAttribute("role")&&!!e.dataset.name}(e.target)?h(e.target,"focusout",r):V(!1)})),D=u.onBlur,B=(0,i._q)((e=>{null==D||D(e),n&&function(e){const t=e.currentTarget,n=e.relatedTarget;return!n||!(0,f.gR)(t,n)}(e)&&V(!1)})),$=(0,r.useContext)(o),G=(0,i._q)((e=>{n&&c&&e&&$&&queueMicrotask((()=>{w(e)||g(e)&&e.focus()}))})),W=(0,i.vO)(d),U=n&&function(e){return!e||"button"===e||"summary"===e||"input"===e||"select"===e||"textarea"===e||"a"===e}(W),Z=n&&function(e){return!e||"button"===e||"input"===e||"select"===e||"textarea"===e}(W),K=u.style,q=(0,r.useMemo)((()=>k?(0,a.IA)({pointerEvents:"none"},K):K),[k,K]);return u=(0,a.ko)((0,a.IA)({"data-focus-visible":n&&L||void 0,"data-autofocus":c||void 0,"aria-disabled":m||void 0},u),{ref:(0,i.SV)(d,G,u.ref),style:q,tabIndex:x(n,k,U,Z,u.tabIndex),disabled:!(!Z||!k)||void 0,contentEditable:m?void 0:u.contentEditable,onKeyPressCapture:z,onClickCapture:F,onMouseDownCapture:O,onMouseDown:N,onKeyDownCapture:I,onFocusCapture:j,onBlur:B}),(0,y.HR)(u)}));function L(e){if(!e.isTrusted)return!1;const t=e.currentTarget;return"Enter"===e.key?(0,f.Bm)(t)||"SUMMARY"===t.tagName||"A"===t.tagName:" "===e.key&&((0,f.Bm)(t)||"SUMMARY"===t.tagName||"INPUT"===t.tagName||"SELECT"===t.tagName)}l((function(e){return u("div",k(e))}));var V=Symbol("command"),z=d((function(e){var t=e,{clickOnEnter:n=!0,clickOnSpace:o=!0}=t,s=(0,a.YG)(t,["clickOnEnter","clickOnSpace"]);const c=(0,r.useRef)(null),l=(0,i.vO)(c),u=s.type,[d,v]=(0,r.useState)((()=>!!l&&(0,f.Bm)({tagName:l,type:u})));(0,r.useEffect)((()=>{c.current&&v((0,f.Bm)(c.current))}),[]);const[g,w]=(0,r.useState)(!1),b=(0,r.useRef)(!1),E=(0,y.$f)(s),[C,x]=(0,i.P1)(s,V,!0),A=s.onKeyDown,S=(0,i._q)((e=>{null==A||A(e);const t=e.currentTarget;if(e.defaultPrevented)return;if(C)return;if(E)return;if(!p(e))return;if((0,f.mB)(t))return;if(t.isContentEditable)return;const r=n&&"Enter"===e.key,i=o&&" "===e.key,s="Enter"===e.key&&!n,c=" "===e.key&&!o;if(s||c)e.preventDefault();else if(r||i){const n=L(e);if(r){if(!n){e.preventDefault();const n=e,{view:r}=n,o=(0,a.YG)(n,["view"]),i=()=>m(t,o);f.Sw&&/firefox\//i.test(navigator.userAgent)?h(t,"keyup",i):queueMicrotask(i)}}else i&&(b.current=!0,n||(e.preventDefault(),w(!0)))}})),M=s.onKeyUp,T=(0,i._q)((e=>{if(null==M||M(e),e.defaultPrevented)return;if(C)return;if(E)return;if(e.metaKey)return;const t=o&&" "===e.key;if(b.current&&t&&(b.current=!1,!L(e))){e.preventDefault(),w(!1);const t=e.currentTarget,n=e,{view:r}=n,o=(0,a.YG)(n,["view"]);queueMicrotask((()=>m(t,o)))}}));return s=(0,a.ko)((0,a.IA)((0,a.IA)({"data-active":g||void 0,type:d?"button":void 0},x),s),{ref:(0,i.SV)(c,s.ref),onKeyDown:S,onKeyUp:T}),k(s)})),O=(l((function(e){return u("button",z(e))})),"button"),F=d((function(e){const t=(0,r.useRef)(null),n=(0,i.vO)(t,O),[o,s]=(0,r.useState)((()=>!!n&&(0,f.Bm)({tagName:n,type:e.type})));return(0,r.useEffect)((()=>{t.current&&s((0,f.Bm)(t.current))}),[]),e=(0,a.ko)((0,a.IA)({role:o||"a"===n?void 0:"button"},e),{ref:(0,i.SV)(t,e.ref)}),e=z(e)})),R=l((function(e){const t=F(e);return u(O,t)}))},611:(e,t,n)=>{"use strict";var r;if(n.d(t,{P1:()=>m,SV:()=>f,_q:()=>d,vO:()=>p}),/^(1028|251|4950|5932|7949)$/.test(n.j))var o=n(4951);var i=n(4823),s=n(1609);if(/^(1028|251|4950|5932|7949)$/.test(n.j))var a=n(754);var c=(0,i.IA)({},r||(r=n.t(s,2))),l=(c.useId,c.useDeferredValue,c.useInsertionEffect),u=/^(1028|251|4950|5932|7949)$/.test(n.j)?a.Sw?s.useLayoutEffect:s.useEffect:null;function d(e){const t=(0,s.useRef)((()=>{throw new Error("Cannot call an event handler while rendering.")}));return l?l((()=>{t.current=e})):t.current=e,(0,s.useCallback)(((...e)=>{var n;return null==(n=t.current)?void 0:n.call(t,...e)}),[])}function f(...e){return(0,s.useMemo)((()=>{if(e.some(Boolean))return t=>{e.forEach((e=>(0,o.cZ)(e,t)))}}),e)}function p(e,t){const n=e=>{if("string"==typeof e)return e},[r,o]=(0,s.useState)((()=>n(t)));return u((()=>{const r=e&&"current"in e?e.current:e;o((null==r?void 0:r.tagName.toLowerCase())||n(t))}),[e,t]),r}function m(e,t,n){const r=e.onLoadedMetadataCapture,o=(0,s.useMemo)((()=>Object.assign((()=>{}),(0,i.ko)((0,i.IA)({},r),{[t]:n}))),[r,t,n]);return[null==r?void 0:r[t],{onLoadedMetadataCapture:o}]}Symbol("setNextState")},4951:(e,t,n)=>{"use strict";if(n.d(t,{cZ:()=>s,v1:()=>a,v6:()=>c}),/^(1028|251|4950|5932|7949)$/.test(n.j))var r=n(4823);var o=n(1609);if(/^(1028|251|4950|5932|7949)$/.test(n.j))var i=n(6985);function s(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function a(e){return function(e){return!!e&&!!(0,o.isValidElement)(e)&&"ref"in e}(e)?e.ref:null}function c(e,t){const n=(0,r.IA)({},e);for(const o in t){if(!(0,i.mQ)(t,o))continue;if("className"===o){const r="className";n[r]=e[r]?`${e[r]} ${t[r]}`:t[r];continue}if("style"===o){const o="style";n[o]=e[o]?(0,r.IA)((0,r.IA)({},e[o]),t[o]):t[o];continue}const s=t[o];if("function"==typeof s&&o.startsWith("on")){const t=e[o];if("function"==typeof t){n[o]=(...e)=>{s(...e),t(...e)};continue}}n[o]=s}return n}},2509:(e,t,n)=>{"use strict";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(this,arguments)}n.d(t,{A:()=>r})},3558:(e,t,n)=>{"use strict";n.d(t,{Bi:()=>s});var r={exports:{}};const o=[{id:0,value:"Too weak",minDiversity:0,minLength:0},{id:1,value:"Weak",minDiversity:2,minLength:6},{id:2,value:"Medium",minDiversity:4,minLength:8},{id:3,value:"Strong",minDiversity:4,minLength:10}],i=(e,t=o,n="!\"#$%&'()*+,-./:;<=>?@[\\\\\\]^_`{|}~")=>{let r=e||"";t[0].minDiversity=0,t[0].minLength=0;const i=[{regex:"[a-z]",message:"lowercase"},{regex:"[A-Z]",message:"uppercase"},{regex:"[0-9]",message:"number"}];n&&i.push({regex:`[${n}]`,message:"symbol"});let s={};s.contains=i.filter((e=>new RegExp(`${e.regex}`).test(r))).map((e=>e.message)),s.length=r.length;let a=t.filter((e=>s.contains.length>=e.minDiversity)).filter((e=>s.length>=e.minLength)).sort(((e,t)=>t.id-e.id)).map((e=>({id:e.id,value:e.value})));return Object.assign(s,a[0]),s};r.exports={passwordStrength:i,defaultOptions:o};var s=r.exports.passwordStrength=i;r.exports.defaultOptions=o,r.exports},4921:(e,t,n)=>{"use strict";function r(e){var t,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e)if(Array.isArray(e)){var i=e.length;for(t=0;t<i;t++)e[t]&&(n=r(e[t]))&&(o&&(o+=" "),o+=n)}else for(n in e)e[n]&&(o&&(o+=" "),o+=n);return o}function o(){for(var e,t,n=0,o="",i=arguments.length;n<i;n++)(e=arguments[n])&&(t=r(e))&&(o&&(o+=" "),o+=t);return o}n.d(t,{$:()=>o,A:()=>i});const i=/^(1(157|21|276|789|861)|2((08|47|84)5||146|448|733)|3(035|068|750|806)|4(0|118|257)|5(30[36]|8(19|38|67)|159)|6(181|364|571|732)|7(064|155|293|341|492|619|960)|9(122|779|860|89)|8851)$/.test(n.j)?null:o},9456:(e,t,n)=>{"use strict";n.d(t,{di:()=>u});var r=function(e){return function(t,n,r){return e(t,n,r)*r}},o=function(e,t){if(e)throw Error("Invalid sort config: "+t)},i=function(e){var t=e||{},n=t.asc,i=t.desc,s=n?1:-1,a=n||i;return o(!a,"Expected `asc` or `desc` property"),o(n&&i,"Ambiguous object with `asc` and `desc` config properties"),{order:s,sortBy:a,comparer:e.comparer&&r(e.comparer)}};function s(e,t,n){if(void 0===e||!0===e)return function(e,r){return t(e,r,n)};if("string"==typeof e)return o(e.includes("."),"String syntax not allowed for nested properties."),function(r,o){return t(r[e],o[e],n)};if("function"==typeof e)return function(r,o){return t(e(r),e(o),n)};if(Array.isArray(e)){var r=function(e){return function t(n,r,o,s,a,c,l){var u,d;if("string"==typeof n)u=c[n],d=l[n];else{if("function"!=typeof n){var f=i(n);return t(f.sortBy,r,o,f.order,f.comparer||e,c,l)}u=n(c),d=n(l)}var p=a(u,d,s);return(0===p||null==u&&null==d)&&r.length>o?t(r[o],r,o+1,s,a,c,l):p}}(t);return function(o,i){return r(e[0],e,1,n,t,o,i)}}var a=i(e);return s(a.sortBy,a.comparer||t,a.order)}var a=function(e,t,n,r){return Array.isArray(t)?(Array.isArray(n)&&n.length<2&&(n=n[0]),t.sort(s(n,r,e))):t};function c(e){var t=r(e.comparer);return function(n){var r=Array.isArray(n)&&!e.inPlaceSorting?n.slice():n;return{asc:function(e){return a(1,r,e,t)},desc:function(e){return a(-1,r,e,t)},by:function(e){return a(1,r,e,t)}}}}var l=function(e,t,n){return null==e?n:null==t?-n:typeof e!=typeof t?typeof e<typeof t?-1:1:e<t?-1:e>t?1:0},u=c({comparer:l});c({comparer:l,inPlaceSorting:!0})},5029:(e,t,n)=>{"use strict";n.d(t,{Cl:()=>r});var r=function(){return r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},r.apply(this,arguments)};Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError},5194:e=>{"use strict";e.exports=JSON.parse('{"T":{"cai":"#fff","fN2":"#101517"}}')}}]);