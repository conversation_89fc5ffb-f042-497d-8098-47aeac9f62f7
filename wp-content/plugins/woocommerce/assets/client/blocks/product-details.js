(()=>{var e,t,o,i={7467:(e,t,o)=>{"use strict";const i=window.wp.blocks,r=window.wp.data,s=window.wc.wcTypes;class c{blocks=new Map;initialized=!1;attemptedRegisteredBlocks=new Set;constructor(){this.initializeSubscriptions()}static getInstance(){return c.instance||(c.instance=new c),c.instance}parseTemplateId(e){const t=(0,s.isNumber)(e)?void 0:e;return t?.split("//")[1]}initializeSubscriptions(){if(this.initialized)return;const e=(0,r.subscribe)((()=>{const t=(0,r.select)("core/edit-site"),o=(0,r.select)("core/edit-post");if(t||o)if(t){const o=t.getEditedPostId();e(),this.currentTemplateId="string"==typeof o?this.parseTemplateId(o):void 0,(0,r.subscribe)((()=>{const e=this.currentTemplateId;this.currentTemplateId=this.parseTemplateId(t.getEditedPostId()),e!==this.currentTemplateId&&this.handleTemplateChange(e)}),"core/edit-site"),this.initialized=!0}else o&&(e(),this.blocks.forEach((e=>{if(e.isAvailableOnPostEditor){const t=e.variationName||e.blockName;this.hasAttemptedRegistration(t)||this.registerBlock(e)}})),this.initialized=!0)}))}handleTemplateChange(e){(this.currentTemplateId?.includes("single-product")||e?.includes("single-product"))&&this.blocks.forEach((e=>{this.unregisterBlock(e),this.registerBlock(e)}))}hasAttemptedRegistration(e){return this.attemptedRegisteredBlocks.has(e)}unregisterBlock(e){const{blockName:t,isVariationBlock:o,variationName:r}=e;try{o&&r?((0,i.unregisterBlockVariation)(t,r),this.attemptedRegisteredBlocks.delete(r)):((0,i.unregisterBlockType)(t),this.attemptedRegisteredBlocks.delete(t))}catch(e){console.debug(`Failed to unregister block ${t}:`,e)}}registerBlock(e){const{blockName:t,settings:o,isVariationBlock:c,variationName:n,isAvailableOnPostEditor:a}=e;try{const e=n||t;if(this.hasAttemptedRegistration(e))return;const l=(0,r.select)("core/edit-site");if(!l&&!a)return;if(c)(0,i.registerBlockVariation)(t,o);else{const e=(0,s.isEmpty)(o?.ancestor)?["woocommerce/single-product"]:o?.ancestor,r=l&&this.currentTemplateId?.includes("single-product");(0,i.registerBlockType)(t,{...o,ancestor:r?void 0:e})}this.attemptedRegisteredBlocks.add(e)}catch(e){console.error(`Failed to register block ${t}:`,e)}}registerBlockConfig(e){const t=e.variationName||e.blockName;this.blocks.set(t,e),this.registerBlock(e)}}const n=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"woocommerce/product-details","title":"Product Details","description":"Display a product\'s description, attributes, and reviews","category":"woocommerce","textdomain":"woocommerce","supports":{"interactivity":{"clientNavigation":true},"align":["wide","full"]},"attributes":{"align":{"type":"string","default":"wide"},"hideTabTitle":{"type":"boolean","default":false}},"usesContext":["query","queryId","postId","postType"]}'),a=window.wp.blockEditor;var l=o(790),d=o(7723);const p=window.wp.components;var u=o(4921);const m=({id:e,title:t,active:o})=>(0,l.jsx)("li",{className:(0,u.A)(`${e}_tab`,{active:o}),id:`tab-title-${e}`,role:"tab","aria-controls":`tab-${e}`,children:(0,l.jsx)("a",{href:`#tab-${e}`,children:t})}),h=({id:e,content:t})=>(0,l.jsx)("div",{className:`${e}_tab`,id:`tab-title-${e}`,role:"tab","aria-controls":`tab-${e}`,children:t}),b=({hideTabTitle:e})=>{const t=[{id:"description",title:"Description",active:!0,content:(0,l.jsxs)(l.Fragment,{children:[!e&&(0,l.jsx)("h2",{children:(0,d.__)("Description","woocommerce")}),(0,l.jsx)("p",{children:(0,d.__)("This block lists description, attributes and reviews for a single product.","woocommerce")})]})},{id:"additional_information",title:"Additional Information",active:!1},{id:"reviews",title:"Reviews",active:!1}],o=t.map((({id:e,title:t,active:o})=>(0,l.jsx)(m,{id:e,title:t,active:o},e))),i=t.map((({id:e,content:t})=>(0,l.jsx)(h,{id:e,content:t},e)));return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("ul",{className:"wc-tabs tabs",role:"tablist",children:o}),i]})};o(4802);const w=[["woocommerce/accordion-group",{metadata:{isDescendantOfProductDetails:!0}},[["woocommerce/accordion-item",{openByDefault:!0},[["woocommerce/accordion-header",{title:(0,d.__)("Description","woocommerce")},[]],["woocommerce/accordion-panel",{},[["woocommerce/product-description",{},[]]]]]],["woocommerce/accordion-item",{},[["woocommerce/accordion-header",{title:(0,d.__)("Additional Information","woocommerce")},[]],["woocommerce/accordion-panel",{},[["woocommerce/product-specifications",{}]]]]],["woocommerce/accordion-item",{},[["woocommerce/accordion-header",{title:(0,d.__)("Reviews","woocommerce")},[]],["woocommerce/accordion-panel",{},[["woocommerce/product-reviews",{}]]]]]]]];var g=o(5573);const v=(0,l.jsxs)(g.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,l.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",fill:"currentColor",d:"M5 5.5H19C19.1326 5.5 19.2598 5.55268 19.3536 5.64645C19.4473 5.74021 19.5 5.86739 19.5 6V7.5C19.5 7.63261 19.4473 7.75979 19.3536 7.85355C19.2598 7.94732 19.1326 8 19 8H5C4.86739 8 4.74021 7.94732 4.64645 7.85355C4.55268 7.75979 4.5 7.63261 4.5 7.5V6C4.5 5.86739 4.55268 5.74021 4.64645 5.64645C4.74021 5.55268 4.86739 5.5 5 5.5V5.5ZM4 9.232C3.69597 9.05647 3.4435 8.804 3.26796 8.49997C3.09243 8.19594 3.00001 7.85106 3 7.5V6C3 5.46957 3.21071 4.96086 3.58579 4.58579C3.96086 4.21071 4.46957 4 5 4H19C19.5304 4 20.0391 4.21071 20.4142 4.58579C20.7893 4.96086 21 5.46957 21 6V7.5C21 7.85106 20.9076 8.19594 20.732 8.49997C20.5565 8.804 20.304 9.05647 20 9.232V18C20 18.5304 19.7893 19.0391 19.4142 19.4142C19.0391 19.7893 18.5304 20 18 20H6C5.46957 20 4.96086 19.7893 4.58579 19.4142C4.21071 19.0391 4 18.5304 4 18V9.232ZM5.5 9.5V18C5.5 18.1326 5.55268 18.2598 5.64645 18.3536C5.74021 18.4473 5.86739 18.5 6 18.5H18C18.1326 18.5 18.2598 18.4473 18.3536 18.3536C18.4473 18.2598 18.5 18.1326 18.5 18V9.5H5.5Z"}),(0,l.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8 13.25V11.75H16V13.25L8 13.25Z"}),(0,l.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M8 16.25V14.75H16V16.25H8Z"})]});o(7492),(e=>{const t=e.name;if(!t)return void console.error("registerProductBlockType: Block name is required for registration");const o=(({name:e,...t})=>t)(e),{isVariationBlock:i,variationName:r,isAvailableOnPostEditor:s,...n}={...o},a={blockName:t,settings:{...n},isVariationBlock:null!=i&&i,variationName:null!=r?r:void 0,isAvailableOnPostEditor:null!=s&&s};c.getInstance().registerBlockConfig(a)})({...n,icon:v,edit:({clientId:e,context:t,attributes:o,setAttributes:i})=>{const s=(0,a.useBlockProps)(),{hideTabTitle:c}=o,{hasInnerBlocks:n,wasBlockJustInserted:u}=(0,r.useSelect)((t=>({hasInnerBlocks:t(a.store).getBlocks(e).length>0,wasBlockJustInserted:t(a.store).wasBlockJustInserted(e)})),[e]),m=(0,a.useInnerBlocksProps)(s,{template:u?w:void 0}),h=((e,t)=>(0,r.useSelect)((o=>o(a.store).getBlockParentsByBlockName(e,"core/post-template").length>0&&"product"!==t),[e,t]))(e,t.postType);return h?(0,l.jsx)("div",{...s,children:(0,l.jsx)(a.Warning,{children:(0,d.__)("The Product Details block requires a product context. When used in a Query Loop, the Query Loop must be configured to display products.","woocommerce")})}):n||u?(0,l.jsx)("div",{...m}):(0,l.jsxs)("div",{...s,children:[(0,l.jsx)(a.InspectorControls,{children:(0,l.jsx)(p.PanelBody,{title:(0,d.__)("Settings","woocommerce"),children:(0,l.jsx)(p.ToggleControl,{label:(0,d.__)("Show tab title in content","woocommerce"),checked:!c,onChange:()=>i({hideTabTitle:!c})})})},"inspector"),(0,l.jsx)(p.Disabled,{children:(0,l.jsx)(b,{hideTabTitle:c})})]})},save:function(){const e=a.useBlockProps.save(),t=a.useInnerBlocksProps.save(e);return(0,l.jsx)("div",{...t})},deprecated:[{attributes:{hideTabTitle:{type:"boolean",default:!1}},save:()=>null,migrate:e=>({...e,align:"wide"})}]})},4802:()=>{},7492:()=>{},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},7723:e=>{"use strict";e.exports=window.wp.i18n},5573:e=>{"use strict";e.exports=window.wp.primitives}},r={};function s(e){var t=r[e];if(void 0!==t)return t.exports;var o=r[e]={exports:{}};return i[e].call(o.exports,o,o.exports,s),o.exports}s.m=i,e=[],s.O=(t,o,i,r)=>{if(!o){var c=1/0;for(d=0;d<e.length;d++){for(var[o,i,r]=e[d],n=!0,a=0;a<o.length;a++)(!1&r||c>=r)&&Object.keys(s.O).every((e=>s.O[e](o[a])))?o.splice(a--,1):(n=!1,r<c&&(c=r));if(n){e.splice(d--,1);var l=i();void 0!==l&&(t=l)}}return t}r=r||0;for(var d=e.length;d>0&&e[d-1][2]>r;d--)e[d]=e[d-1];e[d]=[o,i,r]},s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,s.t=function(e,i){if(1&i&&(e=this(e)),8&i)return e;if("object"==typeof e&&e){if(4&i&&e.__esModule)return e;if(16&i&&"function"==typeof e.then)return e}var r=Object.create(null);s.r(r);var c={};t=t||[null,o({}),o([]),o(o)];for(var n=2&i&&e;"object"==typeof n&&!~t.indexOf(n);n=o(n))Object.getOwnPropertyNames(n).forEach((t=>c[t]=()=>e[t]));return c.default=()=>e,s.d(r,c),r},s.d=(e,t)=>{for(var o in t)s.o(t,o)&&!s.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.j=7161,(()=>{var e={7161:0};s.O.j=t=>0===e[t];var t=(t,o)=>{var i,r,[c,n,a]=o,l=0;if(c.some((t=>0!==e[t]))){for(i in n)s.o(n,i)&&(s.m[i]=n[i]);if(a)var d=a(s)}for(t&&t(o);l<c.length;l++)r=c[l],s.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return s.O(d)},o=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var c=s.O(void 0,[94],(()=>s(7467)));c=s.O(c),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["product-details"]=c})();