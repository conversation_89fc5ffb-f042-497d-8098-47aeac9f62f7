{"name": "woocommerce/mini-cart-shopping-button-block", "version": "1.0.0", "title": "Mini-Cart Shopping Button", "description": "Block that displays the shopping button when the Mini-Cart is empty.", "category": "woocommerce", "supports": {"align": false, "html": false, "multiple": false, "reusable": false, "inserter": true, "color": {"text": true, "background": true}, "interactivity": {"clientNavigation": true}}, "attributes": {"lock": {"type": "object", "default": {"remove": false, "move": false}}, "startShoppingButtonLabel": {"type": "string", "default": ""}}, "styles": [{"name": "fill", "label": "Fill", "isDefault": true}, {"name": "outline", "label": "Outline"}], "parent": ["woocommerce/empty-mini-cart-contents-block"], "textdomain": "woocommerce", "apiVersion": 3, "$schema": "https://schemas.wp.org/trunk/block.json"}