{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "woocommerce/product-collection", "title": "Product Collection", "description": "Display a collection of products from your store.", "category": "woocommerce", "keywords": ["WooCommerce", "Products (Beta)", "all products", "by attribute", "by category", "by tag"], "textdomain": "woocommerce", "attributes": {"queryId": {"type": "number"}, "query": {"type": "object"}, "tagName": {"type": "string"}, "displayLayout": {"type": "object"}, "dimensions": {"type": "object"}, "convertedFromProducts": {"type": "boolean", "default": false}, "collection": {"type": "string"}, "hideControls": {"default": [], "type": "array"}, "queryContextIncludes": {"type": "array"}, "forcePageReload": {"type": "boolean", "default": false}, "__privatePreviewState": {"type": "object"}}, "providesContext": {"queryId": "queryId", "query": "query", "displayLayout": "displayLayout", "dimensions": "dimensions", "queryContextIncludes": "queryContextIncludes", "collection": "collection", "__privateProductCollectionPreviewState": "__privatePreviewState"}, "usesContext": ["templateSlug", "postId"], "supports": {"align": ["wide", "full"], "anchor": true, "html": false, "__experimentalLayout": true, "interactivity": true}, "editorStyle": "file:../woocommerce/product-collection-editor.css", "style": "file:../woocommerce/product-collection-style.css"}