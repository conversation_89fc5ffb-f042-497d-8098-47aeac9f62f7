import*as t from"@wordpress/interactivity";var e={d:(t,r)=>{for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)};const r=(o={store:()=>t.store},s={},e.d(s,o),s),n=t=>{const e=(t=>{const e=t.target,r=e.parentElement?.querySelector(".input-text.qty.text");return r})(t);if(!e)return;const r=parseInt(e.value,10),n=parseInt(e.min,10),a=parseInt(e.max,10),o=parseInt(e.step,10);return{currentValue:isNaN(r)?0:r,minValue:isNaN(n)?1:n,maxValue:isNaN(a)?void 0:a,step:isNaN(o)?1:o,inputElement:e}},a=t=>{const e=new Event("change",{bubbles:!0});t.dispatchEvent(e)};var o,s;(0,r.store)("woocommerce/add-to-cart-form",{state:{},actions:{addQuantity:t=>{const e=n(t);if(!e)return;const{currentValue:r,maxValue:o,step:s,inputElement:i}=e,u=r+s;(void 0===o||u<=o)&&(i.value=u.toString(),a(i))},removeQuantity:t=>{const e=n(t);if(!e)return;const{currentValue:r,minValue:o,step:s,inputElement:i}=e,u=r-s;u>=o&&(i.value=u.toString(),a(i))}}});