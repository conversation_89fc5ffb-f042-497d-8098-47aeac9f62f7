(()=>{var e,t,o,r={3340:(e,t,o)=>{"use strict";const r=window.wp.blocks,c=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"woocommerce/product-collection","title":"Product Collection","description":"Display a collection of products from your store.","category":"woocommerce","keywords":["WooCommerce","Products (Beta)","all products","by attribute","by category","by tag"],"textdomain":"woocommerce","attributes":{"queryId":{"type":"number"},"query":{"type":"object"},"tagName":{"type":"string"},"displayLayout":{"type":"object"},"dimensions":{"type":"object"},"convertedFromProducts":{"type":"boolean","default":false},"collection":{"type":"string"},"hideControls":{"default":[],"type":"array"},"queryContextIncludes":{"type":"array"},"forcePageReload":{"type":"boolean","default":false},"__privatePreviewState":{"type":"object"}},"providesContext":{"queryId":"queryId","query":"query","displayLayout":"displayLayout","dimensions":"dimensions","queryContextIncludes":"queryContextIncludes","collection":"collection","__privateProductCollectionPreviewState":"__privatePreviewState"},"usesContext":["templateSlug","postId"],"supports":{"align":["wide","full"],"anchor":true,"html":false,"__experimentalLayout":true,"interactivity":true},"editorStyle":"file:../woocommerce/product-collection-editor.css","style":"file:../woocommerce/product-collection-style.css"}'),n=window.wp.blockEditor;var s=o(6087);const l=window.wp.data,a=window.wp.coreData;let i=function(e){return e.Product="product",e.Archive="archive",e.Cart="cart",e.Order="order",e.Site="site",e}({});const d="single-product",u="taxonomy-product_cat",m="taxonomy-product_tag",p=async(e,t,o,r)=>{var c;r((c=await(0,l.resolveSelect)(a.store).getEntityRecords(e,t,{_fields:["id"],slug:o}))&&c.length&&c[0]?.id?c[0].id:null)},_=(e,t={})=>({type:e,sourceData:t}),h=window.wp.components;let g=function(e){return e.COLLECTION_PICKER="collection_chooser",e.PRODUCT_REFERENCE_PICKER="product_context_picker",e.VALID_WITH_PREVIEW="uses_reference_preview_mode",e.VALID="valid",e.DELETED_PRODUCT_REFERENCE="deleted_product_reference",e}({}),w=function(e){return e.GRID="flex",e.STACK="list",e.CAROUSEL="carousel",e}({}),x=function(e){return e.FILL="fill",e.FIXED="fixed",e}({}),b=function(e){return e.IN="in",e.NOT_IN="not-in",e}({}),y=function(e){return e.PRODUCT_CATALOG="woocommerce/product-collection/product-catalog",e.BEST_SELLERS="woocommerce/product-collection/best-sellers",e.FEATURED="woocommerce/product-collection/featured",e.NEW_ARRIVALS="woocommerce/product-collection/new-arrivals",e.ON_SALE="woocommerce/product-collection/on-sale",e.TOP_RATED="woocommerce/product-collection/top-rated",e.HAND_PICKED="woocommerce/product-collection/hand-picked",e.RELATED="woocommerce/product-collection/related",e.UPSELLS="woocommerce/product-collection/upsells",e.CROSS_SELLS="woocommerce/product-collection/cross-sells",e.BY_CATEGORY="woocommerce/product-collection/by-category",e.BY_TAG="woocommerce/product-collection/by-tag",e}({}),f=function(e){return e.ATTRIBUTES="attributes",e.CREATED="created",e.FEATURED="featured",e.HAND_PICKED="hand-picked",e.INHERIT="inherit",e.KEYWORD="keyword",e.ON_SALE="on-sale",e.ORDER="order",e.DEFAULT_ORDER="default-order",e.STOCK_STATUS="stock-status",e.TAXONOMY="taxonomy",e.PRICE_RANGE="price-range",e.FILTERABLE="filterable",e.PRODUCTS_PER_PAGE="products-per-page",e.MAX_PAGES_TO_SHOW="max-pages-to-show",e.OFFSET="offset",e.RELATED_BY="related-by",e}({});var C=o(7723),v=o(1244),k=o.n(v);k()("wc-admin:tracks:stats");const S=k()("wc-admin:tracks");function E(e,t){if(S("recordevent %s %o","wcadmin_"+e,t,{_tqk:window._tkq,shouldRecord:!!window._tkq&&!!window.wcTracks&&!!window.wcTracks.isEnabled}),!window.wcTracks||"function"!=typeof window.wcTracks.recordEvent)return!1;window.wcTracks.recordEvent(e,t)}var j=o(9491);const A=window.wc.wcBlocksRegistry;var R=o(4530),I=o(8226);const P=window.wc.wcSettings;let T=function(e){return e.SINGLE="single",e.THUMBNAIL="thumbnail",e}({});const O=`${c.name}/product-title`,L=(0,P.getSetting)("stockStatusOptions",[]),D=(0,P.getSetting)("hideOutOfStockItems",!1),N=()=>D?Object.keys(function(e,t){const{[t]:o,...r}=e;return r}(L,"outofstock")):Object.keys(L),B={perPage:9,pages:0,offset:0,postType:"product",order:"asc",orderBy:"title",search:"",exclude:[],inherit:!1,taxQuery:{},isProductCollectionBlock:!0,featured:!1,woocommerceOnSale:!1,woocommerceStockStatus:N(),woocommerceAttributes:[],woocommerceHandPickedProducts:[],timeFrame:void 0,priceRange:void 0,filterable:!1,relatedBy:{categories:!0,tags:!0}},F={query:B,tagName:"div",displayLayout:{type:w.GRID,columns:3,shrinkColumns:!0},dimensions:{widthType:x.FILL},queryContextIncludes:["collection"],forcePageReload:!1},q={woocommerceOnSale:B.woocommerceOnSale,woocommerceStockStatus:B.woocommerceStockStatus,woocommerceAttributes:B.woocommerceAttributes,woocommerceHandPickedProducts:B.woocommerceHandPickedProducts,taxQuery:B.taxQuery,featured:B.featured,timeFrame:B.timeFrame,priceRange:B.priceRange},M="core/query-pagination",U="woocommerce/product-gallery-large-image-next-previous",V="woocommerce/product-template",H=[V,{},[["woocommerce/product-image",{imageSizing:T.THUMBNAIL,showSaleBadge:!1},[["woocommerce/product-sale-badge",{align:"right"}]]],["core/post-title",{textAlign:"center",level:2,fontSize:"medium",style:{spacing:{margin:{bottom:"0.75rem",top:"0"}},typography:{lineHeight:"1.4"}},isLink:!0,__woocommerceNamespace:O}],["woocommerce/product-price",{textAlign:"center",fontSize:"small"}],["woocommerce/product-button",{textAlign:"center",fontSize:"small"}]]],G={layout:{type:"flex",justifyContent:"center"}},$=[M,G],Q=[H,$,["woocommerce/product-collection-no-results"]];var W=o(790);const K={name:y.BEST_SELLERS,title:(0,C.__)("Best Sellers","woocommerce"),icon:(0,W.jsx)(R.A,{icon:I.A}),description:(0,C.__)("Recommend your best-selling products.","woocommerce"),keywords:["best selling"],scope:["inserter","block"],attributes:{displayLayout:{type:"flex",columns:5,shrinkColumns:!0},query:{orderBy:"popularity",order:"desc",perPage:5,pages:1},hideControls:[f.ORDER,f.FILTERABLE]},innerBlocks:[["core/heading",{textAlign:"center",level:2,content:(0,C.__)("Best selling products","woocommerce"),style:{spacing:{margin:{bottom:"1rem"}}}}],H]};var Y=o(3255);const z={name:y.CROSS_SELLS,title:(0,C.__)("Cross-Sells","woocommerce"),icon:(0,W.jsx)(R.A,{icon:Y.A}),description:(0,C.__)("By suggesting complementary products in the cart using cross-sells, you can significantly increase the average order value.","woocommerce"),keywords:["boost","promotion"],scope:["inserter","block"],usesReference:["product","cart","order"],attributes:{displayLayout:{type:"flex",columns:4,shrinkColumns:!0},query:{perPage:8,pages:1},hideControls:[f.FILTERABLE]},innerBlocks:[["core/heading",{textAlign:"center",level:2,content:(0,C.__)("You may be interested in…","woocommerce"),style:{spacing:{margin:{bottom:"1rem"}}}}],H]};var Z=o(3129);const X={name:y.FEATURED,title:(0,C.__)("Featured Products","woocommerce"),icon:(0,W.jsx)(R.A,{icon:Z.A}),description:(0,C.__)("Showcase your featured products.","woocommerce"),keywords:[],scope:["inserter","block"],attributes:{displayLayout:{type:"flex",columns:5,shrinkColumns:!0},query:{featured:!0,perPage:5,pages:1},hideControls:[f.FEATURED,f.FILTERABLE]},innerBlocks:[["core/heading",{textAlign:"center",level:2,content:(0,C.__)("Featured products","woocommerce"),style:{spacing:{margin:{bottom:"1rem"}}}}],H]};var J=o(5573);const ee=(0,W.jsxs)(J.SVG,{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,W.jsx)(J.Path,{d:"M8.85074 4.8213L7.64702 3.92627L5.56365 6.72818L4.44959 5.89735L3.55286 7.0998L5.87107 8.82862L8.85074 4.8213Z",fill:"currentColor"}),(0,W.jsx)(J.Path,{d:"M20 7.75004H11.1111V6.25004H20V7.75004Z",fill:"currentColor"}),(0,W.jsx)(J.Path,{d:"M20 12.75H11.1111V11.25H20V12.75Z",fill:"currentColor"}),(0,W.jsx)(J.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M6 14C7.10457 14 8 13.1046 8 12C8 10.8955 7.10457 10 6 10C4.89543 10 4 10.8955 4 12C4 13.1046 4.89543 14 6 14ZM6 13C6.55229 13 7 12.5523 7 12C7 11.4478 6.55229 11 6 11C5.44772 11 5 11.4478 5 12C5 12.5523 5.44772 13 6 13Z",fill:"currentColor"}),(0,W.jsx)(J.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M8 17C8 18.1046 7.10457 19 6 19C4.89543 19 4 18.1046 4 17C4 15.8955 4.89543 15 6 15C7.10457 15 8 15.8955 8 17ZM7 17C7 17.5523 6.55229 18 6 18C5.44772 18 5 17.5523 5 17C5 16.4478 5.44772 16 6 16C6.55229 16 7 16.4478 7 17Z",fill:"currentColor"}),(0,W.jsx)(J.Path,{d:"M11.1111 17.75H20V16.25H11.1111V17.75Z",fill:"currentColor"})]}),te={name:y.HAND_PICKED,title:(0,C.__)("Hand-Picked Products","woocommerce"),icon:(0,W.jsx)(R.A,{icon:ee}),description:(0,C.__)("Select specific products to recommend to customers.","woocommerce"),keywords:["specific","choose","recommend","handpicked","hand picked"],scope:["inserter","block"],attributes:{displayLayout:{type:"flex",columns:5,shrinkColumns:!0},query:{orderBy:"post__in"},hideControls:[f.HAND_PICKED,f.FILTERABLE,f.ORDER]},innerBlocks:[["core/heading",{textAlign:"center",level:2,content:(0,C.__)("Recommended products","woocommerce"),style:{spacing:{margin:{bottom:"1rem"}}}}],H,["core/query-pagination",{layout:{type:"flex",justifyContent:"center"}}]]};var oe=o(3174);const re={name:y.NEW_ARRIVALS,title:(0,C.__)("New Arrivals","woocommerce"),icon:(0,W.jsx)(R.A,{icon:oe.A}),description:(0,C.__)("Recommend your newest products.","woocommerce"),keywords:["newest"],scope:["inserter","block"],attributes:{displayLayout:{type:"flex",columns:5,shrinkColumns:!0},query:{orderBy:"date",order:"desc",perPage:5,pages:1,timeFrame:{operator:b.IN,value:"-7 days"}},hideControls:[f.ORDER,f.FILTERABLE]},innerBlocks:[["core/heading",{textAlign:"center",level:2,content:(0,C.__)("New arrivals","woocommerce"),style:{spacing:{margin:{bottom:"1rem"}}}}],H]};var ce=o(9771);const ne={name:y.ON_SALE,title:(0,C.__)("On Sale Products","woocommerce"),icon:(0,W.jsx)(R.A,{icon:ce.A}),description:(0,C.__)("Highlight products that are currently on sale.","woocommerce"),keywords:["discount","promotion","onsale"],scope:["inserter","block"],attributes:{displayLayout:{type:"flex",columns:5,shrinkColumns:!0},query:{woocommerceOnSale:!0,perPage:5,pages:1},hideControls:[f.ON_SALE,f.FILTERABLE]},innerBlocks:[["core/heading",{textAlign:"center",level:2,content:(0,C.__)("On sale products","woocommerce"),style:{spacing:{margin:{bottom:"1rem"}}}}],H]};var se=o(5350);const le={name:y.PRODUCT_CATALOG,title:(0,C.__)("Product Catalog","woocommerce"),icon:(0,W.jsx)(R.A,{icon:se.A}),description:"Display all products in your catalog. Results can (change to) match the current template, page, or search term.",keywords:["all products"],scope:[],innerBlocks:Q},ae={name:y.RELATED,title:(0,C.__)("Related Products","woocommerce"),icon:(0,W.jsx)(R.A,{icon:se.A}),description:(0,C.__)("Recommend products like this one.","woocommerce"),keywords:[],scope:["inserter","block"],usesReference:["product"],attributes:{displayLayout:{type:w.GRID,columns:4,shrinkColumns:!0},query:{perPage:4,pages:1}},innerBlocks:[["core/heading",{textAlign:"center",level:2,content:(0,C.__)("Related Products","woocommerce"),style:{spacing:{margin:{bottom:"1rem"}}}}],H]};var ie=o(2108);const de={name:y.TOP_RATED,title:(0,C.__)("Top Rated Products","woocommerce"),icon:(0,W.jsx)(R.A,{icon:ie.A}),description:(0,C.__)("Recommend products with the highest review ratings.","woocommerce"),keywords:[],scope:["inserter","block"],attributes:{displayLayout:{type:"flex",columns:5,shrinkColumns:!0},query:{orderBy:"rating",order:"desc",perPage:5,pages:1},hideControls:[f.ORDER,f.FILTERABLE]},innerBlocks:[["core/heading",{textAlign:"center",level:2,content:(0,C.__)("Top rated products","woocommerce"),style:{spacing:{margin:{bottom:"1rem"}}}}],H]};var ue=o(4807);const me={name:y.UPSELLS,title:(0,C.__)("Upsells","woocommerce"),icon:(0,W.jsx)(R.A,{icon:ue.A}),description:(0,C.__)("Upsells are typically products that are extra profitable or better quality or more expensive. Experiment with combinations to boost sales.","woocommerce"),keywords:["boost","promotion"],scope:["inserter","block"],usesReference:["product","cart","order"],attributes:{displayLayout:{type:"flex",columns:4,shrinkColumns:!0},query:{perPage:8,pages:1},hideControls:[f.FILTERABLE]},innerBlocks:[["core/heading",{textAlign:"center",level:2,content:(0,C.__)("You may also like","woocommerce"),style:{spacing:{margin:{bottom:"1rem"}}}}],H]};var pe=o(5536);const _e={name:y.BY_CATEGORY,title:(0,C.__)("Products by Category","woocommerce"),icon:(0,W.jsx)(R.A,{icon:pe.A}),description:(0,C.__)("Display products from specific categories.","woocommerce"),scope:["inserter","block"],attributes:{displayLayout:{type:"flex",columns:5,shrinkColumns:!0},hideControls:[f.HAND_PICKED,f.FILTERABLE]},innerBlocks:[["core/heading",{textAlign:"center",level:2,content:(0,C.__)("Products by Category","woocommerce"),style:{spacing:{margin:{bottom:"1rem"}}}}],H,$]};var he=o(1686);const ge=[le,X,re,ne,K,de,te,_e,{name:y.BY_TAG,title:(0,C.__)("Products by Tag","woocommerce"),icon:(0,W.jsx)(R.A,{icon:he.A}),description:(0,C.__)("Display products with specific tags.","woocommerce"),scope:["inserter","block"],attributes:{displayLayout:{type:"flex",columns:5,shrinkColumns:!0},hideControls:[f.HAND_PICKED,f.FILTERABLE]},innerBlocks:[["core/heading",{textAlign:"center",level:2,content:(0,C.__)("Products by Tag","woocommerce"),style:{spacing:{margin:{bottom:"1rem"}}}}],H,$]},ae,me,z],we=e=>e?(0,l.select)(r.store).getBlockVariations(c.name).find((({name:t})=>t===e)):null,xe=window.wp.hooks,be=window.wp.url,ye=window.wp.apiFetch;var fe=o.n(ye);const Ce=(0,P.getSetting)("wcBlocksConfig",{pluginUrl:"",productCount:0,defaultAvatar:"",restApiRoutes:{},wordCountType:"words"}),ve=Ce.pluginUrl+"assets/images/",ke=(Ce.pluginUrl,P.STORE_PAGES.shop,P.STORE_PAGES.checkout,P.STORE_PAGES.checkout,P.STORE_PAGES.privacy,P.STORE_PAGES.privacy,P.STORE_PAGES.terms,P.STORE_PAGES.terms,P.STORE_PAGES.cart,P.STORE_PAGES.cart,P.STORE_PAGES.myaccount?.permalink?P.STORE_PAGES.myaccount.permalink:(0,P.getSetting)("wpLoginUrl","/wp-login.php"),(0,P.getSetting)("localPickupEnabled",!1),(0,P.getSetting)("shippingMethodsExist",!1),(0,P.getSetting)("shippingEnabled",!0),(0,P.getSetting)("countries",{})),Se=(0,P.getSetting)("countryData",{}),Ee={...Object.fromEntries(Object.keys(Se).filter((e=>!0===Se[e].allowBilling)).map((e=>[e,ke[e]||""]))),...Object.fromEntries(Object.keys(Se).filter((e=>!0===Se[e].allowShipping)).map((e=>[e,ke[e]||""])))},je=(Object.fromEntries(Object.keys(Ee).map((e=>[e,Se[e].states||{}]))),Object.fromEntries(Object.keys(Ee).map((e=>[e,Se[e].locale||{}]))),{address:["first_name","last_name","company","address_1","address_2","city","postcode","country","state","phone"],contact:["email"],order:[]}),Ae=((0,P.getSetting)("addressFieldsLocations",je).address,(0,P.getSetting)("addressFieldsLocations",je).contact,(0,P.getSetting)("addressFieldsLocations",je).order,(0,P.getSetting)("additionalOrderFields",{}),(0,P.getSetting)("additionalContactFields",{}),(0,P.getSetting)("additionalAddressFields",{}),()=>{const{experimentalBlocksEnabled:e}=(0,P.getSetting)("wcBlocksConfig",{experimentalBlocksEnabled:!1});return e}),Re=({selected:e=[],search:t="",queryArgs:o={}})=>{const r=(({selected:e=[],search:t="",queryArgs:o={}})=>{const r=Ce.productCount>100,c={per_page:r?100:0,catalog_visibility:"any",search:t,orderby:"title",order:"asc"},n=[(0,be.addQueryArgs)("/wc/store/v1/products",{...c,...o})];return r&&e.length&&n.push((0,be.addQueryArgs)("/wc/store/v1/products",{catalog_visibility:"any",include:e,per_page:0})),n})({selected:e,search:t,queryArgs:o});return Promise.all(r.map((e=>fe()({path:e})))).then((e=>{const t=((e,t)=>{const o=new Map;return e.filter((e=>{const r=t(e);return!o.has(r)&&(o.set(r,e),!0)}))})(e.flat(),(e=>e.id));return t.map((e=>({...e,parent:0})))})).catch((e=>{throw e}))},Ie=e=>fe()({path:`wc/store/v1/products/attributes/${e}/terms`});function Pe(e,t){const{query:o}=e.attributes;e.setAttributes({query:{...o,...t}})}const Te=()=>{const e=(0,l.select)("core/edit-site")?.getEditedPostId();return!!e&&["woocommerce/woocommerce//archive-product","woocommerce/woocommerce//taxonomy-product_attribute","woocommerce/woocommerce//product-search-results","//taxonomy-product_cat","//taxonomy-product_tag"].some((t=>e.includes(t)))},Oe=e=>{const{getBlocksByName:t,getBlock:o}=(0,l.select)(n.store);return!t("woocommerce/product-collection").find((t=>{const r=o(t);return r.attributes?.query?.[e]}))};function Le(){return!!Te()&&Oe("inherit")}function De(){return!Te()&&Oe("filterable")}const Ne=()=>F.displayLayout,Be=(e,t,o)=>{const n=we(e);n&&o(t,n.name===y.PRODUCT_CATALOG?(0,r.createBlock)(c.name,{...F,query:{...F.query,inherit:Le(),filterable:De()}},(0,r.createBlocksFromInnerBlocksTemplate)(Q)):(0,r.createBlock)(c.name,n.attributes,(0,r.createBlocksFromInnerBlocksTemplate)(n.innerBlocks)))},Fe=({title:e,icon:t,description:o,onClick:r})=>(0,W.jsx)(h.Tooltip,{text:o,placement:"top",children:(0,W.jsxs)(h.Button,{className:"wc-blocks-product-collection__collection-button",onClick:r,children:[(0,W.jsx)("div",{className:"wc-blocks-product-collection__collection-button-icon",children:(0,W.jsx)(h.Icon,{icon:t})}),(0,W.jsx)("p",{className:"wc-blocks-product-collection__collection-button-title",children:e})]})}),qe=e=>{const{description:t,onClick:o}=e;return(0,W.jsxs)("div",{className:"wc-blocks-product-collection__collections-create",children:[(0,W.jsx)("span",{children:(0,C.__)("or","woocommerce")}),(0,W.jsx)(h.Tooltip,{text:t,placement:"top",children:(0,W.jsx)(h.Button,{onClick:o,children:(0,C.__)("create your own","woocommerce")})})]})},Me=e=>{const{onCollectionClick:t,catalogVariation:o,collectionVariations:r}=e;return(0,W.jsxs)("div",{className:"wc-blocks-product-collection__collections-grid",children:[(0,W.jsx)("div",{className:"wc-blocks-product-collection__collections-section",children:r.map((({name:e,title:o,icon:r,description:c})=>(0,W.jsx)(Fe,{title:o,description:c,icon:r,onClick:()=>t(e)},e)))}),(0,W.jsx)(qe,{title:o.title,description:o.description,icon:o.icon,onClick:()=>t(o.name)})]})},Ue=e=>{const{onCollectionClick:t,catalogVariation:o,collectionVariations:r}=e;return(0,W.jsxs)("div",{className:"wc-blocks-product-collection__collections-dropdown",children:[(0,W.jsx)(h.Dropdown,{className:"wc-blocks-product-collection__collections-dropdown-toggle",contentClassName:"wc-blocks-product-collection__collections-dropdown-content",renderToggle:({isOpen:e,onToggle:t})=>(0,W.jsx)(h.Button,{variant:"secondary",onClick:t,"aria-expanded":e,children:(0,C.__)("Choose collection","woocommerce")}),renderContent:()=>(0,W.jsx)(W.Fragment,{children:r.map((({name:e,title:o,icon:r,description:c})=>(0,W.jsx)(Fe,{title:o,description:c,icon:r,onClick:()=>t(e)},e)))})}),(0,W.jsx)(qe,{title:o.title,description:o.description,icon:o.icon,onClick:()=>t(o.name)})]})},Ve=e=>{const t=(0,l.useSelect)((e=>{const{getBlockVariations:t}=e(r.store);return t(c.name)}),[]),o=(0,s.useMemo)((()=>t.find((({name:e})=>e===y.PRODUCT_CATALOG))),[t]),n=(0,s.useMemo)((()=>t.filter((({name:e,scope:t})=>e!==y.PRODUCT_CATALOG&&(void 0===t||t?.includes("block"))))),[t]),[a,{width:i}]=(0,j.useResizeObserver)();let d;return d=null!==i&&i>=600?Me:Ue,(0,W.jsxs)(W.Fragment,{children:[a,!!i&&(0,W.jsx)(d,{...e,catalogVariation:o,collectionVariations:n})]})},He=e=>{const t=(0,n.useBlockProps)(),{clientId:o,tracksLocation:r}=e,{replaceBlock:c}=(0,l.useDispatch)(n.store);return(0,W.jsx)("div",{...t,children:(0,W.jsx)(h.Placeholder,{className:"wc-blocks-product-collection__placeholder",instructions:(0,C.__)("What products do you want to show?","woocommerce"),children:(0,W.jsx)(Ve,{onCollectionClick:e=>{E("blocks_product_collection_collection_chosen_from_placeholder",{collection:e,location:r}),Be(e,o,c)}})})})};var Ge=o(1824),$e=o.n(Ge);const Qe="wc-blocks_upgraded-products-to-product-collection",We=()=>({status:"notseen",time:Date.now(),displayCount:0}),Ke=window.wc.wcTypes,Ye=e=>"woocommerce/product-collection"===e.name&&e.attributes.convertedFromProducts,ze=(e,t)=>{let o=[];return e.forEach((e=>{t(e)&&(o=[...o,e.clientId]),o=[...o,...ze(e.innerBlocks,t)]})),o},Ze=(0,P.getSettingWithCoercion)("postTemplateHasSupportForGridView",!1,Ke.isBoolean),Xe=()=>{const e=window.localStorage.getItem(Qe);return e?JSON.parse(e):We()},Je=e=>{window.localStorage.setItem(Qe,JSON.stringify(e))},et=e=>"flex"===e?"grid":"list"===e?"default":"grid",tt=e=>{const{type:t,columns:o}=e;return{type:et(t),columnCount:o}},ot=(e,t)=>{const o=e.map((e=>{const{name:o,attributes:c}=e,n=ot(e.innerBlocks);return(({name:e})=>"woocommerce/product-template"===e)(e)?((e,t,o)=>(0,r.createBlock)("core/post-template",{className:"products-block-post-template",layout:Ze?tt(o):void 0,__woocommerceNamespace:"woocommerce/product-query/product-template",...e.attributes},t))(e,n,t):(({name:e,attributes:t})=>"core/post-title"===e&&"woocommerce/product-collection/product-title"===t.__woocommerceNamespace)(e)?((e,t)=>{const{__woocommerceNamespace:o,...c}=e.attributes;return(0,r.createBlock)("core/post-title",{__woocommerceNamespace:"woocommerce/product-collection/product-title",...c},t)})(e,n):(({name:e,attributes:t})=>"core/post-excerpt"===e&&"woocommerce/product-collection/product-summary"===t.__woocommerceNamespace)(e)?((e,t)=>{const{__woocommerceNamespace:o,...c}=e.attributes;return(0,r.createBlock)("core/post-excerpt",{__woocommerceNamespace:"woocommerce/product-collection/product-summary",...c},t)})(e,n):(0,r.createBlock)(o,c,n)}));return o},rt=e=>{const t=(0,l.select)("core/block-editor").getBlock(e),o=(e=>{const t=(0,l.select)("core/block-editor").getBlockRootClientId(e)||void 0;return(0,l.select)("core/block-editor").canInsertBlockType("core/query",t)})(e);if(t&&o){const{attributes:o={},innerBlocks:c=[]}=t,{displayLayout:n,...s}=o,a=(e=>{const{query:t,...o}=e,{woocommerceAttributes:r,woocommerceStockStatus:c,woocommerceOnSale:n,woocommerceHandPickedProducts:s,taxQuery:l,isProductCollectionBlock:a,...i}=t,d={...i};return s&&(d.include=s),n&&(d.__woocommerceOnSale=n),l&&(d.taxQuery=l),{...o,namespace:"woocommerce/product-query",query:{__woocommerceAttributes:r||[],__woocommerceStockStatus:c||[],...d}}})(Ze?s:o),i=ot(c,n),d=(0,r.createBlock)("core/query",a,i);return(0,l.dispatch)("core/block-editor").replaceBlock(e,d),!0}return!1},ct=()=>{var e;Je({status:"reverted",time:Date.now()}),(e=(0,l.select)("core/block-editor").getBlocks(),ze(e,Ye)).map(rt)},nt=window.wc.customerEffortScore,st=({size:e=12})=>(0,W.jsx)(J.SVG,{width:e,height:e,viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,W.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M2.45865 9.08341L1.6665 9.87639L1.6665 1.66675L10.3332 1.66675L10.3332 9.08341L2.45865 9.08341ZM2.87317 10.0834L10.6665 10.0834C11.0347 10.0834 11.3332 9.78494 11.3332 9.41675L11.3332 1.33342C11.3332 0.965226 11.0347 0.666748 10.6665 0.666748H1.33317C0.964982 0.666748 0.666504 0.965225 0.666504 1.33341V11.0166C0.666504 11.2116 0.773993 11.3907 0.946074 11.4825C1.15124 11.5919 1.40385 11.5543 1.56818 11.3898L2.87317 10.0834ZM8.6665 4.66673H3.33317V3.66673H8.6665V4.66673ZM3.33317 7.33339H6.6665V6.33339H3.33317V7.33339Z",fill:"currentColor"})}),lt=({blockName:e,title:t=(0,C.__)("Share your experience","woocommerce"),firstQuestion:o=(0,C.sprintf)(/* translators: %s is the block name. */ /* translators: %s is the block name. */
(0,C.__)("It was easy for me to accomplish what I wanted with the %s.","woocommerce"),e),feedbackLabel:r=(0,C.sprintf)(/* translators: %s is the block name. */ /* translators: %s is the block name. */
(0,C.__)("How can we improve the %s block for you? (Optional)","woocommerce"),e),feedbackPlaceholder:c=(0,C.__)("What did you try to build using this block? What did and didn't work?","woocommerce"),emailLabel:n=(0,C.__)("Email address (Optional)","woocommerce"),emailHelp:s=(0,C.__)("Share if you would like to discuss your experience or participate in future research.","woocommerce"),buttonText:l=(0,C.__)("Help us improve","woocommerce"),submitLabel:a=(0,C.__)("🙏🏻 Thanks for sharing — we're on it!","woocommerce"),wrapper:i,wrapperProps:d={}})=>{const{showCesModal:u}=(0,nt.useCustomerEffortScoreModal)();if(!window.wcTracks?.isEnabled)return null;const m=(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(nt.CustomerEffortScoreModalContainer,{}),(0,W.jsx)(h.Button,{variant:"tertiary",icon:(0,W.jsx)(st,{}),iconSize:12,onClick:()=>{u({action:`${e.toLowerCase().replace(/\s+/g,"_")}_block_feedback`,title:t,firstQuestion:o,showDescription:!1,onsubmitLabel:a,getExtraFieldsToBeShown:(e,t,o)=>(0,W.jsxs)("div",{children:[(0,W.jsx)(h.TextareaControl,{label:r,value:e.feedback_comment||"",onChange:o=>t({...e,feedback_comment:o}),placeholder:c}),(0,W.jsx)(h.TextControl,{label:n,type:"email",value:e.email||"",onChange:o=>t({...e,email:o}),help:o?.email?(0,W.jsx)("span",{className:"woocommerce-customer-effort-score__errors",children:(0,W.jsx)("p",{children:o.email})}):s})]}),validateExtraFields:({email:e=""})=>{const t={};return e.length>0&&!(0,be.isEmail)(e)&&(t.email=(0,C.__)("Please enter a valid email address.","woocommerce")),t}},{blockName:e,shouldShowComments:()=>!1},{},{})},className:"wc-block-editor__feedback-button",children:l})]});return i?(0,W.jsx)(i,{...d,children:m}):m},at=window.wp.editor;var it=function(e){return e.SINGLE_PRODUCT="single-product",e.PRODUCT_CATALOG="product-catalog",e.PRODUCT_ARCHIVE="product-archive",e.ORDER_CONFIRMATION="order-confirmation",e.CART="cart",e.CHECKOUT="checkout",e.POST="post",e.PAGE="page",e.OTHER="other",e}(it||{});const dt={"single-product":it.SINGLE_PRODUCT,"archive-product":it.PRODUCT_CATALOG,"taxonomy-product_cat":it.PRODUCT_ARCHIVE,"taxonomy-product_tag":it.PRODUCT_ARCHIVE,"taxonomy-product_attribute":it.PRODUCT_ARCHIVE,"product-search-results":it.PRODUCT_ARCHIVE,"order-confirmation":it.ORDER_CONFIRMATION,"page-cart":it.CART,"page-checkout":it.CHECKOUT},ut=e=>{const t=(0,l.useSelect)((e=>e(at.store).getCurrentPostType()),[]);if(t===it.PAGE||t===it.POST)return t;if(!e)return it.OTHER;return dt[e]||(e.includes("single-product")?it.SINGLE_PRODUCT:e.includes("taxonomy-product_cat")||e.includes("taxonomy-product_tag")?it.PRODUCT_ARCHIVE:it.OTHER)};var mt=o(4921);function pt({children:e,className:t,actionLabel:o,onActionClick:r,...c}){return(0,W.jsx)(h.Notice,{...c,className:(0,mt.$)("wc-block-editor-components-upgrade-downgrade-notice",t),actions:[{label:o,onClick:r,noDefaultClasses:!0,variant:"link"}],children:(0,W.jsx)("div",{className:"wc-block-editor-components-upgrade-downgrade-notice__text",children:e})})}o(9969);const _t=(0,s.createInterpolateElement)((0,C.__)("Products (Beta) block was upgraded to <strongText />, an updated version with new features and simplified settings.","woocommerce"),{strongText:(0,W.jsx)("strong",{children:(0,C.__)("Product Collection","woocommerce")})}),ht=(0,C.__)("Revert to Products (Beta)","woocommerce"),gt=({revertMigration:e})=>{const[t,o]=((e,t)=>{const[o,r]=(0,s.useState)((()=>{const o=window.localStorage.getItem(e);if(o)try{return JSON.parse(o)}catch{console.error(`Value for key '${e}' could not be retrieved from localStorage because it can't be parsed.`)}return t}));return(0,s.useEffect)((()=>{try{window.localStorage.setItem(e,JSON.stringify(o))}catch{console.error(`Value for key '${e}' could not be saved in localStorage because it can't be converted into a string.`)}}),[e,o]),[o,r]})(Qe,We()),r=(0,s.useRef)(!0),{status:c}=t;return(0,s.useEffect)((()=>()=>{r.current&&((()=>{const e=Xe(),t=(0,Ke.isNumber)(e.displayCount)?e.displayCount+1:0;Je({...e,displayCount:t})})(),r.current=!1)}),[r]),"notseen"===c?(0,W.jsx)(pt,{actionLabel:ht,onActionClick:()=>{e(),E("blocks_product_collection_migration_between_products_beta",{transform_to:"products_beta"})},onRemove:()=>{o({status:"seen",time:Date.now()})},children:_t}):null},wt=(0,C.__)("Columns","woocommerce"),xt=(0,C.__)("Responsive","woocommerce"),bt=(0,C.__)("Automatically adjust the number of columns to better fit smaller screens.","woocommerce"),yt=e=>{const{type:t,columns:o,shrinkColumns:r}=e.displayLayout,c="flex"===t,n=Ne(),s=()=>{e.setAttributes({displayLayout:n})};return c?(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(h.__experimentalToolsPanelItem,{label:wt,hasValue:()=>n?.columns!==o,isShownByDefault:!0,onDeselect:s,children:(0,W.jsx)(h.RangeControl,{__next40pxDefaultSize:!0,label:wt,onChange:t=>e.setAttributes({displayLayout:{...e.displayLayout,columns:t}}),value:o,min:2,max:Math.max(6,o)})}),(0,W.jsx)(h.__experimentalToolsPanelItem,{label:xt,hasValue:()=>n?.shrinkColumns!==r,isShownByDefault:!0,onDeselect:s,children:(0,W.jsx)(h.ToggleControl,{checked:!!r,label:xt,help:bt,onChange:t=>{e.setAttributes({displayLayout:{...e.displayLayout,shrinkColumns:t}})}})})]}):null},ft=(0,C.__)("Query type","woocommerce"),Ct=(0,C.__)("Default","woocommerce"),vt=(0,C.__)("Custom","woocommerce"),kt=(0,C.__)("Display products based on the current template and allow shoppers to filter.","woocommerce"),St=(0,C.__)("Show products based on specific criteria and allow shoppers to filter.","woocommerce"),Et=(0,C.__)("Show a list of products based on fixed criteria.","woocommerce"),jt=({setQueryAttribute:e,trackInteraction:t,query:o})=>{const r=o?.inherit,c=function(e,t){const o=(0,s.useRef)();return(0,s.useEffect)((()=>{o.current===e||t&&!t(e,o.current)||(o.current=e)}),[e,t]),o.current}(o,(e=>!1===e?.inherit)),n=(0,s.useMemo)((()=>Le()),[]);return(0,W.jsx)(h.__experimentalToolsPanelItem,{label:ft,hasValue:()=>r!==n,isShownByDefault:!0,onDeselect:()=>{e({inherit:n}),t(f.INHERIT)},children:(0,W.jsxs)(h.__experimentalToggleGroupControl,{className:"wc-block-product-collection__inherit-query-control",isBlock:!0,label:ft,help:r?kt:Et,value:r?"default":"custom",onChange:o=>{e("default"===o?{...B,inherit:!0}:{...B,...c,inherit:!1}),t(f.INHERIT)},children:[(0,W.jsx)(h.__experimentalToggleGroupControlOption,{value:"default",label:Ct}),(0,W.jsx)(h.__experimentalToggleGroupControlOption,{value:"custom",label:vt})]})})},At=({setQueryAttribute:e,trackInteraction:t,query:o})=>{const r=o?.filterable,c=(0,s.useMemo)((()=>De()),[]);return(0,W.jsx)(h.__experimentalToolsPanelItem,{label:ft,hasValue:()=>r!==c,isShownByDefault:!0,onDeselect:()=>{e({filterable:c}),t(f.FILTERABLE)},children:(0,W.jsxs)(h.__experimentalToggleGroupControl,{className:"wc-block-product-collection__inherit-query-control",isBlock:!0,label:ft,help:r?St:Et,value:r?"default":"custom",onChange:o=>{e({filterable:"default"===o}),t(f.FILTERABLE)},children:[(0,W.jsx)(h.__experimentalToggleGroupControlOption,{value:"default",label:Ct}),(0,W.jsx)(h.__experimentalToggleGroupControlOption,{value:"custom",label:vt})]})})},Rt=(e,t)=>{if(!e)return null;if(0===e.innerBlocks.length)return null;for(const o of e.innerBlocks){if(t(o))return o;const e=Rt(o,t);if(e)return e}return null},It=(e,t)=>Rt(e,(function(e){return e.name===t})),Pt=({hasValue:e=()=>!0,orderOptions:t,onChange:o,onDeselect:r=()=>{},selectedValue:c,label:n,help:s})=>(0,W.jsx)(h.__experimentalToolsPanelItem,{label:n||(0,C.__)("Order by","woocommerce"),hasValue:e,isShownByDefault:!0,onDeselect:r,resetAllFilter:r,children:(0,W.jsx)(h.SelectControl,{value:c,options:t,label:n||(0,C.__)("Order by","woocommerce"),onChange:o,help:s})}),Tt=[{label:(0,C.__)("Newest to oldest","woocommerce"),value:"date"},{label:(0,C.__)("Price, high to low","woocommerce"),value:"price-desc"},{label:(0,C.__)("Price, low to high","woocommerce"),value:"price"},{label:(0,C.__)("Sales, high to low","woocommerce"),value:"popularity"},{label:(0,C.__)("Rating, high to low","woocommerce"),value:"rating"},{value:"menu_order",label:(0,C.__)("Manual (menu order + name)","woocommerce")}],Ot=({trackInteraction:e})=>{const t=(0,l.select)("core").getEditedEntityRecord("root","site"),[o,r]=(0,s.useState)(t.woocommerce_default_catalog_orderby||"menu_order");return(0,W.jsx)(Pt,{label:(0,C.__)("Default sort by","woocommerce"),selectedValue:o,orderOptions:Tt,onChange:t=>{r(t),(0,l.dispatch)(a.store).editEntityRecord("root","site",void 0,{woocommerce_default_catalog_orderby:t}),e(f.DEFAULT_ORDER)},help:(0,C.__)("All Product Collection blocks using the Default Query will sync to this sort order.","woocommerce")})},Lt=[{label:(0,C.__)("A → Z","woocommerce"),value:"title/asc"},{label:(0,C.__)("Z → A","woocommerce"),value:"title/desc"},{label:(0,C.__)("Newest to oldest","woocommerce"),value:"date/desc"},{label:(0,C.__)("Oldest to newest","woocommerce"),value:"date/asc"},{label:(0,C.__)("Price, high to low","woocommerce"),value:"price/desc"},{label:(0,C.__)("Price, low to high","woocommerce"),value:"price/asc"},{label:(0,C.__)("Sales, high to low","woocommerce"),value:"sales/desc"},{label:(0,C.__)("Sales, low to high","woocommerce"),value:"sales/asc"},{value:"rating/desc",label:(0,C.__)("Rating, high to low","woocommerce")},{value:"rating/asc",label:(0,C.__)("Rating, low to high","woocommerce")},{value:"menu_order/asc",label:(0,C.__)("Manual (menu order + name)","woocommerce")},{value:"random",label:(0,C.__)("Random","woocommerce")}],Dt=e=>{const{query:t,trackInteraction:o,setQueryAttribute:r}=e,{order:c,orderBy:n}=t;let s=c?`${n}/${c}`:n;return"popularity"===n&&(s=`sales/${c}`),(0,W.jsx)(Pt,{selectedValue:s,hasValue:()=>c!==B.order||n!==B.orderBy,orderOptions:Lt,onChange:e=>{const[t,c]=e.split("/");r({orderBy:t,order:c||void 0}),o(f.ORDER)},onDeselect:()=>{r({orderBy:B.orderBy}),o(f.ORDER)},help:(0,C.__)("Set the products order in this collection.","woocommerce")})},Nt=e=>{const{query:t,trackInteraction:o,setQueryAttribute:r}=e,c=()=>{r({woocommerceOnSale:q.woocommerceOnSale}),o(f.ON_SALE)};return(0,W.jsx)(h.__experimentalToolsPanelItem,{label:(0,C.__)("On Sale","woocommerce"),hasValue:()=>!0===t.woocommerceOnSale,isShownByDefault:!0,onDeselect:c,resetAllFilter:c,children:(0,W.jsx)(h.ToggleControl,{label:(0,C.__)("Show only products on sale","woocommerce"),checked:t.woocommerceOnSale||!1,onChange:e=>{r({woocommerceOnSale:e}),o(f.ON_SALE)}})})};function Bt(e){const t="string"==typeof e?e:e.value;return Object.entries(L).find((([,e])=>e===t))?.[0]}const Ft=e=>{const{query:t,trackInteraction:o,setQueryAttribute:r}=e,c=()=>{r({woocommerceStockStatus:q.woocommerceStockStatus}),o(f.STOCK_STATUS)};return(0,W.jsx)(h.__experimentalToolsPanelItem,{label:(0,C.__)("Stock Status","woocommerce"),hasValue:()=>!$e()(t.woocommerceStockStatus,N()),onDeselect:c,resetAllFilter:c,isShownByDefault:!0,children:(0,W.jsx)(h.FormTokenField,{label:(0,C.__)("Stock Status","woocommerce"),onChange:e=>{const t=e.map(Bt).filter(Boolean);r({woocommerceStockStatus:t}),o(f.STOCK_STATUS)},suggestions:Object.values(L),validateInput:e=>Object.values(L).includes(e),value:t?.woocommerceStockStatus?.map((e=>L[e]))||[],__experimentalExpandOnFocus:!0,__experimentalShowHowTo:!1})})},qt=e=>{const{query:t,trackInteraction:o,setQueryAttribute:r}=e,[c,n]=(0,s.useState)(t.search),l=(0,j.useDebounce)((()=>{t.search!==c&&(r({search:c}),o(f.KEYWORD))}),250);(0,s.useEffect)((()=>(l(),l.cancel)),[c,l]);const a=()=>{n(""),o(f.KEYWORD)};return(0,W.jsx)(h.__experimentalToolsPanelItem,{hasValue:()=>!!c,label:(0,C.__)("Keyword","woocommerce"),onDeselect:a,resetAllFilter:a,children:(0,W.jsx)(h.TextControl,{label:(0,C.__)("Keyword","woocommerce"),value:c,onChange:n})})};function Mt(e,t,o){const r=new Set(t.map((e=>e[o])));return e.filter((e=>!r.has(e[o])))}const Ut=window.wp.htmlEntities,Vt={clear:(0,C.__)("Clear all selected items","woocommerce"),noItems:(0,C.__)("No items found.","woocommerce"),
/* Translators: %s search term */
noResults:(0,C.__)("No results for %s","woocommerce"),search:(0,C.__)("Search for items","woocommerce"),selected:e=>(0,C.sprintf)(/* translators: Number of items selected from list. */ /* translators: Number of items selected from list. */
(0,C._n)("%d item selected","%d items selected",e,"woocommerce"),e),updated:(0,C.__)("Search results updated.","woocommerce")},Ht=(e,t=e)=>{const o=e.reduce(((e,t)=>{const o=t.parent||0;return e[o]||(e[o]=[]),e[o].push(t),e}),{}),r=t.reduce(((e,t)=>(e[String(t.id)]=t,e)),{});const c=["0"],n=(e={})=>e.parent?[...n(r[e.parent]),e.name]:e.name?[e.name]:[],s=e=>e.map((e=>{const t=o[e.id];return c.push(""+e.id),{...e,breadcrumbs:n(r[e.parent]),children:t&&t.length?s(t):[]}})),l=s(o[0]||[]);return Object.entries(o).forEach((([e,t])=>{c.includes(e)||l.push(...s(t||[]))})),l},Gt=(e,t)=>{if(!t)return e;const o=new RegExp(`(${t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")})`,"ig");return e.split(o).map(((e,t)=>o.test(e)?(0,W.jsx)("strong",{children:e},t):(0,W.jsx)(s.Fragment,{children:e},t)))},$t=({label:e})=>(0,W.jsx)("span",{className:"woocommerce-search-list__item-count",children:e}),Qt=e=>{const{item:t,search:o}=e,r=t.breadcrumbs&&t.breadcrumbs.length;return(0,W.jsxs)("span",{className:"woocommerce-search-list__item-label",children:[r?(0,W.jsx)("span",{className:"woocommerce-search-list__item-prefix",children:(c=t.breadcrumbs,1===c.length?c.slice(0,1).toString():2===c.length?c.slice(0,1).toString()+" › "+c.slice(-1).toString():c.slice(0,1).toString()+" … "+c.slice(-1).toString())}):null,(0,W.jsx)("span",{className:"woocommerce-search-list__item-name",children:Gt((0,Ut.decodeEntities)(t.name),o)})]});var c},Wt=({countLabel:e,className:t,depth:o=0,controlId:r="",item:c,isSelected:n,isSingle:l,onSelect:a,search:i="",selected:d,useExpandedPanelId:u,...m})=>{const[p,_]=u,g=null!=e&&void 0!==c.count&&null!==c.count,w=!!c.breadcrumbs?.length,x=!!c.children?.length,b=p===c.id,y=(0,mt.A)(["woocommerce-search-list__item",`depth-${o}`,t],{"has-breadcrumbs":w,"has-children":x,"has-count":g,"is-expanded":b,"is-radio-button":l});(0,s.useEffect)((()=>{x&&n&&_(c.id)}),[c,x,n,_]);const f=m.name||`search-list-item-${r}`,C=`${f}-${c.id}`,v=(0,s.useCallback)((()=>{_(b?-1:Number(c.id))}),[b,c.id,_]);return x?(0,W.jsx)("div",{className:y,onClick:v,onKeyDown:e=>"Enter"===e.key||" "===e.key?v():null,role:"treeitem",tabIndex:0,children:l?(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)("input",{type:"radio",id:C,name:f,value:c.value,onChange:a(c),onClick:e=>e.stopPropagation(),checked:n,className:"woocommerce-search-list__item-input",...m}),(0,W.jsx)(Qt,{item:c,search:i}),g?(0,W.jsx)($t,{label:e||c.count}):null]}):(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(h.CheckboxControl,{className:"woocommerce-search-list__item-input",checked:n,...!n&&c.children.some((e=>d.find((t=>t.id===e.id))))?{indeterminate:!0}:{},label:Gt((0,Ut.decodeEntities)(c.name),i),onChange:()=>{n?a(Mt(d,c.children,"id"))():a(function(e,t){const o=Mt(t,e,"id");return[...e,...o]}(d,c.children))()},onClick:e=>e.stopPropagation()}),g?(0,W.jsx)($t,{label:e||c.count}):null]})}):(0,W.jsxs)("label",{htmlFor:C,className:y,children:[l?(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)("input",{...m,type:"radio",id:C,name:f,value:c.value,onChange:a(c),checked:n,className:"woocommerce-search-list__item-input"}),(0,W.jsx)(Qt,{item:c,search:i})]}):(0,W.jsx)(h.CheckboxControl,{...m,id:C,name:f,className:"woocommerce-search-list__item-input",value:(0,Ut.decodeEntities)(c.value),label:Gt((0,Ut.decodeEntities)(c.name),i),onChange:a(c),checked:n}),g?(0,W.jsx)($t,{label:e||c.count}):null]})},Kt=Wt;var Yt=o(2624),zt=o(3028);o(5022);const Zt=({id:e,label:t,popoverContents:o,remove:r,screenReaderLabel:c,className:n=""})=>{const[l,a]=(0,s.useState)(!1),i=(0,j.useInstanceId)(Zt);if(c=c||t,!t)return null;t=(0,Ut.decodeEntities)(t);const d=(0,mt.A)("woocommerce-tag",n,{"has-remove":!!r}),u=`woocommerce-tag__label-${i}`,m=(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)("span",{className:"screen-reader-text",children:c}),(0,W.jsx)("span",{"aria-hidden":"true",children:t})]});return(0,W.jsxs)("span",{className:d,children:[o?(0,W.jsx)(h.Button,{className:"woocommerce-tag__text",id:u,onClick:()=>a(!0),children:m}):(0,W.jsx)("span",{className:"woocommerce-tag__text",id:u,children:m}),o&&l&&(0,W.jsx)(h.Popover,{onClose:()=>a(!1),children:o}),r&&(0,W.jsx)(h.Button,{className:"woocommerce-tag__remove",onClick:r(e),label:(0,C.sprintf)(
// Translators: %s label.
// Translators: %s label.
(0,C.__)("Remove %s","woocommerce"),t),"aria-describedby":u,children:(0,W.jsx)(R.A,{icon:zt.A,size:20,className:"clear-icon",role:"img"})})]})},Xt=Zt;o(1939);const Jt=e=>(0,W.jsx)(Kt,{...e}),eo=e=>{const{list:t,selected:o,renderItem:r,depth:c=0,onSelect:n,instanceId:l,isSingle:a,search:i,useExpandedPanelId:d}=e,[u]=d;return t?(0,W.jsx)(W.Fragment,{children:t.map((t=>{const m=t.children?.length&&!a?t.children.every((({id:e})=>o.find((t=>t.id===e)))):!!o.find((({id:e})=>e===t.id)),p=t.children?.length&&u===t.id;return(0,W.jsxs)(s.Fragment,{children:[(0,W.jsx)("li",{children:r({item:t,isSelected:m,onSelect:n,isSingle:a,selected:o,search:i,depth:c,useExpandedPanelId:d,controlId:l})}),p?(0,W.jsx)(eo,{...e,list:t.children,depth:c+1}):null]},t.id)}))}):null},to=({isLoading:e,isSingle:t,selected:o,messages:r,onChange:c,onRemove:n})=>{if(e||t||!o)return null;const s=o.length;return(0,W.jsxs)("div",{className:"woocommerce-search-list__selected",children:[(0,W.jsxs)("div",{className:"woocommerce-search-list__selected-header",children:[(0,W.jsx)("strong",{children:r.selected(s)}),s>0?(0,W.jsx)(h.Button,{variant:"link",isDestructive:!0,onClick:()=>c([]),"aria-label":r.clear,children:(0,C.__)("Clear all","woocommerce")}):null]}),s>0?(0,W.jsx)("ul",{children:o.map(((e,t)=>(0,W.jsx)("li",{children:(0,W.jsx)(Xt,{label:e.name,id:e.id,remove:n})},t)))}):null]})},oo=({filteredList:e,search:t,onSelect:o,instanceId:r,useExpandedPanelId:c,...n})=>{const{messages:s,renderItem:l,selected:a,isSingle:i}=n,d=l||Jt;return 0===e.length?(0,W.jsxs)("div",{className:"woocommerce-search-list__list is-not-found",children:[(0,W.jsx)("span",{className:"woocommerce-search-list__not-found-icon",children:(0,W.jsx)(R.A,{icon:Yt.A,role:"img"})}),(0,W.jsx)("span",{className:"woocommerce-search-list__not-found-text",children:t?(0,C.sprintf)(s.noResults,t):s.noItems})]}):(0,W.jsx)("ul",{className:"woocommerce-search-list__list",children:(0,W.jsx)(eo,{useExpandedPanelId:c,list:e,selected:a,renderItem:d,onSelect:o,instanceId:r,isSingle:i,search:t})})},ro=e=>{const{className:t="",isCompact:o,isHierarchical:r,isLoading:c,isSingle:n,list:l,messages:a=Vt,onChange:i,onSearch:d,selected:u,type:m="text",debouncedSpeak:p}=e,[_,g]=(0,s.useState)(""),w=(0,s.useState)(-1),x=(0,j.useInstanceId)(ro),b=(0,s.useMemo)((()=>({...Vt,...a})),[a]),y=(0,s.useMemo)((()=>((e,t,o)=>{if(!t)return o?Ht(e):e;const r=new RegExp(t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"),"i"),c=e.map((e=>!!r.test(e.name)&&e)).filter(Boolean);return o?Ht(c,e):c})(l,_,r)),[l,_,r]);(0,s.useEffect)((()=>{p&&p(b.updated)}),[p,b]),(0,s.useEffect)((()=>{"function"==typeof d&&d(_)}),[_,d]);const f=(0,s.useCallback)((e=>()=>{n&&i([]);const t=u.findIndex((({id:t})=>t===e));i([...u.slice(0,t),...u.slice(t+1)])}),[n,u,i]),v=(0,s.useCallback)((e=>()=>{Array.isArray(e)?i(e):-1===u.findIndex((({id:t})=>t===e.id))?i(n?[e]:[...u,e]):f(e.id)()}),[n,f,i,u]),k=(0,s.useCallback)((e=>{const[t]=u.filter((t=>!e.find((e=>t.id===e.id))));f(t.id)()}),[f,u]);return(0,W.jsxs)("div",{className:(0,mt.A)("woocommerce-search-list",t,{"is-compact":o,"is-loading":c,"is-token":"token"===m}),children:["text"===m&&(0,W.jsx)(to,{...e,onRemove:f,messages:b}),(0,W.jsx)("div",{className:"woocommerce-search-list__search",children:"text"===m?(0,W.jsx)(h.TextControl,{label:b.search,type:"search",value:_,onChange:e=>g(e)}):(0,W.jsx)(h.FormTokenField,{disabled:c,label:b.search,onChange:k,onInputChange:e=>g(e),suggestions:[],__experimentalValidateInput:()=>!1,value:c?[(0,C.__)("Loading…","woocommerce")]:u.map((e=>({...e,value:e.name}))),__experimentalShowHowTo:!1})}),c?(0,W.jsx)("div",{className:"woocommerce-search-list__list",children:(0,W.jsx)(h.Spinner,{})}):(0,W.jsx)(oo,{...e,search:_,filteredList:y,messages:b,onSelect:v,instanceId:x,useExpandedPanelId:w})]})},co=((0,h.withSpokenMessages)(ro),async e=>{if(!("json"in e))return{code:e.code||"",message:e.message,type:e.type||"general"};try{const t=await e.json();return{code:t.code||"",message:t.message,type:t.type||"api"}}catch(e){return{message:e.message,type:"general"}}});const no=window.wp.escapeHtml,so=({message:e,type:t})=>e?"general"===t?(0,W.jsxs)("span",{children:[(0,C.__)("The following error was returned","woocommerce"),(0,W.jsx)("br",{}),(0,W.jsx)("code",{children:(0,no.escapeHTML)(e)})]}):"api"===t?(0,W.jsxs)("span",{children:[(0,C.__)("The following error was returned from the API","woocommerce"),(0,W.jsx)("br",{}),(0,W.jsx)("code",{children:(0,no.escapeHTML)(e)})]}):e:(0,C.__)("An error has prevented the block from being updated.","woocommerce"),lo=({error:e})=>(0,W.jsx)("div",{className:"wc-block-error-message",children:so(e)});var ao=o(1609);const io=({className:e,item:t,isSelected:o,isLoading:r,onSelect:c,disabled:n,...s})=>(0,W.jsxs)(W.Fragment,{children:[(0,ao.createElement)(Wt,{...s,key:t.id,className:e,isSelected:o,item:t,onSelect:c,disabled:n}),o&&r&&(0,W.jsx)("div",{className:(0,mt.A)("woocommerce-search-list__item","woocommerce-product-attributes__item","depth-1","is-loading","is-not-active"),children:(0,W.jsx)(h.Spinner,{})},"loading")]}),uo=((0,P.getSetting)("attributes",[]).reduce(((e,t)=>{const o=(r=t)&&r.attribute_name?{id:parseInt(r.attribute_id,10),name:r.attribute_name,taxonomy:"pa_"+r.attribute_name,label:r.attribute_label,orderby:r.attribute_orderby}:null;var r;return o&&o.id&&e.push(o),e}),[]),e=>{const{count:t,id:o,name:r,parent:c}=e;return{count:t,id:o,name:r,parent:c,breadcrumbs:[],children:[],value:(0,Ke.isAttributeTerm)(e)?e.attr_slug:""}});o(2663);const mo=(0,j.withInstanceId)((({onChange:e,onOperatorChange:t,instanceId:o,isCompact:r=!1,messages:c={},operator:n="any",selected:l,type:a="text"})=>{const{errorLoadingAttributes:i,isLoadingAttributes:d,productsAttributes:u}=function(e){const[t,o]=(0,s.useState)(null),[r,c]=(0,s.useState)(!1),[n,l]=(0,s.useState)([]),a=(0,s.useRef)(!1);return(0,s.useEffect)((()=>{if(e&&!r&&!a.current)return async function(){c(!0);try{const e=await fe()({path:"wc/store/v1/products/attributes"}),t=[];for(const o of e){const e=await Ie(o.id);t.push({...o,parent:0,terms:e.map((e=>({...e,attr_slug:o.taxonomy,parent:o.id})))})}l(t),a.current=!0}catch(e){e instanceof Error&&o(await co(e))}finally{c(!1)}}(),()=>{a.current=!0}}),[r,e]),{errorLoadingAttributes:t,isLoadingAttributes:r,productsAttributes:n}}(!0),m=u.reduce(((e,t)=>{const{terms:o,...r}=t;return[...e,uo(r),...o.map(uo)]}),[]);return c={clear:(0,C.__)("Clear all product attributes","woocommerce"),noItems:(0,C.__)("Your store doesn't have any product attributes.","woocommerce"),search:(0,C.__)("Search for product attributes","woocommerce"),selected:e=>(0,C.sprintf)(/* translators: %d is the count of attributes selected. */ /* translators: %d is the count of attributes selected. */
(0,C._n)("%d attribute selected","%d attributes selected",e,"woocommerce"),e),updated:(0,C.__)("Product attribute search results updated.","woocommerce"),...c},i?(0,W.jsx)(lo,{error:i}):(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(ro,{className:"woocommerce-product-attributes",isCompact:r,isHierarchical:!0,isLoading:d,isSingle:!1,list:m,messages:c,onChange:e,renderItem:e=>{const{item:t,search:r,depth:c=0}=e,n=t.count||0,s=["woocommerce-product-attributes__item","woocommerce-search-list__item",{"is-searching":r.length>0,"is-skip-level":0===c&&0!==t.parent}];if(!t.breadcrumbs.length)return(0,W.jsx)(io,{...e,className:(0,mt.A)(s),item:t,isLoading:d,disabled:0===t.count,name:`attributes-${o}`,countLabel:(0,C.sprintf)(/* translators: %d is the count of terms. */ /* translators: %d is the count of terms. */
(0,C._n)("%d term","%d terms",n,"woocommerce"),n),"aria-label":(0,C.sprintf)(/* translators: %1$s is the item name, %2$d is the count of terms for the item. */ /* translators: %1$s is the item name, %2$d is the count of terms for the item. */
(0,C._n)("%1$s, has %2$d term","%1$s, has %2$d terms",n,"woocommerce"),t.name,n)});const l=`${t.breadcrumbs[0]}: ${t.name}`;return(0,W.jsx)(Wt,{...e,name:`terms-${o}`,className:(0,mt.A)(...s,"has-count"),countLabel:(0,C.sprintf)(/* translators: %d is the count of products. */ /* translators: %d is the count of products. */
(0,C._n)("%d product","%d products",n,"woocommerce"),n),"aria-label":(0,C.sprintf)(/* translators: %1$s is the attribute name, %2$d is the count of products for that attribute. */ /* translators: %1$s is the attribute name, %2$d is the count of products for that attribute. */
(0,C._n)("%1$s, has %2$d product","%1$s, has %2$d products",n,"woocommerce"),l,n)})},selected:l.map((({id:e})=>m.find((t=>t.id===e)))).filter(Boolean),type:a}),!!t&&(0,W.jsx)("div",{hidden:l.length<2,children:(0,W.jsx)(h.SelectControl,{className:"woocommerce-product-attributes__operator",label:(0,C.__)("Display products matching","woocommerce"),help:(0,C.__)("Pick at least two attributes to use this setting.","woocommerce"),value:n,onChange:t,options:[{label:(0,C.__)("Any selected attributes","woocommerce"),value:"any"},{label:(0,C.__)("All selected attributes","woocommerce"),value:"all"}]})})]})})),po=`${P.ADMIN_URL}edit.php?post_type=product&page=product_attributes`,_o=({query:e,trackInteraction:t,setQueryAttribute:o})=>{const r=e.woocommerceAttributes||[],c=r?.map((({termId:e})=>({id:e}))),n=()=>{o({woocommerceAttributes:q.woocommerceAttributes}),t(f.ATTRIBUTES)};return(0,W.jsxs)(h.__experimentalToolsPanelItem,{label:(0,C.__)("Product Attributes","woocommerce"),hasValue:()=>!!r?.length,onDeselect:n,resetAllFilter:n,children:[(0,W.jsx)(mo,{messages:{search:(0,C.__)("Attributes","woocommerce")},selected:c||[],onChange:e=>{const r=e.map((({id:e,value:t})=>({termId:e,taxonomy:t})));o({woocommerceAttributes:r}),t(f.ATTRIBUTES)},operator:"any",isCompact:!0,type:"token"}),(0,W.jsx)(h.ExternalLink,{className:"wc-block-editor-product-collection-panel__manage-attributes-link",href:po,children:(0,C.__)("Manage attributes","woocommerce")})]})},ho={_fields:"id,name",order:"asc",orderby:"name",context:"view"},go=e=>{var t;const o=e.match(/^(?:(.+) )?\(#(\d+)\)$/);return!!o&&{name:null!==(t=o[1])&&void 0!==t?t:"",id:parseInt(o[2],10)}},wo=({taxonomy:e,termIds:t,onChange:o})=>{const{existingTerms:r,isLoadingExistingTerms:c}=(0,l.useSelect)((o=>{if(!t||!t.length)return{existingTerms:[],isLoadingExistingTerms:!1};const{getEntityRecords:r,hasFinishedResolution:c}=o("core"),n=["taxonomy",e.slug,{...ho,include:t}];return{existingTerms:r(...n),isLoadingExistingTerms:!c("getEntityRecords",n)}}),[e,t]),[n,a]=(0,s.useState)(""),{searchTerms:i}=(0,l.useSelect)((o=>{if(n.length<=1)return{searchTerms:[]};const{getEntityRecords:r}=o("core");return{searchTerms:r("taxonomy",e.slug,{...ho,exclude:t,search:n})}}),[e,t,n]),d=(0,j.useDebounce)(a,250),u=new Set,m=new Set,p=e=>(u.has(e.name)&&m.add(e.name),u.add(e.name),(e=>`${e.name} (#${e.id})`)(e)),_=r?r.map(p):[],g=i?i.map(p):[];if(r&&t.length!==r.length){const e=r.reduce(((e,t)=>(e[t.id]=t,e)),{});t.forEach((t=>{e[t]||_.push(`(#${t})`)}))}return(0,W.jsx)("div",{className:"wc-block-editor-product-collection-inspector__taxonomy-control",children:(0,W.jsx)(h.FormTokenField,{label:e.name,value:_,onInputChange:d,onChange:e=>{const t=[];e.forEach((e=>{const o=go(e);o&&t.push(o.id)})),o(t)},suggestions:g,disabled:c,displayTransform:e=>{const t=go(e);return t&&(e=t.name?m.has(t.name)?`${t.name} (#${t.id})`:t.name:`(#${t.id} ${(0,C.__)("Missing","woocommerce")})`),(0,Ut.decodeEntities)(e)||""},__experimentalShowHowTo:!1})})},xo=()=>{const e=(0,l.useSelect)((e=>{const{getTaxonomies:t}=e(a.store);return t({type:"product",per_page:-1})}),[]);return(0,s.useMemo)((()=>e?.filter((({visibility:e})=>!!e?.publicly_queryable))),[e])},bo=e=>e?e.split(" ").map((e=>e.charAt(0).toUpperCase()+e.slice(1))).join(" "):"",yo=function({setQueryAttribute:e,trackInteraction:t,query:o,collection:r,renderMode:c="panel"}){const{filteredTaxonomies:n,taxQuery:l,createHandleChange:a,shouldShowTaxonomyControl:i}=function({setQueryAttribute:e,trackInteraction:t,query:o,collection:r,isFiltersPanel:c}){const{taxQuery:n}=o,l=xo(),a=(0,s.useMemo)((()=>l&&0!==l.length?r===y.BY_CATEGORY?l.filter((e=>c?"product_cat"!==e.slug:"product_cat"===e.slug)):r===y.BY_TAG?l.filter((e=>c?"product_tag"!==e.slug:"product_tag"===e.slug)):c?l:[]:[]),[l,r,c]),i=a.length>0;return{filteredTaxonomies:a,taxQuery:n,createHandleChange:o=>r=>{e({taxQuery:{...n,[o]:r}}),t(`${f.TAXONOMY}__${o}`)},shouldShowTaxonomyControl:i}}({query:o,collection:r,setQueryAttribute:e,trackInteraction:t,isFiltersPanel:"panel"===c});if(!i)return null;const d=e=>{const{slug:t}=e,o=l?.[t]||[],r=a(t);return(0,W.jsx)(wo,{taxonomy:e,termIds:o,onChange:r},t)};return(0,W.jsx)(W.Fragment,{children:n.map((e=>"panel"===c?(e=>{const{slug:t,name:o}=e,r=l?.[t]||[],c=a(t),n=()=>c([]);return(0,W.jsx)(h.__experimentalToolsPanelItem,{label:bo(o),hasValue:()=>r.length>0,onDeselect:n,resetAllFilter:n,children:d(e)},t)})(e):d(e)))})},fo=({query:e,trackInteraction:t,setQueryAttribute:o})=>{const r=(Ce.productCount||0)>100,c=e.woocommerceHandPickedProducts,[n,l]=(0,s.useState)(""),{productsMap:a,productsList:i,productsLoaded:d}=function(e,t,o=[]){const[r,c]=(0,s.useState)(new Map),[n,l]=(0,s.useState)([]),[a,i]=(0,s.useState)(!1);return(0,s.useEffect)((()=>{const r={selected:e?o.map(Number):[],queryArgs:e?{search:t,per_page:40}:{per_page:0}};Re(r).then((e=>{const t=new Map;e.forEach((e=>{t.set(e.id,e),t.set(e.name,e)})),l(e),c(t),i(!0)}))}),[e,t,o]),{productsMap:r,productsList:n,productsLoaded:a}}(r,n,c),u=(0,j.useDebounce)(l,250),m=(0,s.useMemo)((()=>c?.length&&a.size?c.filter((e=>!!a.get(Number(e)))):c||[]),[c,a]);(0,s.useEffect)((()=>{m.length!==c.length&&o({woocommerceHandPickedProducts:m})}),[m,c,o]);const p=(0,s.useCallback)((e=>{const r=e.reduce(((e,t)=>{const o=a.get(t)||a.get(Number(t));return o&&e.add(String(o.id)),e}),new Set);o({woocommerceHandPickedProducts:Array.from(r)}),t(f.HAND_PICKED)}),[o,t,a]),_=(0,s.useMemo)((()=>i.filter((e=>!m?.includes(String(e.id)))).map((e=>e.name))),[i,m]);return(0,W.jsx)(h.FormTokenField,{displayTransform:e=>{const t=Number(e);if(Number.isNaN(t))return(0,Ut.decodeEntities)(e)||"";const o=a.get(t);return(0,Ut.decodeEntities)(o?.name)||""},label:(0,C.__)("Hand-Picked","woocommerce"),onChange:p,onInputChange:r?u:void 0,suggestions:_,__experimentalValidateInput:e=>a.has(e),value:d?m||[]:[(0,C.__)("Loading…","woocommerce")],__experimentalExpandOnFocus:!0,__experimentalShowHowTo:!1,placeholder:(0,C.__)("Search for products to display…","woocommerce")})},Co=({query:e,trackInteraction:t,setQueryAttribute:o})=>{const r=e.woocommerceHandPickedProducts,c=()=>{o({woocommerceHandPickedProducts:q.woocommerceHandPickedProducts}),t(f.HAND_PICKED)};return(0,W.jsx)(h.__experimentalToolsPanelItem,{label:(0,C.__)("Hand-Picked","woocommerce"),hasValue:()=>!!r?.length,onDeselect:c,resetAllFilter:c,children:(0,W.jsx)(fo,{query:e,trackInteraction:t,setQueryAttribute:o})})},vo=e=>{switch(e){case w.GRID:return(0,C.__)("Display products using rows and columns.","woocommerce");case w.STACK:return(0,C.__)("Display products in a single column.","woocommerce");case w.CAROUSEL:return(0,C.__)("Display products in a carousel. It displays a single row of products.","woocommerce");default:return""}},ko=w.GRID,So=e=>{const{type:t,columns:o,shrinkColumns:r}=e.displayLayout,c=t=>{e.setAttributes({displayLayout:{type:t,columns:o,shrinkColumns:r}})};return(0,W.jsx)(h.__experimentalToolsPanelItem,{label:(0,C.__)("Layout","woocommerce"),hasValue:()=>t!==ko,isShownByDefault:!0,onDeselect:()=>{c(w.GRID)},children:(0,W.jsxs)(h.__experimentalToggleGroupControl,{label:(0,C.__)("Layout","woocommerce"),isBlock:!0,onChange:e=>{c(e)},help:vo(t),value:t,children:[(0,W.jsx)(h.__experimentalToggleGroupControlOption,{value:w.STACK,label:(0,C.__)("Stack","woocommerce")}),(0,W.jsx)(h.__experimentalToggleGroupControlOption,{value:w.GRID,label:(0,C.__)("Grid","woocommerce")}),Ae()&&(0,W.jsx)(h.__experimentalToggleGroupControlOption,{value:w.CAROUSEL,label:(0,C.__)("Carousel","woocommerce")})]})})},Eo=e=>{const{query:t,trackInteraction:o,setQueryAttribute:r}=e,c=()=>{r({featured:q.featured}),o(f.FEATURED)};return(0,W.jsx)(h.__experimentalToolsPanelItem,{label:(0,C.__)("Featured","woocommerce"),hasValue:()=>!0===t.featured,onDeselect:c,resetAllFilter:c,children:(0,W.jsx)(h.BaseControl,{id:"product-collection-featured-products-control",label:(0,C.__)("Featured","woocommerce"),children:(0,W.jsx)(h.ToggleControl,{label:(0,C.__)("Show only featured products","woocommerce"),checked:t.featured||!1,onChange:e=>{r({featured:e}),o(f.FEATURED)}})})})},jo=e=>{const{query:t,trackInteraction:o,setQueryAttribute:r}=e,{timeFrame:c}=t,n=()=>{r({timeFrame:q.timeFrame}),o(f.CREATED)};return(0,W.jsx)(h.__experimentalToolsPanelItem,{label:(0,C.__)("Created","woocommerce"),hasValue:()=>c?.operator&&c?.value,onDeselect:n,resetAllFilter:n,children:(0,W.jsxs)(h.Flex,{direction:"column",gap:3,children:[(0,W.jsx)(h.FlexItem,{children:(0,W.jsxs)(h.__experimentalToggleGroupControl,{label:(0,C.__)("Created","woocommerce"),isBlock:!0,onChange:e=>{r({timeFrame:{...c,operator:e}}),o(f.CREATED)},value:c?.operator||b.IN,children:[(0,W.jsx)(h.__experimentalToggleGroupControlOption,{value:b.IN,label:(0,C._x)("Within","Product Collection query operator","woocommerce")}),(0,W.jsx)(h.__experimentalToggleGroupControlOption,{value:b.NOT_IN,label:(0,C._x)("Before","Product Collection query operator","woocommerce")})]})}),(0,W.jsx)(h.FlexItem,{children:(0,W.jsx)(h.RadioControl,{onChange:e=>{r({timeFrame:{operator:b.IN,...c,value:e}}),o(f.CREATED)},options:[{label:"last 24 hours",value:"-1 day"},{label:"last 7 days",value:"-7 days"},{label:"last 30 days",value:"-30 days"},{label:"last 3 months",value:"-3 months"}],selected:c?.value})})]})})},Ao=window.wc.priceFormat,Ro=({value:e,onChange:t,label:o})=>{const[r,c]=(0,s.useState)(`${e||""}`),n=(0,Ao.getCurrency)(),l=((e="",t)=>{const o=e.replace(new RegExp(`[^0-9\\${t.decimalSeparator||""}]`,"g"),"").replace(new RegExp(`\\${t.decimalSeparator}`,"g"),"."),r=Number(o);if(""!==o&&!isNaN(r))return r<0?0:r})(r,n),a=((e,t)=>{if(void 0===e||isNaN(e))return;let o=((e,t)=>{const o=Math.pow(10,t.minorUnit),r=""+Math.round(e*o)/o;let[c,n]=r.split(".");return t.thousandSeparator&&(c=c.replace(/\B(?=(\d{3})+(?!\d))/g,t.thousandSeparator)),n?`${c}${t.decimalSeparator||"."}${n}`:c})(e,t);return t?.prefix&&(o=`${t.prefix}${o}`),t?.suffix&&(o=`${o}${t.suffix}`),o})(l,n);return(0,W.jsx)(h.__experimentalInputControl,{value:a,onChange:e=>{c(e)},onBlur:()=>{t(l)},onKeyDown:e=>{"Enter"===e.key&&t(l)},label:o,prefix:(0,W.jsx)(h.__experimentalInputControlPrefixWrapper,{children:o}),placeholder:(0,C.__)("Auto","woocommerce"),hideLabelFromVision:!0,type:"text",style:{textAlign:"right"},__next40pxDefaultSize:!0})},Io=e=>{const{query:t,trackInteraction:o,setQueryAttribute:r}=e,c=t.priceRange,n=()=>{r({priceRange:q.priceRange}),o(f.PRICE_RANGE)};return(0,W.jsxs)(h.__experimentalToolsPanelItem,{label:(0,C.__)("Price Range","woocommerce"),hasValue:()=>void 0!==c?.min||void 0!==c?.max,onDeselect:n,resetAllFilter:n,className:"wc-block-product-price-range-control",children:[(0,W.jsx)(h.BaseControl.VisualLabel,{children:(0,C.__)("PRICE RANGE","woocommerce")}),(0,W.jsxs)(h.__experimentalHStack,{spacing:"2",children:[(0,W.jsx)(Ro,{label:(0,C.__)("MIN","woocommerce"),value:c?.min,onChange:e=>{r({priceRange:{min:0===e?void 0:e,max:c?.max}}),o(f.PRICE_RANGE)}}),(0,W.jsx)(Ro,{label:(0,C.__)("MAX","woocommerce"),value:c?.max,onChange:e=>{const t=0===e?void 0:e;r({priceRange:{min:c?.min,max:t}}),o(f.PRICE_RANGE)}})]})]})};var Po=o(4347);var To=o(923),Oo=o.n(To);const Lo=(0,j.createHigherOrderComponent)((e=>{class t extends s.Component{state={error:null,loading:!1,variations:{}};componentDidMount(){const{selected:e,showVariations:t}=this.props;e&&t&&this.loadVariations()}componentDidUpdate(e){const{isLoading:t,selected:o,showVariations:r}=this.props;r&&(!Oo()(e.selected,o)||e.isLoading&&!t)&&this.loadVariations()}loadVariations=()=>{const{products:e}=this.props,{loading:t,variations:o}=this.state;if(t)return;const r=this.getExpandedProduct();if(!r||o[r])return;const c=e.find((e=>e.id===r));var n;c?.variations&&0!==c.variations.length?(this.setState({loading:!0}),(n=r,fe()({path:(0,be.addQueryArgs)("wc/store/v1/products",{per_page:0,type:"variation",parent:n})})).then((e=>{const t=e.map((e=>({...e,parent:r})));this.setState({variations:{...this.state.variations,[r]:t},loading:!1,error:null})})).catch((async e=>{const t=await co(e);this.setState({variations:{...this.state.variations,[r]:null},loading:!1,error:t})}))):this.setState({variations:{...this.state.variations,[r]:null},loading:!1,error:null})};isProductId(e){const{products:t}=this.props;return t.some((t=>t.id===e))}findParentProduct(e){const{products:t}=this.props,o=t.filter((t=>t.variations&&t.variations.find((({id:t})=>t===e))));return o[0]?.id}getExpandedProduct(){const{isLoading:e,selected:t,showVariations:o}=this.props;if(!o)return null;let r=t&&t.length?t[0]:null;return r?this.prevSelectedItem=r:!this.prevSelectedItem||e||this.isProductId(this.prevSelectedItem)||(r=this.prevSelectedItem),!e&&r?this.isProductId(r)?r:this.findParentProduct(r):null}render(){const{error:t,isLoading:o}=this.props,{error:r,loading:c,variations:n}=this.state;return(0,W.jsx)(e,{...this.props,error:r||t,expandedProduct:this.getExpandedProduct(),isLoading:o,variations:n,variationsLoading:c})}}return t}),"withProductVariations"),Do=e=>{const{id:t,name:o,parent:r}=e;return{id:t,name:o,parent:r,breadcrumbs:[],children:[],details:e,value:e.slug}};o(5653);const No={list:(0,C.__)("Products","woocommerce"),noItems:(0,C.__)("Your store doesn't have any products.","woocommerce"),search:(0,C.__)("Search for a product to display","woocommerce"),updated:(0,C.__)("Product search results updated.","woocommerce")},Bo=(e=>t=>{let{selected:o}=t;o=void 0===o?null:o;const r=null===o;return Array.isArray(o)?(0,W.jsx)(e,{...t}):(0,W.jsx)(e,{...t,selected:r?[]:[o]})})((Ho=Lo((0,j.withInstanceId)((e=>{const{expandedProduct:t=null,error:o,instanceId:r,isCompact:c=!1,isLoading:n,onChange:s,onSearch:l,products:a,renderItem:i,selected:d=[],showVariations:u=!1,variations:m,variationsLoading:p}=e;if(o)return(0,W.jsx)(lo,{error:o});const _=[...a,...m&&t&&m[t]?m[t]:[]].map(Do);return(0,W.jsx)(ro,{className:"woocommerce-products",list:_,isCompact:c,isLoading:n,isSingle:!0,selected:_.filter((({id:e})=>d.includes(Number(e)))),onChange:s,renderItem:i||(u?e=>{const{item:t,search:o,depth:c=0,isSelected:s,onSelect:l}=e,a=t.details?.variations&&Array.isArray(t.details.variations)?t.details.variations.length:0,i=(0,mt.A)("woocommerce-search-product__item","woocommerce-search-list__item",`depth-${c}`,"has-count",{"is-searching":o.length>0,"is-skip-level":0===c&&0!==t.parent,"is-variable":a>0});if(!t.breadcrumbs.length){const o=t.details?.variations&&t.details.variations.length>0;return(0,W.jsx)(io,{...e,className:(0,mt.A)(i,{"is-selected":s}),isSelected:s,item:t,onSelect:()=>()=>{l(t)()},isLoading:n||p,countLabel:o?(0,C.sprintf)(/* translators: %1$d is the number of variations of a product product. */ /* translators: %1$d is the number of variations of a product product. */
(0,C.__)("%1$d variations","woocommerce"),t.details?.variations.length):null,name:`products-${r}`,"aria-label":o?(0,C.sprintf)(/* translators: %1$s is the product name, %2$d is the number of variations of that product. */ /* translators: %1$s is the product name, %2$d is the number of variations of that product. */
(0,C._n)("%1$s, has %2$d variation","%1$s, has %2$d variations",t.details?.variations?.length,"woocommerce"),t.name,t.details?.variations.length):void 0})}const d=(0,Ke.isEmpty)(t.details?.variation)?e:{...e,item:{...e.item,name:t.details?.variation},"aria-label":`${t.breadcrumbs[0]}: ${t.details?.variation}`};return(0,W.jsx)(Wt,{...d,className:i,name:`variations-${r}`})}:void 0),onSearch:l,messages:{...No,...e.messages},isHierarchical:!0})}))),({selected:e,...t})=>{const[o,r]=(0,s.useState)(!0),[c,n]=(0,s.useState)(null),[l,a]=(0,s.useState)([]),i=Ce.productCount>100,d=async e=>{const t=await co(e);n(t),r(!1)},u=(0,s.useRef)(e);(0,s.useEffect)((()=>{Re({selected:u.current}).then((e=>{a(e),r(!1)})).catch(d)}),[u]);const m=(0,Po.YQ)((t=>{Re({selected:e,search:t}).then((e=>{a(e),r(!1)})).catch(d)}),400),p=(0,s.useCallback)((e=>{r(!0),m(e)}),[r,m]);return(0,W.jsx)(Ho,{...t,selected:e,error:c,products:l,isLoading:o,onSearch:i?p:null})})),Fo="product",qo="cart",Mo="order",Uo=({isOpen:e,onToggle:t,product:o,isLoading:r})=>{if(r&&!o)return(0,W.jsx)(h.Spinner,{});const c=!o,n=c||!o?.images?.[0]?.src,s=n?`${ve}/blocks/product-collection/placeholder.svg`:o.images[0].src,l=n?"":o?.name;return(0,W.jsx)(h.Button,{className:"wc-block-product-collection-linked-product-control__button",onClick:t,"aria-expanded":e,disabled:r,children:(0,W.jsxs)(h.Flex,{direction:"row",expanded:!0,justify:"flex-start",children:[(0,W.jsx)(h.FlexItem,{className:"wc-block-product-collection-linked-product-control__image-container",children:(0,W.jsx)("img",{src:s,alt:l})}),(0,W.jsx)(h.Flex,{direction:"column",align:"flex-start",gap:1,className:"wc-block-product-collection-linked-product-control__content",children:c?(0,W.jsx)(h.FlexItem,{children:(0,W.jsx)(h.__experimentalText,{color:"inherit",lineHeight:1,children:(0,C.__)("Select product","woocommerce")})}):(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(h.FlexItem,{children:(0,W.jsx)(h.__experimentalText,{color:"inherit",lineHeight:1,children:o?.name?(0,Ut.decodeEntities)(o.name):""})}),(0,W.jsx)(h.FlexItem,{children:(0,W.jsx)(h.__experimentalText,{color:"inherit",lineHeight:1,children:o?.sku})})]})})]})})},Vo=({query:e,setAttributes:t,setIsDropdownOpen:o})=>(0,W.jsx)(Bo,{selected:e?.productReference,onChange:(r=[])=>{var c;const n=null!==(c=r[0]?.id)&&void 0!==c?c:null;null!==n&&(t({query:{...e,productReference:n}}),o(!1))},messages:{search:(0,C.__)("Select a product","woocommerce")}});var Ho,Go=function(e){return e.CURRENT_PRODUCT="CURRENT_PRODUCT",e.SPECIFIC_PRODUCT="SPECIFIC_PRODUCT",e}(Go||{});const $o=({query:e,setAttributes:t,location:o,usesReference:r})=>{const c=o.type===Fo,n=!!r?.includes(Fo),l=o.type===qo,a=!!r?.includes(qo),i=o.type===Mo,d=!!r?.includes(Mo),{productReference:u}=e,{product:m,isLoading:p}=(e=>{const[t,o]=(0,s.useState)(null),[r,c]=(0,s.useState)(!1);return(0,s.useEffect)((()=>{(async()=>{if(e){c(!0);try{const t=await(e=>fe()({path:`/wc/store/v1/products/${e}`}))(e);o(t)}catch(e){o(null)}finally{c(!1)}}else o(null),c(!1)})()}),[e]),{product:t,isLoading:r}})(u),[_,g]=(0,s.useState)(!1),[w,x]=(0,s.useState)((c||l||i)&&(0,Ke.isEmpty)(u)?Go.CURRENT_PRODUCT:Go.SPECIFIC_PRODUCT),b=(0,s.useRef)(void 0),y=c&&n||l&&a||i&&d,f=y?w===Go.SPECIFIC_PRODUCT:!(0,Ke.isEmpty)(u);if(!y&&!f||!(n||a||d))return null;const v=w===Go.CURRENT_PRODUCT?(0,C.__)("Linked products will be pulled from the product a shopper is currently viewing","woocommerce"):(0,C.__)("Select a product to pull the linked products from","woocommerce"),k=((e,t,o)=>e===qo&&t?(0,C.__)("From products in the cart","woocommerce"):e===Mo&&o?(0,C.__)("From products in the order","woocommerce"):(0,C.__)("From the current product","woocommerce"))(o.type,a,d);return(0,W.jsxs)(h.PanelBody,{title:(0,C.__)("Linked Product","woocommerce"),children:[y&&(0,W.jsx)(h.PanelRow,{children:(0,W.jsx)(h.RadioControl,{className:"wc-block-product-collection-product-reference-radio",label:(0,C.__)("Products to show","woocommerce"),help:v,selected:w,options:[{label:k,value:Go.CURRENT_PRODUCT},{label:(0,C.__)("From a specific product","woocommerce"),value:Go.SPECIFIC_PRODUCT}],onChange:o=>{if(o===Go.CURRENT_PRODUCT){const{productReference:o,...r}=e;b.current=o,t({query:r})}else t({query:b.current?{...e,productReference:b.current}:e});x(o)}})}),f&&(0,W.jsx)(h.PanelRow,{children:(0,W.jsx)(h.Dropdown,{className:"wc-block-product-collection-linked-product-control",contentClassName:"wc-block-product-collection-linked-product__popover-content",popoverProps:{placement:"left-start"},renderToggle:({isOpen:e,onToggle:t})=>(0,W.jsx)(Uo,{isOpen:e,onToggle:t,product:m,isLoading:p}),renderContent:()=>(0,W.jsx)(Vo,{query:e,setAttributes:t,setIsDropdownOpen:g}),open:_,onToggle:()=>g(!_)})})]})},Qo=({dimensions:e,setAttributes:t})=>{const{widthType:o,fixedWidth:r=""}=e;return(0,W.jsxs)(h.__experimentalToolsPanelItem,{label:(0,C.__)("Width","woocommerce"),hasValue:()=>o!==x.FILL,isShownByDefault:!0,children:[(0,W.jsxs)(h.__experimentalToggleGroupControl,{label:(0,C.__)("Width","woocommerce"),value:o,help:(c=o,c===x.FILL?(0,C.__)("Stretch to fill available space.","woocommerce"):(0,C.__)("Specify a fixed width.","woocommerce")),onChange:o=>(o=>{t({dimensions:{...e,widthType:o}})})(o),isBlock:!0,children:[(0,W.jsx)(h.__experimentalToggleGroupControlOption,{value:x.FILL,label:(0,C.__)("Fill","woocommerce")}),(0,W.jsx)(h.__experimentalToggleGroupControlOption,{value:x.FIXED,label:(0,C.__)("Fixed","woocommerce")})]}),o===x.FIXED&&(0,W.jsx)(h.__experimentalUnitControl,{onChange:o=>{t({dimensions:{...e,fixedWidth:o}})},value:r})]});var c},Wo=({query:e,setQueryAttribute:t,trackInteraction:o})=>{const r=e?.relatedBy,c=(e,c)=>{const n={...r,[c]:e};t({relatedBy:n}),o(f.RELATED_BY)};return(0,W.jsx)(h.PanelBody,{title:(0,C.__)("Related by","woocommerce"),children:(0,W.jsxs)("div",{className:"wc-block-editor-product-collection-inspector-controls__relate-by",children:[(0,W.jsx)(h.CheckboxControl,{label:(0,C.__)("Categories","woocommerce"),checked:r?.categories,onChange:e=>{c(e,"categories")}}),(0,W.jsx)(h.CheckboxControl,{label:(0,C.__)("Tags","woocommerce"),checked:r?.tags,onChange:e=>{c(e,"tags")}})]})})},Ko=(0,C.__)("Products per page","woocommerce"),Yo=(0,C.__)("Products in carousel","woocommerce"),zo=({query:e,setQueryAttribute:t,trackInteraction:o,carouselVariant:r})=>{const c=()=>{t({perPage:B.perPage}),o(f.PRODUCTS_PER_PAGE)},n=(e=>e?Yo:Ko)(r),s=e.perPage||B.perPage,l=r&&s>30;return(0,W.jsxs)(h.__experimentalToolsPanelItem,{label:n,isShownByDefault:!0,hasValue:()=>e.perPage!==B.perPage,onDeselect:c,resetAllFilter:c,children:[l&&(0,W.jsx)("div",{children:(0,W.jsx)(h.Notice,{status:"warning",isDismissible:!1,className:"wc-block-editor-product-collection__carousel-warning",children:(0,C.__)("High product counts in carousel may impact performance. Consider reducing the number of products for better user experience.","woocommerce")})}),(0,W.jsx)(h.RangeControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,label:n,min:1,max:100,onChange:e=>{e<1||e>100||(t({perPage:e}),o(f.PRODUCTS_PER_PAGE))},value:s})]})},Zo=({query:e,setQueryAttribute:t,trackInteraction:o})=>{const r=()=>{t({offset:B.offset}),o(f.OFFSET)};return(0,W.jsx)(h.__experimentalToolsPanelItem,{label:(0,C.__)("Offset","woocommerce"),hasValue:()=>e.offset!==B.offset,onDeselect:r,resetAllFilter:r,children:(0,W.jsx)(h.__experimentalNumberControl,{__next40pxDefaultSize:!0,label:(0,C.__)("Offset","woocommerce"),value:e.offset,min:0,onChange:e=>{isNaN(e)||e<0||e>100||(t({offset:e}),o(f.OFFSET))}})})},Xo=({query:e,setQueryAttribute:t,trackInteraction:o})=>{const r=()=>{t({pages:B.pages}),o(f.MAX_PAGES_TO_SHOW)};return(0,W.jsx)(h.__experimentalToolsPanelItem,{label:(0,C.__)("Max pages to show","woocommerce"),hasValue:()=>e.pages!==B.pages,onDeselect:r,resetAllFilter:r,children:(0,W.jsx)(h.__experimentalNumberControl,{__next40pxDefaultSize:!0,label:(0,C.__)("Max pages to show","woocommerce"),value:e.pages,min:0,onChange:e=>{isNaN(e)||e<0||(t({pages:e}),o(f.MAX_PAGES_TO_SHOW))},help:(0,C.__)("Limit the pages you want to show, even if the query has more results. To show all pages use 0 (zero).","woocommerce")})})},Jo=e=>{const{attributes:t,context:o,setAttributes:a,clientId:i}=e,{query:d,hideControls:u,dimensions:m,displayLayout:p,collection:_}=t,g=ut(o.templateSlug),x=e=>E("blocks_product_collection_inspector_control_clicked",{collection:t.collection,location:g,filter:e}),b=d?.inherit||!1,y=(e=>t=>!e.includes(t))(u),v="product-catalog"===g||"product-archive"===g,k=p?.type===w.CAROUSEL;((e,t)=>{const{displayLayout:o,collection:c}=t,a=(0,s.useRef)(o.type),i=(0,l.useDispatch)(n.store),{productCollectionBlock:d,productTemplateBlock:u,productTemplateIndex:m}=(0,l.useSelect)((t=>{const o=t(n.store).getBlock(e),r=It(o,V),c=r?.clientId;return{productCollectionBlock:o,productTemplateBlock:r,productTemplateIndex:r?.clientId?t(n.store).getBlockIndex(c):0}}),[e]);(0,s.useEffect)((()=>{e&&(o?.type===w.CAROUSEL&&a.current!==w.CAROUSEL&&u&&((e,t,o,c)=>{const{removeBlock:n,insertBlock:s}=c,l=It(e,M),a=l?.clientId,i=t?.clientId,d=e?.clientId,u=(0,r.createBlock)(U),m=(0,r.createBlock)(V,{...t.attributes,layout:{type:"flex",justifyContent:"left",verticalAlignment:"top",flexWrap:"nowrap",orientation:"horizontal"}},t.innerBlocks),p=(0,r.createBlock)("core/group",{},[u,m]);n(i,!1),s(p,o,d,!1),a&&n(a,!1)})(d,u,m,i),o?.type!==w.CAROUSEL&&a.current===w.CAROUSEL&&((e,t,o)=>{const{removeBlock:c,insertBlock:s,replaceBlock:a}=t,i=It(e,U);i&&c(i?.clientId,!1);const d=Rt(e,(e=>"core/group"===e.name&&e.innerBlocks.some((e=>e.name===V)))),u=It(e,V),m=u?(0,r.createBlock)(V,{...u.attributes,layout:{}},u.innerBlocks):null;if(d){const t=(0,l.select)(n.store).getBlockIndex(d.clientId);m&&(c(u?.clientId,!1),s(m,t,e.clientId,!1)),!(0,l.select)(n.store).getClientIdsOfDescendants(d.clientId).length&&c(d.clientId,!1)}else u&&a(u?.clientId,m);o||s((0,r.createBlock)(M,G),e.innerBlocks.length,e.clientId,!1)})(d,i,c),a.current=o.type)}),[o.type,e,i,c,u,m])})(i,t);const S=!1===b,j=v&&y(f.INHERIT),A=!v&&y(f.FILTERABLE),R=S&&y(f.ORDER),I=!S,P=S&&y(f.OFFSET),T=!k,O=S&&!k&&y(f.MAX_PAGES_TO_SHOW),L=S&&y(f.PRODUCTS_PER_PAGE),D=y(f.ON_SALE),N=y(f.STOCK_STATUS),q=y(f.HAND_PICKED),H=y(f.KEYWORD),$=y(f.ATTRIBUTES),Q=y(f.TAXONOMY),K=y(f.FEATURED),Y=y(f.CREATED),z=y(f.PRICE_RANGE),Z={setAttributes:a,displayLayout:p},X={setAttributes:a,dimensions:m},J={setQueryAttribute:(0,s.useMemo)((()=>Pe.bind(null,e)),[e]),trackInteraction:x,query:d};return(0,W.jsxs)(n.InspectorControls,{children:[(0,W.jsx)($o,{query:e.attributes.query,setAttributes:e.setAttributes,usesReference:e.usesReference,location:e.location}),(0,W.jsxs)(h.__experimentalToolsPanel,{label:(0,C.__)("Settings","woocommerce"),resetAll:()=>{const t=(o=e.attributes,{displayLayout:Ne(),query:(r=o.query,{...r,orderBy:B.orderBy,order:B.order,inherit:Le(),filterable:De(),perPage:B.perPage,offset:B.offset,pages:B.pages}),dimensions:F.dimensions});var o,r;e.setAttributes(t)},className:"wc-block-editor-product-collection__settings_panel",children:[j&&(0,W.jsx)(jt,{...J}),A&&(0,W.jsx)(At,{...J}),R&&(0,W.jsx)(Dt,{...J}),I&&(0,W.jsx)(Ot,{trackInteraction:x}),(0,W.jsx)(So,{...Z}),(0,W.jsx)(Qo,{...X}),L&&(0,W.jsx)(zo,{...J,carouselVariant:k}),T&&(0,W.jsx)(yt,{...Z}),P&&(0,W.jsx)(Zo,{...J}),O&&(0,W.jsx)(Xo,{...J})]}),S?(0,W.jsxs)(h.__experimentalToolsPanel,{label:(0,C.__)("Filters","woocommerce"),resetAll:e=>{e.forEach((e=>{e()}))},className:"wc-block-editor-product-collection-inspector-toolspanel__filters",children:[D&&(0,W.jsx)(Nt,{...J}),N&&(0,W.jsx)(Ft,{...J}),q&&(0,W.jsx)(Co,{...J}),H&&(0,W.jsx)(qt,{...J}),$&&(0,W.jsx)(_o,{...J}),Q&&(0,W.jsx)(yo,{...J,collection:_,renderMode:"panel"}),K&&(0,W.jsx)(Eo,{...J}),Y&&(0,W.jsx)(jo,{...J}),z&&(0,W.jsx)(Io,{...J})]}):null,(0,W.jsx)(lt,{blockName:`${c.title} block`,wrapper:h.PanelBody})]})},er=e=>e===c.name,tr=e=>{const t=e+2592e5;return Date.now()<t},or=(e=0)=>e<=4,rr=e=>{const{collection:t}=e.attributes,o=(0,s.useMemo)((()=>Pe.bind(null,e)),[e]),r=ut(e.context.templateSlug),c={setQueryAttribute:o,trackInteraction:e=>E("blocks_product_collection_inspector_control_clicked",{collection:t,location:r,filter:e}),query:e.attributes.query},l=t===y.BY_CATEGORY||t===y.BY_TAG;return(0,W.jsxs)(n.InspectorControls,{children:[t===y.HAND_PICKED&&(0,W.jsx)(h.PanelBody,{children:(0,W.jsx)(fo,{...c})}),t===y.RELATED&&(0,W.jsx)(Wo,{...c}),l&&(0,W.jsx)(h.PanelBody,{children:(0,W.jsx)(yo,{...c,collection:t,renderMode:"standalone"})})]})};(0,xe.addFilter)("editor.BlockEdit",c.name,(e=>t=>er(t.name)?(0,W.jsxs)(W.Fragment,{children:[(0,W.jsx)(rr,{...t}),(0,W.jsx)(e,{...t})]}):(0,W.jsx)(e,{...t}))),(0,xe.addFilter)("editor.BlockEdit",c.name,(e=>t=>{if(!er(t.name))return(0,W.jsx)(e,{...t});const o=(e=>{const{attributes:t}=e,{convertedFromProducts:o}=t,{status:r,time:c,displayCount:n}=Xe();return o&&"notseen"===r&&tr(c)&&or(n)})(t),r=(e=>{const{attributes:t}=e,{convertedFromProducts:o}=t,{status:r,time:c,displayCount:n}=Xe();return o&&("seen"===r||!tr(c)||!or(n))})(t);return r&&t.setAttributes({convertedFromProducts:!1}),(0,W.jsxs)(W.Fragment,{children:[o&&(0,W.jsx)(n.InspectorControls,{children:(0,W.jsx)(gt,{revertMigration:ct})}),(0,W.jsx)(e,{...t})]})}));const cr=(0,C.__)("Enable to enforce full page reload on certain interactions, like using paginations controls.","woocommerce"),nr=(0,C.__)("Browsing between pages requires a full page reload.","woocommerce"),sr=(0,C.__)("Reload full page can't be disabled because there are incompatible blocks inside the Product Collection block.","woocommerce"),lr=e=>{const{clientId:t,forcePageReload:o,setAttributes:c}=e,a=(e=>(0,l.useSelect)((t=>{const{getClientIdsOfDescendants:o,getBlockName:c}=t(n.store);return o(e).find((e=>!(e=>{const t=Object.is((0,r.getBlockSupport)(e,"interactivity"),!0),o=(0,r.getBlockSupport)(e,"interactivity.clientNavigation");return t||o})(c(e))))||!1}),[e]))(t);(0,s.useEffect)((()=>{!o&&a&&c({forcePageReload:!0})}),[o,a,c]);let i=cr;return o&&(i=nr),a&&(i=sr),(0,W.jsx)(h.ToggleControl,{label:(0,C.__)("Reload full page","woocommerce"),help:i,checked:o,onChange:()=>c({forcePageReload:!o}),disabled:a})};function ar(e){const{clientId:t,attributes:o,setAttributes:r}=e,{forcePageReload:c}=o;return(0,W.jsx)(n.InspectorAdvancedControls,{children:(0,W.jsx)(lr,{clientId:t,forcePageReload:c,setAttributes:r})})}const ir=e=>(0,W.jsx)(h.ToolbarGroup,{children:(0,W.jsx)(h.ToolbarButton,{onClick:e.openCollectionSelectionModal,children:(0,C.__)("Choose collection","woocommerce")})});function dr(e){const{openCollectionSelectionModal:t}=e,o=we(e.attributes.collection),r=o?.scope?.includes("block")||void 0===o?.scope;return(0,W.jsx)(n.BlockControls,{children:r&&(0,W.jsx)(ir,{openCollectionSelectionModal:t})})}const ur=({preview:{setPreviewState:e,initialPreviewState:t}={},...o})=>{const r=(0,s.useRef)(!1),{clientId:c,attributes:a,setAttributes:d,location:u,isUsingReferencePreviewMode:m}=o;(({setPreviewState:e,location:t,attributes:o,setAttributes:r,isUsingReferencePreviewMode:c})=>{const n=e=>{r({__privatePreviewState:{...o.__privatePreviewState,...e}})},l=((e,t)=>t?e.type===i.Product?(0,C.__)("Actual products will vary depending on the product being viewed.","woocommerce"):(0,C.__)("Actual products will vary depending on the page being viewed.","woocommerce"):"")(t,c);(0,s.useLayoutEffect)((()=>{c&&r({__privatePreviewState:{isPreview:l.length>0,previewMessage:l}})}),[r,l,c]),(0,s.useLayoutEffect)((()=>{if(!e&&!c)return;const r=e?.({setState:n,location:t,attributes:o});return r||void 0}),[e]);const a=t.type===i.Archive?t.sourceData?.termId:null;(0,s.useLayoutEffect)((()=>{if(!e&&!c){const e=t.type===i.Archive&&null===a;r({__privatePreviewState:{isPreview:!!e&&!!o?.query?.inherit,previewMessage:(0,C.__)("Actual products will vary depending on the page being viewed.","woocommerce")}})}}),[o?.query?.inherit,l,a,t.type,r,e,c])})({setPreviewState:e,setAttributes:d,location:u,attributes:a,isUsingReferencePreviewMode:m});const p=(0,n.useBlockProps)(),_=(0,n.useInnerBlocksProps)({},{template:Q}),g=((e,t,o)=>{const r=(0,j.useInstanceId)(o),{getBlockParentsByBlockName:c}=(0,l.useSelect)(n.store);let a=r;return(0,s.useMemo)((()=>c(e,"core/block")),[c,e]).length>0&&(a=t.queryId),a})(c,a,ur),w={...F,query:{...F.query,inherit:Le(),filterable:De()},...a,queryId:g,...!!a.collection&&t&&{__privatePreviewState:t}};let b={};return x.FIXED===a?.dimensions?.widthType&&a?.dimensions?.fixedWidth&&(b={maxWidth:a.dimensions.fixedWidth,margin:"0 auto"}),(0,s.useEffect)((()=>{d(w),r.current=!0}),[]),r.current=r.current||$e()(a,w),r.current?(0,W.jsxs)("div",{...p,children:[a.__privatePreviewState?.isPreview&&o.isSelected&&(0,W.jsx)(h.Button,{variant:"primary",size:"small",showTooltip:!0,label:a.__privatePreviewState?.previewMessage,className:"wc-block-product-collection__preview-button","data-testid":"product-collection-preview-button",children:"Preview"}),(0,W.jsx)(Jo,{...o}),(0,W.jsx)(ar,{...o}),(0,W.jsx)(dr,{...o}),(0,W.jsx)("div",{..._,style:b})]}):null},mr=ur,pr=e=>{const{clientId:t,attributes:o,tracksLocation:r,closePatternSelectionModal:c}=e,{collection:a}=o,{replaceBlock:i}=(0,l.useDispatch)(n.store),[d,u]=(0,s.useState)(a),m=e=>{E("blocks_product_collection_collection_replaced_from_placeholder",{action:e,location:r}),c()};return(0,W.jsx)(h.Modal,{overlayClassName:"wc-blocks-product-collection__modal",title:(0,C.__)("What products do you want to show?","woocommerce"),onRequestClose:()=>m("close"),size:"large",children:(0,W.jsxs)("div",{className:"wc-blocks-product-collection__content",children:[(0,W.jsx)(Ve,{chosenCollection:d,onCollectionClick:u}),(0,W.jsxs)("div",{className:"wc-blocks-product-collection__footer",children:[(0,W.jsx)(h.Button,{variant:"tertiary",onClick:()=>m("cancel"),children:(0,C.__)("Cancel","woocommerce")}),(0,W.jsx)(h.Button,{variant:"primary",onClick:()=>{d&&(E("blocks_product_collection_collection_replaced_from_placeholder",{from:a,to:d,location:r}),Be(d,t,i))},children:(0,C.__)("Continue","woocommerce")})]})]})})},_r=e=>{const{attributes:t,isDeletedProductReference:o}=e,r=(0,n.useBlockProps)(),c=we(t.collection);if(!c)return null;const l=o?(0,C.__)("Previously selected product is no longer available.","woocommerce"):(0,s.createInterpolateElement)((0,C.sprintf)(/* translators: %s: collection title */ /* translators: %s: collection title */
(0,C.__)("<strong>%s</strong> requires a product to be selected in order to display associated items.","woocommerce"),c.title),{strong:(0,W.jsx)("strong",{})});return(0,W.jsx)("div",{...r,children:(0,W.jsxs)(h.Placeholder,{className:"wc-blocks-product-collection__editor-product-picker",children:[(0,W.jsxs)(h.__experimentalHStack,{alignment:"center",children:[(0,W.jsx)(R.A,{icon:Yt.A,className:"wc-blocks-product-collection__info-icon"}),(0,W.jsx)(h.__experimentalText,{children:l})]}),(0,W.jsx)(Bo,{selected:t.query?.productReference,onChange:(o=[])=>{var r;null!==(null!==(r=o[0]?.id)&&void 0!==r?r:null)&&e.setAttributes({query:{...t.query,productReference:o[0].id}})},messages:{search:(0,C.__)("Select a product","woocommerce")}})]})})};var hr=o(7715);function gr(e,{blockDescription:t,blockIcon:o,blockTitle:c,variationName:n,scope:s}){(0,r.registerBlockVariation)(e,{description:t,name:n,title:c,isActive:e=>e.__woocommerceNamespace===n,icon:{src:o},attributes:{__woocommerceNamespace:n},scope:s})}const wr=(0,C.__)("Product Summary","woocommerce"),xr=(R.A,hr.A,(0,C.__)("Display a short description about a product.","woocommerce")),br=`${c.name}/product-summary`;var yr=o(8992);const fr=JSON.parse('{"title":"Product Title","description":"Display the title of a product."}'),Cr=`${c.name}/product-title`;(0,r.registerBlockType)(c,{icon:()=>(0,W.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,W.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M19 11H5C4.72386 11 4.5 11.2239 4.5 11.5V17.5C4.5 17.7761 4.72386 18 5 18H19C19.2761 18 19.5 17.7761 19.5 17.5V11.5C19.5 11.2239 19.2761 11 19 11ZM5 9.5H19C20.1046 9.5 21 10.3954 21 11.5V17.5C21 18.6046 20.1046 19.5 19 19.5H5C3.89543 19.5 3 18.6046 3 17.5V11.5C3 10.3954 3.89543 9.5 5 9.5Z",fill:"currentColor"}),(0,W.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M18 7.5C18 7.77614 17.7761 8 17.5 8L6.5 8C6.22386 8 6 7.77614 6 7.5V7.5C6 7.22386 6.22386 7 6.5 7L17.5 7C17.7761 7 18 7.22386 18 7.5V7.5Z",fill:"currentColor"}),(0,W.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16 5C16 5.27614 15.7761 5.5 15.5 5.5L8.5 5.5C8.22386 5.5 8 5.27614 8 5V5C8 4.72386 8.22386 4.5 8.5 4.5L15.5 4.5C15.7761 4.5 16 4.72386 16 5V5Z",fill:"currentColor"})]}),edit:e=>{const{clientId:t,attributes:o,context:r}=e,c=((e,t)=>{const o=e.templateSlug||"",r=e.postId||null,c=(e=>t=>e.replace(`${t}-`,""))(o),a=(e=>t=>e.includes(t)&&e!==t)(o),h=a(d),g=a(u),w=a(m),[x,b]=(0,s.useState)(null),[y,f]=(0,s.useState)(null),[C,v]=(0,s.useState)(null);(0,s.useEffect)((()=>{if(h){const e=c(d);p("postType","product",e,b)}if(g){const e=c(u);p("taxonomy","product_cat",e,f)}if(w){const e=c(m);p("taxonomy","product_tag",e,v)}}),[h,g,w,c]);const{isInSingleProductBlock:k,isInSomeCartCheckoutBlock:S}=(0,l.useSelect)((e=>{const{getBlockParentsByBlockName:o}=e(n.store),r=e=>o(t,e).length>0;return{isInSingleProductBlock:r(["woocommerce/single-product"]),isInSomeCartCheckoutBlock:r(["woocommerce/cart","woocommerce/checkout","woocommerce/mini-cart-contents"])}}),[t]);if(k)return _(i.Product,{productId:r});if(S)return _(i.Cart);if(h)return _(i.Product,{productId:x});const E=(e=>t=>e===t)(o);if(E(d))return _(i.Product,{productId:null});if(g)return _(i.Archive,{taxonomy:"product_cat",termId:y});if(w)return _(i.Archive,{taxonomy:"product_tag",termId:C});if(E(u))return _(i.Archive,{taxonomy:"product_cat",termId:null});if(E(m))return _(i.Archive,{taxonomy:"product_tag",termId:null});if(E("taxonomy-product_attribute"))return _(i.Archive,{taxonomy:null,termId:null});if("page-cart"===o||"page-checkout"===o)return _(i.Cart);const j=E("order-confirmation");return _(j?i.Order:i.Site)})(r,t),w=ut(r.templateSlug),[x,b]=(0,s.useState)(!1),y=(0,l.useSelect)((e=>!!e(n.store).getBlocks(t).length),[t]),{productCollectionUIStateInEditor:f,isLoading:C}=(({location:e,usesReference:t,attributes:o,hasInnerBlocks:r})=>{const c=o.query?.productReference,{product:n,hasResolved:d}=(0,l.useSelect)((e=>{if(!c)return{product:null,hasResolved:!0};const{getEntityRecord:t,hasFinishedResolution:o}=e(a.store),r=["postType","product",c];return{product:t(...r),hasResolved:o("getEntityRecord",r)}}),[c]);return{productCollectionUIStateInEditor:(0,s.useMemo)((()=>{var s;const l=t?.includes(e.type),a=!!o.collection,d=t?.includes("product"),u=null!==(null!==(s=o.query?.productReference)&&void 0!==s?s:null);if(a&&d&&!l&&!u)return g.PRODUCT_REFERENCE_PICKER;if(a&&d&&!l&&u&&c&&(void 0===n||"trash"===n?.status))return g.DELETED_PRODUCT_REFERENCE;if(l){var m,p;const t=e.type===i.Archive&&null!==(null!==(m=e.sourceData?.termId)&&void 0!==m?m:null),o=e.type===i.Product&&null!==(null!==(p=e.sourceData?.productId)&&void 0!==p?p:null);if(!t&&!o&&!c)return g.VALID_WITH_PREVIEW}return r||a?g.VALID:g.COLLECTION_PICKER}),[e.type,e.sourceData?.termId,e.sourceData?.productId,t,o.collection,c,n,r,o.query?.productReference]),isLoading:!d}})({location:c,attributes:o,hasInnerBlocks:y,usesReference:e.usesReference});if(C)return(0,W.jsx)(h.Flex,{justify:"center",align:"center",children:(0,W.jsx)(h.Spinner,{})});const v={...e,openCollectionSelectionModal:()=>b(!0),location:c,isUsingReferencePreviewMode:f===g.VALID_WITH_PREVIEW};return(0,W.jsxs)(W.Fragment,{children:[(()=>{switch(f){case g.COLLECTION_PICKER:return(0,W.jsx)(He,{...e,tracksLocation:w});case g.PRODUCT_REFERENCE_PICKER:return(0,W.jsx)(_r,{...e,isDeletedProductReference:!1});case g.DELETED_PRODUCT_REFERENCE:return(0,W.jsx)(_r,{...e,isDeletedProductReference:!0});case g.VALID:case g.VALID_WITH_PREVIEW:return(0,W.jsx)(mr,{...v});default:return(0,W.jsx)(He,{...e,tracksLocation:w})}})(),x&&(0,W.jsx)(pr,{clientId:t,attributes:o,tracksLocation:w,closePatternSelectionModal:()=>b(!1)})]})},save:function({attributes:{tagName:e="div"}}){const t=n.useBlockProps.save(),o=n.useInnerBlocksProps.save(t);return(0,W.jsx)(e,{...o})}}),gr("core/post-excerpt",{blockDescription:xr,blockIcon:(0,W.jsx)(h.Icon,{icon:hr.A}),blockTitle:wr,variationName:br,scope:[]}),gr("core/post-title",{blockDescription:fr.description,blockIcon:(0,W.jsx)(h.Icon,{icon:yr.A}),blockTitle:fr.title,variationName:Cr,scope:["block","inserter"]}),ge.forEach((e=>(0,A.__experimentalRegisterProductCollection)(e))),(0,xe.addFilter)("blocks.registerBlockType","woocommerce/add-product-collection-block-to-parent-array-of-pagination-block",((e,t)=>t!==M?e:e?.ancestor?{...e,ancestor:[...e.ancestor,c.name]}:e?.parent?{...e,parent:[...e.parent,c.name]}:e))},2663:()=>{},5653:()=>{},1939:()=>{},5022:()=>{},9969:()=>{},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},9491:e=>{"use strict";e.exports=window.wp.compose},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},923:e=>{"use strict";e.exports=window.wp.isShallowEqual},5573:e=>{"use strict";e.exports=window.wp.primitives}},c={};function n(e){var t=c[e];if(void 0!==t)return t.exports;var o=c[e]={exports:{}};return r[e].call(o.exports,o,o.exports,n),o.exports}n.m=r,e=[],n.O=(t,o,r,c)=>{if(!o){var s=1/0;for(d=0;d<e.length;d++){for(var[o,r,c]=e[d],l=!0,a=0;a<o.length;a++)(!1&c||s>=c)&&Object.keys(n.O).every((e=>n.O[e](o[a])))?o.splice(a--,1):(l=!1,c<s&&(s=c));if(l){e.splice(d--,1);var i=r();void 0!==i&&(t=i)}}return t}c=c||0;for(var d=e.length;d>0&&e[d-1][2]>c;d--)e[d]=e[d-1];e[d]=[o,r,c]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},o=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,n.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var c=Object.create(null);n.r(c);var s={};t=t||[null,o({}),o([]),o(o)];for(var l=2&r&&e;"object"==typeof l&&!~t.indexOf(l);l=o(l))Object.getOwnPropertyNames(l).forEach((t=>s[t]=()=>e[t]));return s.default=()=>e,n.d(c,s),c},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.j=5201,(()=>{var e={5201:0};n.O.j=t=>0===e[t];var t=(t,o)=>{var r,c,[s,l,a]=o,i=0;if(s.some((t=>0!==e[t]))){for(r in l)n.o(l,r)&&(n.m[r]=l[r]);if(a)var d=a(n)}for(t&&t(o);i<s.length;i++)c=s[i],n.o(e,c)&&e[c]&&e[c][0](),e[c]=0;return n.O(d)},o=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var s=n.O(void 0,[94],(()=>n(3340)));s=n.O(s),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["product-collection"]=s})();