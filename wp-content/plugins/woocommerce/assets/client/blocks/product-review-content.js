(()=>{var e,t,r,o={3643:(e,t,r)=>{"use strict";const o=window.wp.blocks;var n=r(3791);const i=JSON.parse('{"$schema":"https://schemas.wp.org/trunk/block.json","apiVersion":3,"name":"woocommerce/product-review-content","title":"Review Content","category":"woocommerce","ancestor":["woocommerce/product-reviews"],"description":"Displays the contents of a product review.","textdomain":"woocommerce","usesContext":["commentId"],"attributes":{"textAlign":{"type":"string"}},"supports":{"color":{"gradients":true,"link":true,"__experimentalDefaultControls":{"background":true,"text":true}},"typography":{"fontSize":true,"lineHeight":true,"__experimentalFontFamily":true,"__experimentalFontWeight":true,"__experimentalFontStyle":true,"__experimentalTextTransform":true,"__experimentalTextDecoration":true,"__experimentalLetterSpacing":true,"__experimentalDefaultControls":{"fontSize":true}},"__experimentalBorder":{"radius":true,"color":true,"width":true,"style":true,"__experimentalDefaultControls":{"radius":true,"color":true,"width":true,"style":true}},"spacing":{"padding":["horizontal","vertical"],"__experimentalDefaultControls":{"padding":true}},"html":false}}');var s=r(4921),c=r(7723),l=r(6087);const a=window.wp.components,u=window.wp.coreData,p=window.wp.blockEditor;var d=r(790);r(6142),(0,o.registerBlockType)(i,{icon:n.A,edit:function({setAttributes:e,attributes:{textAlign:t},context:{commentId:r}}){const o=(0,p.useBlockProps)({className:(0,s.A)({[`has-text-align-${t}`]:t})}),[n]=(0,u.useEntityProp)("root","comment","content",r),i=(0,d.jsx)(p.BlockControls,{children:(0,d.jsx)(p.AlignmentControl,{value:t,onChange:t=>{void 0!==t&&e({textAlign:t})}})});return r&&n?(0,d.jsxs)(d.Fragment,{children:[i,(0,d.jsx)("div",{...o,children:(0,d.jsx)(a.Disabled,{children:(0,d.jsx)(l.RawHTML,{children:n.rendered},"html")})})]}):(0,d.jsxs)(d.Fragment,{children:[i,(0,d.jsx)("div",{...o,children:(0,d.jsx)("p",{children:(0,c._x)("Review Content","block title","woocommerce")})})]})}})},6142:()=>{},1609:e=>{"use strict";e.exports=window.React},790:e=>{"use strict";e.exports=window.ReactJSXRuntime},6087:e=>{"use strict";e.exports=window.wp.element},7723:e=>{"use strict";e.exports=window.wp.i18n},5573:e=>{"use strict";e.exports=window.wp.primitives}},n={};function i(e){var t=n[e];if(void 0!==t)return t.exports;var r=n[e]={exports:{}};return o[e].call(r.exports,r,r.exports,i),r.exports}i.m=o,e=[],i.O=(t,r,o,n)=>{if(!r){var s=1/0;for(u=0;u<e.length;u++){for(var[r,o,n]=e[u],c=!0,l=0;l<r.length;l++)(!1&n||s>=n)&&Object.keys(i.O).every((e=>i.O[e](r[l])))?r.splice(l--,1):(c=!1,n<s&&(s=n));if(c){e.splice(u--,1);var a=o();void 0!==a&&(t=a)}}return t}n=n||0;for(var u=e.length;u>0&&e[u-1][2]>n;u--)e[u]=e[u-1];e[u]=[r,o,n]},i.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return i.d(t,{a:t}),t},r=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,i.t=function(e,o){if(1&o&&(e=this(e)),8&o)return e;if("object"==typeof e&&e){if(4&o&&e.__esModule)return e;if(16&o&&"function"==typeof e.then)return e}var n=Object.create(null);i.r(n);var s={};t=t||[null,r({}),r([]),r(r)];for(var c=2&o&&e;"object"==typeof c&&!~t.indexOf(c);c=r(c))Object.getOwnPropertyNames(c).forEach((t=>s[t]=()=>e[t]));return s.default=()=>e,i.d(n,s),n},i.d=(e,t)=>{for(var r in t)i.o(t,r)&&!i.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},i.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),i.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.j=2753,(()=>{var e={2753:0};i.O.j=t=>0===e[t];var t=(t,r)=>{var o,n,[s,c,l]=r,a=0;if(s.some((t=>0!==e[t]))){for(o in c)i.o(c,o)&&(i.m[o]=c[o]);if(l)var u=l(i)}for(t&&t(r);a<s.length;a++)n=s[a],i.o(e,n)&&e[n]&&e[n][0](),e[n]=0;return i.O(u)},r=globalThis.webpackChunkwebpackWcBlocksMainJsonp=globalThis.webpackChunkwebpackWcBlocksMainJsonp||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var s=i.O(void 0,[94],(()=>i(3643)));s=i.O(s),((this.wc=this.wc||{}).blocks=this.wc.blocks||{})["product-review-content"]=s})();