"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[451],{341:(t,s,e)=>{e.r(s),e.d(s,{default:()=>h});var o=e(9525),a=e(5460),n=e(4921),c=e(2213),r=e(790);const u=({className:t})=>{const{cartNeedsPayment:s}=(0,a.V)();return s?(0,r.jsx)("div",{className:(0,n.A)("wc-block-cart__payment-options",t),children:(0,r.jsx)(c.A,{})}):null};var l=e(9030),d=e(690);const h=t=>{const s=(0,o.N)(d.attributes,t),{showButtonStyles:e,buttonHeight:a,buttonBorderRadius:n,className:c}=s;return(0,r.jsx)(l.W.Provider,{value:{showButtonStyles:e,buttonHeight:a,buttonBorderRadius:n},children:(0,r.jsx)(u,{className:c})})}}}]);