"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[631],{8084:(s,t,a)=>{a.r(t),a.d(t,{default:()=>n});var e=a(4656),o=a(910),r=a(5460),c=a(9865),l=a(790);const n=({className:s=""})=>{const{cartTotals:t}=(0,r.V)(),{isLoading:a}=(0,c.n)();if(!parseFloat(t.total_fees)&&!parseFloat(t.total_discount)&&!parseFloat(t.total_shipping))return null;const n=(0,o.getCurrencyFromPriceResponse)(t);return(0,l.jsx)(e.TotalsWrapper,{className:s,children:(0,l.jsx)(e.Subtotal,{currency:n,values:t,showSkeleton:a})})}}}]);