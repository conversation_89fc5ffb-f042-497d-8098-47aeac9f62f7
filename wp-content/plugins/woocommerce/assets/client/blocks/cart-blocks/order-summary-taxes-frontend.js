"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[262],{8962:(e,t,a)=>{a.r(t),a.d(t,{default:()=>p});var s=a(1616),r=a(4656),o=a(910),n=a(5460),c=a(9865),l=a(5703),i=a(790);const u={showRateAfterTaxName:{type:"boolean",default:(0,l.getSetting)("displayCartPricesIncludingTax",!1)},lock:{type:"object",default:{remove:!0,move:!1}}},p=(0,s.withFilteredAttributes)(u)((({className:e,showRateAfterTaxName:t})=>{const{cartTotals:a}=(0,n.V)(),{isLoading:s}=(0,c.n)();if((0,l.getSetting)("displayCartPricesIncludingTax",!1)||parseInt(a.total_tax,10)<=0)return null;const u=(0,o.getCurrencyFromPriceResponse)(a);return(0,i.jsx)(r.TotalsWrapper,{className:e,children:(0,i.jsx)(r.TotalsTaxes,{showRateAfterTaxName:t,currency:u,values:a,showSkeleton:s})})}))}}]);