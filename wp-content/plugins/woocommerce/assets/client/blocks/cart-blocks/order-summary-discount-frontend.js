"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[146],{3185:(e,o,n)=>{n.r(o),n.d(o,{default:()=>C});var s=n(749),r=n(4656),t=n(910),c=n(5460),a=n(5954),i=n(9865),u=n(1e3),l=n(790);const p=()=>{const{extensions:e,receiveCart:o,...n}=(0,c.V)(),s={extensions:e,cart:n,context:"woocommerce/cart"};return(0,l.jsx)(u.ExperimentalDiscountsMeta.Slot,{...s})},C=({className:e})=>{const{cartTotals:o,cartCoupons:n}=(0,c.V)(),{removeCoupon:u,isRemovingCoupon:C}=(0,a.k)("wc/cart"),{isLoading:k}=(0,i.n)();if(!n.length)return(0,l.jsx)(p,{});const m=(0,t.getCurrencyFromPriceResponse)(o);return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(r.TotalsWrapper,{className:e,children:(0,l.jsx)(s.n$,{cartCoupons:n,currency:m,isRemovingCoupon:C,removeCoupon:u,values:o,isLoading:k})}),(0,l.jsx)(p,{})]})}}}]);