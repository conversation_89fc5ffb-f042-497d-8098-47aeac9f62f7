import*as e from"@wordpress/interactivity";var t={7908:e=>{e.exports=import("@woocommerce/stores/store-notices")}},r={};function o(e){var a=r[e];if(void 0!==a)return a.exports;var s=r[e]={exports:{}};return t[e](s,s.exports,o),s.exports}o.d=(e,t)=>{for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);const a=(n={store:()=>e.store},i={},o.d(i,n),i),s=({preserveCartData:e=!1})=>{((e,{bubbles:t=!1,cancelable:r=!1,element:o,detail:a={}})=>{if(!CustomEvent)return;o||(o=document.body);const s=new CustomEvent(e,{bubbles:t,cancelable:r,detail:a});o.dispatchEvent(s)})("wc-blocks_added_to_cart",{bubbles:!0,cancelable:!0,detail:{preserveCartData:e}})};var n,i;function c(e,t){return!e.ok}function d(e){return Object.assign(new Error(e.message||"Unknown error."),{code:e.code||"unknown_error"})}const y=e=>({notice:e.message,type:"error",dismissible:!0}),l=e=>({notice:e,type:"notice",dismissible:!0}),p=(e,t,r)=>{const o=e.items,a=t.items,{productsPendingAdd:s=[],cartItemsPendingQuantity:n=[],cartItemsPendingDelete:i=[]}=r,c=o.filter((e=>e.key&&!a.some((t=>e.key===t.key))&&!i.includes(e.key))),d=a.filter((e=>{const t=o.find((t=>t.key===e.key));return t?!n.includes(e.key)&&e.quantity!==t.quantity:!s.includes(e.id)}));return[...c.map((e=>l('"%s" was removed from your cart.'.replace("%s",e.name)))),...d.map((e=>l('The quantity of "%1$s" was changed to %2$d.'.replace("%1$s",e.name).replace("%2$d",e.quantity.toString()))))]};let m=!1,u=3e3;function h({quantityChanges:e}){window.dispatchEvent(new CustomEvent("wc-blocks_store_sync_required",{detail:{type:"from_iAPI",quantityChanges:e}}))}const{state:v,actions:f}=(0,a.store)("woocommerce",{actions:{*removeCartItem(e){const t=JSON.stringify(v.cart);v.cart.items=v.cart.items.filter((t=>t.key!==e));try{const t=yield fetch(`${v.restUrl}wc/store/v1/cart/remove-item`,{method:"POST",headers:{Nonce:v.nonce,"Content-Type":"application/json"},body:JSON.stringify({key:e})}),r=yield t.json();if(c(t))throw d(r);const o={cartItemsPendingDelete:[e]},a=p(v.cart,r,o),s=r.errors.map(y);yield f.updateNotices([...a,...s],!0),v.cart=r,h({quantityChanges:o})}catch(e){v.cart=JSON.parse(t),f.showNoticeError(e)}},*addCartItem({id:e,quantity:t,variation:r}){let o=v.cart.items.find((t=>"variation"===t.type?!(e!==t.id||!t.variation||!r||t.variation.length!==r.length)&&((e,t)=>!(!Array.isArray(e.variation)||!Array.isArray(t))&&e.variation.length===t.length&&e.variation.every((({raw_attribute:e,value:r})=>t.some((t=>t.attribute===e&&(t.value.toLowerCase()===r.toLowerCase()||t.value&&""===r))))))(t,r):e===t.id));const a=o?"update-item":"add-item",n=JSON.stringify(v.cart),i={};o?(o.quantity=t,o.key&&(i.cartItemsPendingQuantity=[o.key])):(o={id:e,quantity:t,variation:r},v.cart.items.push(o),i.productsPendingAdd=[e]);try{const e=yield fetch(`${v.restUrl}wc/store/v1/cart/${a}`,{method:"POST",headers:{Nonce:v.nonce,"Content-Type":"application/json"},body:JSON.stringify(o)}),t=yield e.json();if(c(e))throw d(t);const r=p(v.cart,t,i),n=t.errors.map(y);yield f.updateNotices([...r,...n],!0),v.cart=t,s({preserveCartData:!0}),h({quantityChanges:i})}catch(e){v.cart=JSON.parse(n),f.showNoticeError(e)}},*batchAddCartItems(e){const t=JSON.stringify(v.cart),r={};try{const t=e.map((e=>{const t=v.cart.items.find((({id:t})=>e.id===t));return t?(t.quantity=e.quantity,t.key&&(r.cartItemsPendingQuantity=[t.key]),{method:"POST",path:"/wc/store/v1/cart/update-item",headers:{Nonce:v.nonce,"Content-Type":"application/json"},body:t}):(e={id:e.id,quantity:e.quantity,variation:e.variation},v.cart.items.push(e),r.productsPendingAdd=r.productsPendingAdd?[...r.productsPendingAdd,e.id]:[e.id],{method:"POST",path:"/wc/store/v1/cart/add-item",headers:{Nonce:v.nonce,"Content-Type":"application/json"},body:e})})),o=yield fetch(`${v.restUrl}wc/store/v1/batch`,{method:"POST",headers:{Nonce:v.nonce,"Content-Type":"application/json"},body:JSON.stringify({requests:t})}),a=yield o.json();a.responses?.forEach((e=>{if(c(o))throw d(e)}));const n=Array.isArray(a.responses)?a.responses.filter((e=>e.status>=200&&e.status<300)):[],i=Array.isArray(a.responses)?a.responses.filter((e=>e.status<200||e.status>=300)):[];if(n.length>0){const e=n[n.length-1]?.body,t=p(v.cart,e,r),o=n.flatMap((e=>{var t;return(null!==(t=e.body.errors)&&void 0!==t?t:[]).map(y)}));yield f.updateNotices([...t,...o],!0),v.cart=e,s({preserveCartData:!0}),h({quantityChanges:r})}yield f.updateNotices(i.filter((e=>e.body&&"object"==typeof e.body)).map((({body:e})=>y(e))))}catch(e){v.cart=JSON.parse(t),f.showNoticeError(e)}},*refreshCartItems(){if(!m){m=!0;try{const e=yield fetch(`${v.restUrl}wc/store/v1/cart`,{headers:{"Content-Type":"application/json"}}),t=yield e.json();if(c(e))throw d(t);v.cart=t,u=3e3}catch(e){setTimeout(f.refreshCartItems,u),u*=2}finally{m=!1}}},*showNoticeError(e){yield Promise.resolve().then(o.bind(o,7908));const{actions:t}=(0,a.store)("woocommerce/store-notices",{},{lock:"I acknowledge that using a private store means my plugin will inevitably break on the next store release."}),{code:r,message:s}=e,n=v.errorMessages?.[r]||s;t.addNotice({notice:n,type:"error",dismissible:!0}),console.error(e)},*updateNotices(e=[],t=!1){yield Promise.resolve().then(o.bind(o,7908));const{state:r,actions:s}=(0,a.store)("woocommerce/store-notices",{},{lock:"I acknowledge that using a private store means my plugin will inevitably break on the next store release."}),n=e.map((e=>s.addNotice(e))),{notices:i}=r;t&&i.map((({id:e})=>e)).filter((e=>!n.includes(e))).forEach((e=>s.removeNotice(e)))}}},{lock:!0});window.addEventListener("wc-blocks_store_sync_required",(async e=>{"from_@wordpress/data"===e.detail.type&&f.refreshCartItems()}));