import*as t from"@wordpress/interactivity";var e={d:(t,o)=>{for(var r in o)e.o(o,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:o[r]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)};const o=(a={getContext:()=>t.getContext,store:()=>t.store},i={},e.d(i,a),i),r=(0,o.store)("woocommerce/product-data",{state:{get productId(){const t=(0,o.getContext)("woocommerce/single-product");return t?.productId||r?.state?.templateState?.productId},get variationId(){const t=(0,o.getContext)("woocommerce/single-product");return t?.variationId||r?.state?.templateState?.variationId}},actions:{setVariationId:t=>{const e=(0,o.getContext)("woocommerce/single-product");void 0!==e?.variationId?e.variationId=t:void 0!==r?.state?.templateState?.variationId&&(r.state.templateState.variationId=t)}}},{lock:!0});var a,i;