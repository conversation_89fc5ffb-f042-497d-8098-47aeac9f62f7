/*! For license information please see fulfillments.js.LICENSE.txt */
(()=>{"use strict";var e={94931:(e,t,l)=>{var o=l(51609),n=Symbol.for("react.element"),r=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,s=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function a(e,t,l){var o,r={},a=null,m=null;for(o in void 0!==l&&(a=""+l),void 0!==t.key&&(a=""+t.key),void 0!==t.ref&&(m=t.ref),t)i.call(t,o)&&!c.hasOwnProperty(o)&&(r[o]=t[o]);if(e&&e.defaultProps)for(o in t=e.defaultProps)void 0===r[o]&&(r[o]=t[o]);return{$$typeof:n,type:e,key:a,ref:m,props:r,_owner:s.current}}t.Fragment=r,t.jsx=a,t.jsxs=a},39793:(e,t,l)=>{e.exports=l(94931)},51609:e=>{e.exports=window.React}},t={};function l(o){var n=t[o];if(void 0!==n)return n.exports;var r=t[o]={exports:{}};return e[o](r,r.exports,l),r.exports}l.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return l.d(t,{a:t}),t},l.d=(e,t)=>{for(var o in t)l.o(t,o)&&!l.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},l.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),l.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var o={};l.r(o),l.d(o,{default:()=>qe});var n=l(51609),r=l.n(n);const i=window.wp.element,s=window.wc.navigation,c=window.wp.i18n,a=window.wp.components,m=window.wp.data;var d;const u=[...Object.values(null!==(d=window.wcFulfillmentSettings.providers)&&void 0!==d?d:{}),{label:(0,c.__)("Other","woocommerce"),icon:null,value:"other"}],f=window.wp.apiFetch;var h=l.n(f);const p=(e,t)=>{if(e&&"object"==typeof e&&"message"in e&&"code"in e){const t=e;if("woocommerce_fulfillment_error"===t.code)return t.message}return t},x="SET_ORDER",_="SET_REFUNDS",w="SET_LOADING",g="SET_ERROR",v="SET_FULFILLMENTS",j="SET_FULFILLMENT",C="DELETE_FULFILLMENT",y={orderMap:{}},k={setOrder:(e,t)=>({type:x,orderId:e,order:t}),setRefunds:(e,t)=>({type:_,orderId:e,refunds:t}),setLoading:(e,t)=>({type:w,orderId:e,isLoading:t}),setError:(e,t)=>({type:g,orderId:e,error:t}),setFulfillments:(e,t)=>({type:v,orderId:e,fulfillments:t}),setFulfillment:(e,t,l)=>({type:j,orderId:e,fulfillmentId:t,fulfillment:l}),deleteFulfillmentRecord:(e,t)=>({type:C,orderId:e,fulfillmentId:t})},b={...k,saveFulfillment:(e,t,l)=>async({dispatch:o})=>{o.setLoading(e,!0),o.setError(e,null);try{const n=await h()({path:`/wc/v3/orders/${e}/fulfillments?notify_customer=${l}`,method:"POST",data:t});o.setFulfillment(e,n.fulfillment.id,n.fulfillment)}catch(t){o.setError(e,p(t,"Failed to save fulfillment"))}finally{o.setLoading(e,!1)}},updateFulfillment:(e,t,l)=>async({dispatch:o})=>{o.setLoading(e,!0),o.setError(e,null);try{const n=await h()({path:`/wc/v3/orders/${e}/fulfillments/${t.id}?notify_customer=${l}`,method:"PUT",data:t});o.setFulfillment(e,n.fulfillment.id,n.fulfillment)}catch(t){o.setError(e,p(t,"Failed to update fulfillment"))}finally{o.setLoading(e,!1)}},deleteFulfillment:(e,t,l)=>async({dispatch:o})=>{o.setLoading(e,!0),o.setError(e,null);try{await h()({path:`/wc/v3/orders/${e}/fulfillments/${t}?notify_customer=${l}`,method:"DELETE"}),o.deleteFulfillmentRecord(e,t)}catch(t){o.setError(e,p(t,"Failed to delete fulfillment"))}finally{o.setLoading(e,!1)}}},N={getOrder:e=>async({dispatch:t})=>{t.setLoading(e,!0),t.setError(e,null);try{const l=await h()({path:`/wc/v3/orders/${e}`,method:"GET"});if(t.setOrder(e,l),l.refunds.length>0){const l=await h()({path:`/wc/v3/orders/${e}/refunds`,method:"GET"});t.setRefunds(e,l)}}catch(l){t.setError(e,l instanceof Error?l.message:"Failed to load order")}finally{t.setLoading(e,!1)}},readFulfillments:e=>async({dispatch:t})=>{t.setLoading(e,!0),t.setError(e,null);try{const{fulfillments:l}=await h()({path:`/wc/v3/orders/${e}/fulfillments`,method:"GET"});t.setFulfillments(e,l)}catch(l){t.setError(e,l instanceof Error?l.message:"Failed to load fulfillments")}finally{t.setLoading(e,!1)}}},E=(0,m.createReduxStore)("order/fulfillments",{reducer:function(e=y,t){const l=e.orderMap[t.orderId]||{order:null,refunds:[],fulfillments:[],loading:!1,error:null};switch(t.type){case x:return{...e,orderMap:{...e.orderMap,[t.orderId]:{...l,order:t.order}}};case _:return{...e,orderMap:{...e.orderMap,[t.orderId]:{...l,refunds:t.refunds}}};case w:return{...e,orderMap:{...e.orderMap,[t.orderId]:{...l,loading:t.isLoading}}};case g:return{...e,orderMap:{...e.orderMap,[t.orderId]:{...l,error:t.error}}};case v:return{...e,orderMap:{...e.orderMap,[t.orderId]:{...l,fulfillments:t.fulfillments}}};case j:return{...e,orderMap:{...e.orderMap,[t.orderId]:{...l,fulfillments:[...l.fulfillments.filter((e=>e.id!==t.fulfillmentId)),t.fulfillment]}}};case C:return{...e,orderMap:{...e.orderMap,[t.orderId]:{...l,fulfillments:l.fulfillments.filter((e=>e.id!==t.fulfillmentId))}}};default:return e}},actions:b,selectors:{getState:e=>e,getOrder:(e,t)=>e.orderMap[t]?.order,getRefunds:(e,t)=>e.orderMap[t]?.refunds||[],isLoading:(e,t)=>!!e.orderMap[t]?.loading,getError:(e,t)=>e.orderMap[t]?.error||null,readFulfillments:(e,t)=>e.orderMap[t]?.fulfillments||[],readFulfillment:(e,t,l)=>e.orderMap[t]?.fulfillments?.find((e=>e.id===l))||null},resolvers:N});function S(e,t,l){if(!e)return l;const o=e.meta_data.find((e=>e.key===t))?.value;return o||l}function M(e){return S(e,"_items",[])}async function F(e){(0,m.dispatch)(E).invalidateResolution("getOrder",[e]);const t=await(0,m.resolveSelect)(E).getOrder(e);if(t){var l;const o=null!==(l=t.meta_data.find((e=>"_fulfillment_status"===e.key))?.value)&&void 0!==l?l:"no_fulfillments",n=document.querySelector(`.order-${e} td.fulfillment_status mark`);if(n){const e=window.wcFulfillmentSettings.order_fulfillment_statuses[o]||{label:(0,c.__)("Unknown","woocommerce"),background_color:"#f8f9fa",text_color:"#6c757d"},t=n.querySelector("span");if(t)t.textContent=e.label;else{const t=document.createElement("span");t.textContent=e.label,n.replaceChildren(t)}n.setAttribute("style",`background-color: ${e.background_color}; color: ${e.text_color};`)}}}function I(e){const t=u.find((t=>t.value===e));return t?t.label:""}(0,m.register)(E);const L="_shipping_option",H="_tracking_number",O="_tracking_url",T="_shipment_provider",V="_provider_name",P="tracking-number",B="manual-entry",R="no-info",D=P;var z=l(39793);const U={selectedOption:D,setSelectedOption:()=>{},trackingNumber:"",setTrackingNumber:()=>{},shipmentProvider:"",setShipmentProvider:()=>{},trackingUrl:"",setTrackingUrl:()=>{},providerName:"",setProviderName:()=>{}},Z=(0,n.createContext)(U),$=()=>{const e=r().useContext(Z);if(!e)throw new Error("useShipmentFormContext must be used within a ShipmentFormProvider");return e},q=({fulfillment:e=null,children:t})=>{const[l,o]=r().useState(U.selectedOption),[i,s]=r().useState(U.trackingNumber),[c,a]=r().useState(U.shipmentProvider),[m,d]=r().useState(U.trackingUrl),[u,f]=r().useState(U.providerName);r().useEffect((()=>{o(S(e,L,"tracking-number")),s(S(e,H,"")),a(S(e,T,"")),d(S(e,O,"")),f(S(e,V,""))}),[e]);const h=(0,n.useMemo)((()=>({selectedOption:l,setSelectedOption:o,trackingNumber:i,setTrackingNumber:s,shipmentProvider:c,setShipmentProvider:a,trackingUrl:m,setTrackingUrl:d,providerName:u,setProviderName:f})),[l,o,i,s,c,a,m,d,u,f]);return(0,z.jsx)(Z.Provider,{value:h,children:t})},A=(0,n.createContext)({order:null,fulfillment:null,setFulfillment:()=>{},selectedItems:[],setSelectedItems:()=>{},notifyCustomer:!0,setNotifyCustomer:()=>{}}),G=()=>{const e=r().useContext(A);if(!e)throw new Error("useFulfillmentContext must be used within a FulfillmentProvider");return e},K=({order:e,fulfillment:t,items:l,children:o})=>{const[i,s]=r().useState(null!=t?t:null),[c,a]=r().useState(!0),{selectedOption:m,trackingNumber:d,trackingUrl:u,shipmentProvider:f,providerName:h}=$(),[p,x]=(0,n.useState)(null!=l?l:[]);(0,n.useEffect)((()=>{x(null!=l?l:[])}),[l]),(0,n.useEffect)((()=>{var l,o,n,r;s(e?.id?{id:null!==(l=t?.id)&&void 0!==l?l:void 0,fulfillment_id:null!==(o=t?.id)&&void 0!==o?o:void 0,entity_id:String(e.id),entity_type:"WC_Order",is_fulfilled:null!==(n=t?.is_fulfilled)&&void 0!==n&&n,status:null!==(r=t?.status)&&void 0!==r?r:"unfulfilled",meta_data:[{id:0,key:L,value:m},{id:0,key:H,value:m===R?"":d},{id:0,key:O,value:m===R?"":u},{id:0,key:T,value:m===R?"":f},{id:0,key:V,value:m===R?"":h},{id:0,key:"_items",value:p.map((e=>({item_id:e.item_id,qty:e.selection.filter((e=>e.checked)).length}))).filter((e=>e.qty>0))}]}:null)}),[e,d,u,f,h,m,t,p]);const _=(0,n.useMemo)((()=>({order:e,fulfillment:i,setFulfillment:s,selectedItems:p,setSelectedItems:x,notifyCustomer:c,setNotifyCustomer:a})),[e,i,s,p,x,c,a]);return(0,z.jsx)(A.Provider,{value:_,children:o})},W=window.lodash,Y=(e,t)=>M(t).map((t=>{const l=e.line_items.find((e=>e.id===t.item_id));return{item_id:t.item_id,item:l||{},selection:(0,W.range)(t.qty).map((e=>({index:e,checked:!0})))}})),Q=(e,t)=>{const l={};return e.forEach((e=>{l[e.item_id]={...e}})),t.forEach((e=>{l[e.item_id]?l[e.item_id].selection=[...l[e.item_id].selection,...e.selection].map(((e,t)=>(e.index=t,e))):l[e.item_id]={...e}})),Object.values(l)},J=(e,t)=>{const l={};return e.forEach((e=>{l[e.item_id]={...e}})),t.forEach((e=>{l[e.item_id]?(l[e.item_id].selection.splice(0,e.selection.length),l[e.item_id].selection=l[e.item_id].selection.map(((e,t)=>(e.index=t,e)))):l[e.item_id]={...e}})),Object.values(l)},X=(e,t,l=[])=>{let o=(e=>{const t=[];return e.line_items.forEach((e=>{t.push({item_id:e.id,item:e,selection:(0,W.range)(e.quantity).map((e=>({index:e,checked:!1})))})})),t})(t);if(l.length>0){const e=l.reduce(((e,t)=>{const l=t.line_items.map((e=>({item_id:o.find((t=>t.item.product_id===e.product_id))?.item_id||e.id,item:e,selection:(0,W.range)(-e.quantity).map((e=>({index:e,checked:!0})))})));return Q(e,l)}),[]);o=J(o,e)}if(e.length>0){const l=e.reduce(((e,l)=>{const o=Y(t,l);return Q(e,o)}),[]);o=J(o,l)}return o.filter((e=>e.selection.length>0))},ee=(0,n.createContext)({fulfillments:[],setFulfillments:()=>{},order:null,setOrder:()=>{},refunds:[],setRefunds:()=>{},openSection:"",setOpenSection:()=>{},isEditing:!1,setIsEditing:()=>{}}),te=()=>{const e=r().useContext(ee);if(!e)throw new Error("useFulfillmentDrawerContext must be used within a FulfillmentDrawerProvider");return e},le=({orderId:e,children:t})=>{const[l,o]=(0,n.useState)("order"),[r,i]=(0,n.useState)(!1),[s,c]=(0,n.useState)(),[a,d]=(0,n.useState)(),[u,f]=(0,n.useState)();return(0,m.useSelect)((t=>{if(!e)return;const l=t(E),o=l.getOrder(e),n=l.readFulfillments(e),r=l.getRefunds(e);(0,W.isEqual)(o,u)||(f(o),i(!1)),(0,W.isEqual)(r,a)||(d(null!=r?r:[]),i(!1)),(0,W.isEqual)(n,s)||(c(null!=n?n:[]),i(!1))}),[e,s,u,a]),(0,n.useLayoutEffect)((()=>{u&&s&&X(s,u,a).length>0?o("order"):s&&1===s.length?o("fulfillment-"+s[0].id):o("")}),[e,s,u,a]),null===e?null:(0,z.jsx)(ee.Provider,{value:{fulfillments:null!=s?s:[],setFulfillments:c,order:null!=u?u:null,setOrder:f,refunds:null!=a?a:[],setRefunds:d,openSection:l,setOpenSection:o,isEditing:r,setIsEditing:i},children:t})};function oe({setError:e}){const{setIsEditing:t}=te(),{order:l,fulfillment:o,notifyCustomer:r}=G(),[i,s]=(0,n.useState)(!1),{saveFulfillment:d}=(0,m.useDispatch)(E);return(0,z.jsx)(a.Button,{variant:"secondary",onClick:async()=>{if(e(null),!o||!l)return;if(0===M(o).length)return void e((0,c.__)("Select items to be fulfilled.","woocommerce"));s(!0),await d(l.id,o,r);const n=(0,m.select)(E).getError(l.id);n?e(n):(F(l.id),t(!1)),s(!1)},__next40pxDefaultSize:!0,isBusy:i,children:(0,c.__)("Save as draft","woocommerce")})}function ne({setError:e}){const{setIsEditing:t}=te(),{order:l,fulfillment:o,notifyCustomer:r}=G(),[i,s]=(0,n.useState)(!1),{saveFulfillment:d}=(0,m.useDispatch)(E);return(0,z.jsx)(a.Button,{variant:"primary",onClick:async()=>{if(e(null),!o||!l)return;if(0===M(o).length)return void e((0,c.__)("Select items to be fulfilled.","woocommerce"));s(!0),o.is_fulfilled=!0,o.status="fulfilled",await d(l.id,o,r);const n=(0,m.select)(E).getError(l.id);n?e(n):(F(l.id),t(!1)),s(!1)},__next40pxDefaultSize:!0,isBusy:i,disabled:i,children:(0,c.__)("Fulfill items","woocommerce")})}const re=window.wc.currency;var ie=l.n(re);const se=window.wp.htmlEntities;function ce({item:e,quantity:t,currency:l,editMode:o,toggleItem:r,isChecked:i,isIndeterminate:s}){const[c,m]=(0,n.useState)(!1),d=(0,n.useContext)(re.CurrencyContext),u=d.getCurrencyConfig(),f=(e,t)=>{if(t||(t=u?.code||"USD"),u&&u.code===t)return d.formatAmount(e);const l=window.wcFulfillmentSettings.currency_symbols[t];return l?ie()({...u,symbol:(0,se.decodeEntities)(l),symbolPosition:u.symbolPosition,code:t}).formatAmount(e):`${t}${e}`};return(0,z.jsxs)(z.Fragment,{children:[(0,z.jsxs)("div",{className:["woocommerce-fulfillment-item-container",c?"woocommerce-fulfillment-item-expanded":""].join(" "),children:[o&&(0,z.jsx)("div",{className:"woocommerce-fulfillment-item-checkbox",children:(0,z.jsx)(a.CheckboxControl,{value:e.id,checked:i(e.id,-1),onChange:t=>{r(e.id,-1,t)},indeterminate:s(e.id),__nextHasNoMarginBottom:!0})}),o&&t>1&&(0,z.jsx)(a.Icon,{icon:c?"arrow-up-alt2":"arrow-down-alt2",onClick:()=>{m(!c)},size:16}),(0,z.jsxs)("div",{className:"woocommerce-fulfillment-item-title",children:[(0,z.jsx)("div",{className:"woocommerce-fulfillment-item-image-container",children:e.image?.src&&(0,z.jsx)("img",{src:e.image?.src,alt:e.name,width:32,height:32,className:"woocommerce-fulfillment-item-image"})}),(0,z.jsxs)("div",{className:"woocommerce-fulfillment-item-name-sku",children:[(0,z.jsx)("div",{className:"woocommerce-fulfillment-item-name",children:e.name}),e.sku&&(0,z.jsx)("span",{className:"woocommerce-fulfillment-item-sku",children:e.sku})]})]}),t>1&&(0,z.jsx)("div",{className:"woocommerce-fulfillment-item-quantity",children:"x"+t}),(0,z.jsx)("div",{className:"woocommerce-fulfillment-item-price",children:f(parseFloat(e.total)*(t/e.quantity),l)})]}),o&&c&&(0,z.jsx)("div",{className:"woocommerce-fulfillment-item-expansion",children:(0,W.range)(t).map((t=>(0,z.jsxs)("div",{className:"woocommerce-fulfillment-item-expansion-row",children:[o&&(0,z.jsx)("div",{className:"woocommerce-fulfillment-item-checkbox",children:(0,z.jsx)(a.CheckboxControl,{name:`fulfillment-item-${e.id}-${t}`,value:e.id+"-"+t,checked:i(e.id,t),onChange:l=>{r(e.id,t,l)},__nextHasNoMarginBottom:!0})}),(0,z.jsxs)("div",{className:"woocommerce-fulfillment-item-title",children:[(0,z.jsx)("div",{className:"woocommerce-fulfillment-item-image-container",children:(0,z.jsx)("img",{src:e.image.src,alt:e.name,width:32,height:32,className:"woocommerce-fulfillment-item-image"})}),(0,z.jsxs)("div",{className:"woocommerce-fulfillment-item-name-sku",children:[(0,z.jsx)("div",{className:"woocommerce-fulfillment-item-name",children:e.name}),e.sku&&(0,z.jsx)("span",{className:"woocommerce-fulfillment-item-sku",children:e.sku})]})]}),(0,z.jsx)("div",{className:"woocommerce-fulfillment-item-price",children:f(parseInt(e.total,10)/e.quantity,l)})]},"fulfillment-item-expansion-"+t)))})]})}function ae({editMode:e}){const{order:t,selectedItems:l,setSelectedItems:o}=G(),n=l.reduce(((e,t)=>e+t.selection.length),0),r=l.reduce(((e,t)=>e+t.selection.filter((e=>e.checked)).length),0),i=(e,t,n)=>{o(t<0?[...l.map((t=>t.item_id===e?{...t,selection:t.selection.map((e=>({...e,checked:n})))}:t))]:[...l.map((l=>(l.item_id===e&&l.selection.map((e=>(e.index===t&&(e.checked=n),e))),l)))])},s=(e,t)=>{if(t<0)return l.some((t=>t.item_id===e&&t.selection.every((e=>e.checked))));const o=l.find((t=>t.item_id===e));if(!o)return!1;const n=o.selection.find((e=>e.index===t));return!!n&&n.checked},m=e=>{const t=l.find((t=>t.item_id===e));if(!t)return!1;const o=t.selection.filter((e=>e.checked)).length;return o>0&&o<t.selection.length};return(0,z.jsxs)("ul",{className:"woocommerce-fulfillment-item-list",children:[(0,z.jsx)("li",{children:(0,z.jsxs)("div",{className:"woocommerce-fulfillment-item-bulk-select",children:[e&&(0,z.jsx)(a.CheckboxControl,{onChange:()=>{o(r===n?l.map((e=>({...e,selection:e.selection.map((e=>({...e,checked:!1})))}))):l.map((e=>({...e,selection:e.selection.map((e=>({...e,checked:!0})))}))))},checked:r===n,indeterminate:r>0&&r<n,__nextHasNoMarginBottom:!0}),(0,z.jsx)("div",{className:"woocommerce-fulfillment-item-bulk-select__label",children:(0,c.sprintf)((0,c._n)("%s selected","%s selected",r,"woocommerce"),r)})]})}),l.map((l=>{var o;return(0,z.jsx)("li",{children:(0,z.jsx)(ce,{item:l.item,quantity:l.selection.length,editMode:e,currency:null!==(o=t?.currency)&&void 0!==o?o:"",toggleItem:i,isChecked:s,isIndeterminate:m})},l.item_id)}))]})}function me({error:e}){const t=(0,n.useRef)(null);return(0,n.useLayoutEffect)((()=>{e&&t.current?.scrollIntoView({behavior:"smooth",block:"start",inline:"nearest"})}),[e]),(0,z.jsxs)("div",{className:"woocommerce-fulfillment-error-label",ref:t,children:[(0,z.jsx)("span",{className:"woocommerce-fulfillment-error-label__icon",children:(0,z.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,z.jsx)("path",{d:"M7.99996 13.3333C10.9455 13.3333 13.3333 10.9455 13.3333 7.99996C13.3333 5.05444 10.9455 2.66663 7.99996 2.66663C5.05444 2.66663 2.66663 5.05444 2.66663 7.99996C2.66663 10.9455 5.05444 13.3333 7.99996 13.3333Z",strokeWidth:"1.5"}),(0,z.jsx)("path",{d:"M8.66671 4.66663H7.33337V8.66663H8.66671V4.66663Z"}),(0,z.jsx)("path",{d:"M8.66671 10H7.33337V11.3333H8.66671V10Z"})]})}),(0,z.jsx)("span",{className:"woocommerce-fulfillment-error-label__text",children:e})]})}const de=()=>(0,z.jsx)("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,z.jsx)("path",{d:"M6.75 0.75C4.275 0.75 2.25 2.775 2.25 5.25C2.25 6.3 2.625 7.275 3.225 8.025L0.375 10.875L1.2 11.7L4.05 8.85C4.8 9.45 5.775 9.825 6.825 9.825C9.3 9.825 11.325 7.8 11.325 5.325C11.325 2.85 9.225 0.75 6.75 0.75ZM6.75 8.625C4.875 8.625 3.375 7.125 3.375 5.25C3.375 3.375 4.875 1.875 6.75 1.875C8.625 1.875 10.125 3.375 10.125 5.25C10.125 7.125 8.625 8.625 6.75 8.625Z",fill:"#1E1E1E"})}),ue=()=>(0,z.jsx)("svg",{width:"18",height:"14",viewBox:"0 0 18 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,z.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0.5 1.75C0.5 0.783502 1.2835 0 2.25 0L12.5 0V3H14.5607L18 6.43934V8.75C18 9.7165 17.2165 10.5 16.25 10.5H16.2377C16.2458 10.5822 16.25 10.6656 16.25 10.75C16.25 12.1307 15.1307 13.25 13.75 13.25C12.3693 13.25 11.25 12.1307 11.25 10.75C11.25 10.6656 11.2542 10.5822 11.2623 10.5H7.23766C7.24582 10.5822 7.25 10.6656 7.25 10.75C7.25 12.1307 6.13071 13.25 4.75 13.25C3.36929 13.25 2.25 12.1307 2.25 10.75C2.25 10.6656 2.25418 10.5822 2.26234 10.5H0.5V1.75ZM11 9V1.5H2.25C2.11193 1.5 2 1.61193 2 1.75V9H2.96464C3.41837 8.53716 4.05065 8.25 4.75 8.25C5.44935 8.25 6.08163 8.53716 6.53536 9H11ZM15.5354 9H16.25C16.3881 9 16.5 8.88807 16.5 8.75V7.06066L13.9393 4.5H12.5V8.58446C12.8677 8.37174 13.2946 8.25 13.75 8.25C14.4493 8.25 15.0816 8.53716 15.5354 9ZM3.7815 10.5C3.76094 10.5799 3.75 10.6637 3.75 10.75C3.75 11.3023 4.19772 11.75 4.75 11.75C5.30228 11.75 5.75 11.3023 5.75 10.75C5.75 10.6637 5.73906 10.5799 5.7185 10.5C5.60749 10.0687 5.21596 9.75 4.75 9.75C4.28404 9.75 3.89251 10.0687 3.7815 10.5ZM12.7815 10.5C12.7609 10.5799 12.75 10.6637 12.75 10.75C12.75 11.3023 13.1977 11.75 13.75 11.75C14.3023 11.75 14.75 11.3023 14.75 10.75C14.75 10.6637 14.7391 10.5799 14.7185 10.5C14.7144 10.4841 14.7099 10.4683 14.705 10.4526C14.5784 10.0456 14.1987 9.75 13.75 9.75C13.284 9.75 12.8925 10.0687 12.7815 10.5Z",fill:"#1E1E1E"})}),fe=()=>(0,z.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,z.jsx)("path",{d:"M14 1.5H2C1.86739 1.5 1.74021 1.55268 1.64645 1.64645C1.55268 1.74021 1.5 1.86739 1.5 2V14C1.5 14.1326 1.55268 14.2598 1.64645 14.3536C1.74021 14.4473 1.86739 14.5 2 14.5H14C14.1326 14.5 14.2598 14.4473 14.3536 14.3536C14.4473 14.2598 14.5 14.1326 14.5 14V2C14.5 1.86739 14.4473 1.74021 14.3536 1.64645C14.2598 1.55268 14.1326 1.5 14 1.5ZM2 0H14C14.5304 0 15.0391 0.210714 15.4142 0.585786C15.7893 0.960859 16 1.46957 16 2V14C16 14.5304 15.7893 15.0391 15.4142 15.4142C15.0391 15.7893 14.5304 16 14 16H2C1.46957 16 0.960859 15.7893 0.585786 15.4142C0.210714 15.0391 0 14.5304 0 14V2C0 1.46957 0.210714 0.960859 0.585786 0.585786C0.960859 0.210714 1.46957 0 2 0ZM3 5H4.5V6.5H3V5ZM4.5 9.5H3V11H4.5V9.5ZM6 5H13V6.5H6V5ZM13 9.5H6V11H13V9.5Z",fill:"#1E1E1E"})}),he=()=>(0,z.jsx)("svg",{width:"18",height:"14",viewBox:"0 0 18 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,z.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M0 2C0 0.89543 0.895431 0 2 0H16C17.1046 0 18 0.895431 18 2V12C18 13.1046 17.1046 14 16 14H2C0.89543 14 0 13.1046 0 12V2ZM2 1.5H16C16.2761 1.5 16.5 1.72386 16.5 2V2.93754L9.00005 8.5625L1.5 2.93746V2C1.5 1.72386 1.72386 1.5 2 1.5ZM1.5 4.81246V12C1.5 12.2761 1.72386 12.5 2 12.5H16C16.2761 12.5 16.5 12.2761 16.5 12V4.81254L9.00005 10.4375L1.5 4.81246Z",fill:"#1E1E1E"})}),pe=({copyText:e})=>(0,z.jsx)(a.Button,{size:"small",iconSize:14,onClick:()=>{navigator.clipboard.writeText(e)},icon:(0,z.jsx)("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,z.jsx)("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.68815 1.5835H9.81315C9.87065 1.5835 9.91732 1.63016 9.91732 1.68766V9.81266C9.91732 9.84029 9.90634 9.86679 9.88681 9.88632C9.86727 9.90586 9.84078 9.91683 9.81315 9.91683H1.68815C1.66052 9.91683 1.63403 9.90586 1.61449 9.88632C1.59496 9.86679 1.58398 9.84029 1.58398 9.81266V1.68766C1.58398 1.63016 1.63065 1.5835 1.68815 1.5835ZM0.333984 1.68766C0.333984 0.940163 0.940651 0.333496 1.68815 0.333496H9.81315C10.5615 0.333496 11.1673 0.940163 11.1673 1.68766V9.81266C11.1673 10.561 10.5615 11.1668 9.81315 11.1668H1.68815C1.329 11.1668 0.984566 11.0242 0.730611 10.7702C0.476655 10.5162 0.333984 10.1718 0.333984 9.81266V1.68766ZM12.4173 11.401V3.901H13.6673V11.401C13.6673 12.6668 12.6423 13.6668 11.3765 13.6668H2.20898V12.4168H11.3765C11.9515 12.4168 12.4173 11.9768 12.4173 11.401Z",fill:"#949494"})}),label:"Copy",__next40pxDefaultSize:!0}),xe=()=>(0,z.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 12 14",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,z.jsx)("path",{d:"M11.8333 2.83301L9.33329 0.333008L2.24996 7.41634L1.41663 10.7497L4.74996 9.91634L11.8333 2.83301ZM5.99996 12.4163H0.166626V13.6663H5.99996V12.4163Z",fill:"#1E1E1E"})}),_e=({providerKey:e})=>{const t=u.find((t=>t.value===e)),l=t?.icon;return t&&l?(0,z.jsx)("div",{className:"woocommerce-fulfillment-shipment-provider-icon",children:(0,z.jsx)("img",{src:l,alt:t.label},e)}):null};function we(){const[e,t]=(0,n.useState)(""),[l,o]=(0,n.useState)(null),[r,i]=(0,n.useState)(!1),[s,m]=(0,n.useState)(!1),{order:d}=G(),{trackingNumber:u,setTrackingNumber:f,trackingUrl:p,setTrackingUrl:x,setProviderName:_,shipmentProvider:w,setShipmentProvider:g}=$();(0,n.useEffect)((()=>{o(null)}),[d?.id]);const v=async()=>{o(null);try{m(!0);const{tracking_number_details:t}=await h()({path:`/wc/v3/orders/${d?.id}/fulfillments/lookup?tracking_number=${e}`,method:"GET"});if(!t.tracking_number)return void o((0,c.__)("No information found for this tracking number. Check the number or enter the details manually.","woocommerce"));f(t.tracking_number),x(t.tracking_url),g(t.shipping_provider),_(""),i(!1)}catch(e){o((0,c.__)("Failed to fetch shipment information.","woocommerce"))}finally{m(!1)}};return(0,n.useEffect)((()=>{(0,W.isEmpty)(u)&&i(!0)}),[u]),(0,z.jsxs)(z.Fragment,{children:[(0,z.jsx)("p",{className:"woocommerce-fulfillment-description",children:(0,c.__)("Provide the shipment tracking number to find the shipment provider and tracking URL.","woocommerce")}),r?(0,z.jsx)("div",{className:"woocommerce-fulfillment-input-container",children:(0,z.jsxs)("div",{className:"woocommerce-fulfillment-input-group",children:[(0,z.jsx)(a.TextControl,{type:"text",label:(0,c.__)("Tracking Number","woocommerce"),placeholder:(0,c.__)("Enter tracking number","woocommerce"),value:e,onChange:e=>{t(e)},onKeyDown:t=>{"Enter"!==t.key||s||(0,W.isEmpty)(e.trim())||v()},__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0}),(0,z.jsx)(a.Button,{variant:"secondary",text:"Find info",disabled:s||(0,W.isEmpty)(e.trim()),isBusy:s,onClick:v,__next40pxDefaultSize:!0})]})}):(0,z.jsxs)(z.Fragment,{children:[(0,z.jsxs)("div",{className:"woocommerce-fulfillment-input-container",children:[(0,z.jsx)("h4",{children:(0,c.__)("Tracking Number","woocommerce")}),(0,z.jsxs)("div",{className:"woocommerce-fulfillment-input-group space-between",children:[(0,z.jsx)("span",{onClick:()=>{i(!0),t(u)},role:"button",tabIndex:0,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||(i(!0),t(u))},style:{cursor:"pointer"},children:u}),(0,z.jsx)(a.Button,{size:"small",onClick:()=>{i(!0),t(u)},children:(0,z.jsx)(xe,{})})]})]}),(0,z.jsxs)("div",{className:"woocommerce-fulfillment-input-container",children:[(0,z.jsx)("h4",{children:(0,c.__)("Provider","woocommerce")}),(0,z.jsx)("div",{className:"woocommerce-fulfillment-input-group",children:(0,z.jsxs)("div",{children:[(0,z.jsx)(_e,{providerKey:w}),(0,z.jsx)("span",{children:I(w)})]})})]}),(0,z.jsxs)("div",{className:"woocommerce-fulfillment-input-container",children:[(0,z.jsx)("h4",{children:(0,c.__)("Tracking URL","woocommerce")}),(0,z.jsx)("div",{className:"woocommerce-fulfillment-input-group",children:(0,z.jsx)(a.ExternalLink,{href:p,style:{width:"100%",textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"},children:p})})]})]}),l&&(0,z.jsx)(me,{error:l})]})}const ge=({item:e})=>(0,z.jsxs)("div",{className:["woocommerce-fulfillment-shipping-provider-list-item","woocommerce-fulfillment-shipping-provider-list-item-"+e.value].join(" "),children:[e.icon&&(0,z.jsx)("div",{className:"woocommerce-fulfillment-shipping-provider-list-item-icon",children:(0,z.jsx)("img",{src:e.icon,alt:e.label})}),(0,z.jsx)("div",{className:"woocommerce-fulfillment-shipping-provider-list-item-label",children:e.label})]});function ve(){const{trackingNumber:e,setTrackingNumber:t,shipmentProvider:l,setShipmentProvider:o,providerName:n,setProviderName:r,trackingUrl:i,setTrackingUrl:s}=$();return(0,z.jsxs)(z.Fragment,{children:[(0,z.jsx)("p",{className:"woocommerce-fulfillment-description",children:(0,c.__)("Provide the shipment information for this fulfillment.","woocommerce")}),(0,z.jsx)("div",{className:"woocommerce-fulfillment-input-container",children:(0,z.jsx)("div",{className:"woocommerce-fulfillment-input-group",children:(0,z.jsx)(a.TextControl,{label:(0,c.__)("Tracking Number","woocommerce"),type:"text",placeholder:(0,c.__)("Enter tracking number","woocommerce"),value:e,onChange:e=>{t(e)},__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0})})}),(0,z.jsx)("div",{className:"woocommerce-fulfillment-input-container",children:(0,z.jsxs)("div",{className:"woocommerce-fulfillment-input-group",children:[(0,z.jsx)(a.ComboboxControl,{label:(0,c.__)("Provider","woocommerce"),__experimentalRenderItem:({item:e})=>(0,z.jsx)(ge,{item:e}),allowReset:!1,__next40pxDefaultSize:!0,value:l,options:u,onChange:e=>{o(e)},__nextHasNoMarginBottom:!0}),(0,z.jsx)("div",{className:"woocommerce-fulfillment-shipment-provider-search-icon",children:(0,z.jsx)(de,{})})]})}),"other"===l&&(0,z.jsx)("div",{className:"woocommerce-fulfillment-input-container",children:(0,z.jsx)("div",{className:"woocommerce-fulfillment-input-group",children:(0,z.jsx)(a.TextControl,{label:(0,c.__)("Provider Name","woocommerce"),type:"text",placeholder:(0,c.__)("Enter provider name","woocommerce"),value:n,onChange:e=>{r(e)},__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0})})}),(0,z.jsx)("div",{className:"woocommerce-fulfillment-input-container",children:(0,z.jsx)("div",{className:"woocommerce-fulfillment-input-group",children:(0,z.jsx)(a.TextControl,{label:(0,c.__)("Tracking URL","woocommerce"),type:"text",placeholder:(0,c.__)("Enter tracking URL","woocommerce"),value:i,onChange:e=>{s(e)},__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0})})})]})}function je({header:e,isCollapsable:t,initialState:l,size:o="medium",children:i}){const[s,c]=(0,n.useState)("expanded"===l),m=r().Children.toArray(i).length>0;return(0,z.jsxs)("div",{className:`woocommerce-fulfillment-card woocommerce-fulfillment-card__size-${o}`,children:[(0,z.jsxs)("div",{className:"woocommerce-fulfillment-card__header",children:[e,t&&(0,z.jsx)(a.Button,{__next40pxDefaultSize:!0,size:"small",onClick:()=>c(!s),children:(0,z.jsx)(a.Icon,{icon:s?"arrow-up-alt2":"arrow-down-alt2",size:16})})]}),s&&m&&(0,z.jsx)("div",{className:["woocommerce-fulfillment-card__body",t?"":"no-collapse"].join(" "),children:i})]})}function Ce(){const{selectedOption:e,setSelectedOption:t}=$(),l="radio-group-"+String(Math.floor(1e6*Math.random()));return(0,z.jsx)(je,{isCollapsable:!1,initialState:"expanded",header:(0,z.jsxs)(z.Fragment,{children:[(0,z.jsx)(ue,{}),(0,z.jsx)("h3",{children:(0,c.__)("Shipment Information","woocommerce")})]}),children:(0,z.jsxs)("div",{className:"woocommerce-fulfillment-shipment-information-options",children:[(0,z.jsxs)("div",{className:"woocommerce-fulfillment-shipment-information-option-tracking-number",children:[(0,z.jsx)(a.CheckboxControl,{type:"radio",name:l,value:P,checked:e===P,onChange:e=>e&&t(P),label:(0,c.__)("Tracking Number","woocommerce"),__nextHasNoMarginBottom:!0}),e===P&&(0,z.jsx)(we,{})]}),(0,z.jsxs)("div",{className:"woocommerce-fulfillment-shipment-information-option-manual-entry",children:[(0,z.jsx)(a.CheckboxControl,{type:"radio",name:l,value:B,checked:e===B,onChange:e=>e&&t(B),label:(0,c.__)("Enter manually","woocommerce"),__nextHasNoMarginBottom:!0}),e===B&&(0,z.jsx)(ve,{})]}),(0,z.jsx)("div",{className:"woocommerce-fulfillment-shipment-information-option-no-info",children:(0,z.jsx)(a.CheckboxControl,{type:"radio",name:l,value:R,checked:e===R,onChange:e=>e&&t(R),label:(0,c.__)("No shipment information","woocommerce"),__nextHasNoMarginBottom:!0})})]})})}function ye({type:e="fulfill"}){const{notifyCustomer:t,setNotifyCustomer:l}=G(),o={fulfill:(0,c.__)("Fulfillment notification","woocommerce"),remove:(0,c.__)("Removal update","woocommerce"),update:(0,c.__)("Update notification","woocommerce")},n={fulfill:(0,c.__)("Automatically send an email to the customer when the selected items are fulfilled.","woocommerce"),remove:(0,c.__)("Automatically send an email to the customer notifying that the fulfillment is cancelled.","woocommerce"),update:(0,c.__)("Automatically send an email to the customer when the fulfillment is updated.","woocommerce")};return(0,z.jsx)(je,{size:"small",isCollapsable:!1,initialState:"expanded",header:(0,z.jsxs)(z.Fragment,{children:[(0,z.jsx)(he,{}),(0,z.jsx)("h3",{children:o[e]||o.fulfill}),(0,z.jsx)(a.ToggleControl,{__nextHasNoMarginBottom:!0,checked:t,label:null,onChange:e=>{l(e)}})]}),children:(0,z.jsx)("p",{className:"woocommerce-fulfillment-description",children:n[e]||n.fulfill})})}const ke=()=>{const{order:e,fulfillments:t,refunds:l,openSection:o,setOpenSection:r,isEditing:i}=te(),[s,m]=(0,n.useState)(null);(0,n.useEffect)((()=>{m(null)}),[e?.id]);const d=(0,n.useMemo)((()=>X(t,null!=e?e:{line_items:[]},null!=l?l:[]).map((e=>({...e,selection:e.selection.map((e=>({...e,checked:!0})))})))),[t,e,l]);return e?0===d.length?null:(0,z.jsxs)("div",{className:["woocommerce-fulfillment-new-fulfillment-form",i?"woocommerce-fulfillment-new-fulfillment-form__disabled":"",0===t.length?"woocommerce-fulfillment-new-fulfillment-form__first":""].join(" "),children:[(0,z.jsxs)("div",{className:["woocommerce-fulfillment-new-fulfillment-form__header","order"===o?"is-open":""].join(" "),onClick:()=>{t.length>0&&r("order"===o?"":"order")},onKeyDown:e=>{t.length>0&&("Enter"!==e.key&&" "!==e.key||r("order"===o?"":"order"))},tabIndex:0,role:"button",children:[(0,z.jsx)("h3",{children:0===t.length?(0,c.__)("Order Items","woocommerce"):(0,c.__)("Pending Items","woocommerce")}),t.length>0&&(0,z.jsx)(a.Button,{__next40pxDefaultSize:!0,size:"small",children:(0,z.jsx)(a.Icon,{icon:"order"===o?"arrow-up-alt2":"arrow-down-alt2",size:16})})]}),!i&&"order"===o&&(0,z.jsxs)("div",{className:"woocommerce-fulfillment-new-fulfillment-form__content",children:[s&&(0,z.jsx)(me,{error:s}),(0,z.jsx)(q,{children:(0,z.jsxs)(K,{order:e,fulfillment:null,items:d,children:[(0,z.jsx)(ae,{editMode:!0}),(0,z.jsx)(Ce,{}),(0,z.jsx)(ye,{type:"fulfill"}),(0,z.jsxs)("div",{className:"woocommerce-fulfillment-item-actions",children:[(0,z.jsx)(oe,{setError:m}),(0,z.jsx)(ne,{setError:m})]})]})})]})]}):null},be=window.wc.remoteLogging,Ne=window.wc.tracks;class Ee extends n.Component{constructor(e){super(e),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){this.setState({errorInfo:t}),(0,Ne.bumpStat)("error","unhandled-js-error-during-render");const l=t.componentStack?.trim().split("\n").slice(0,10).map((e=>e.trim()));(0,be.captureException)(e,{severity:"critical",extra:{componentStack:l}})}handleRefresh=()=>{window.location.reload()};handleOpenSupport=()=>{window.open("https://wordpress.org/support/plugin/woocommerce/","_blank")};render(){return this.state.hasError?(0,z.jsxs)("div",{className:"woocommerce-global-error-boundary",children:[(0,z.jsx)("h1",{className:"woocommerce-global-error-boundary__heading",children:(0,c.__)("Oops, something went wrong","woocommerce")}),(0,z.jsx)("p",{className:"woocommerce-global-error-boundary__subheading",children:(0,c.__)("We’re sorry for the inconvenience. Please try reloading the page, or you can get support from the community forums.","woocommerce")}),(0,z.jsxs)("div",{className:"woocommerce-global-error-boundary__actions",children:[(0,z.jsx)(a.Button,{variant:"secondary",onClick:this.handleOpenSupport,children:(0,c.__)("Get Support","woocommerce")}),(0,z.jsx)(a.Button,{variant:"primary",onClick:this.handleRefresh,children:(0,c.__)("Reload Page","woocommerce")})]}),(0,z.jsxs)("details",{className:"woocommerce-global-error-boundary__details",children:[(0,z.jsx)("summary",{children:(0,c.__)("Click for error details","woocommerce")}),(0,z.jsxs)("div",{className:"woocommerce-global-error-boundary__details-content",children:[(0,z.jsx)("strong",{className:"woocommerce-global-error-boundary__error",children:this.state.error&&this.state.error.toString()}),(0,z.jsx)("p",{children:this.state.errorInfo&&this.state.errorInfo.componentStack})]})]})]}):this.props.children}}function Se({onClick:e}){return(0,z.jsx)(a.Button,{variant:"secondary",onClick:e,__next40pxDefaultSize:!0,children:(0,c.__)("Edit fulfillment","woocommerce")})}function Me({onClick:e}){return(0,z.jsx)(a.Button,{variant:"link",onClick:e,style:{flex:1},__next40pxDefaultSize:!0,children:(0,c.__)("Cancel","woocommerce")})}function Fe({setError:e}){const{setIsEditing:t,setOpenSection:l}=te(),{order:o,fulfillment:r,notifyCustomer:i}=G(),[s,d]=(0,n.useState)(!1),{deleteFulfillment:u}=(0,m.useDispatch)(E),[f,h]=(0,n.useState)(!1),p=()=>h(!1),x=async()=>{if(e(null),!(r&&r.id&&o&&o.id))return;d(!0),await u(o.id,r.id,i);const n=(0,m.select)(E).getError(o.id);n?e(n):(F(o.id),l("order"),t(!1)),d(!1)};return(0,z.jsxs)(z.Fragment,{children:[(0,z.jsx)(a.Button,{variant:"secondary",onClick:e=>{e.stopPropagation(),e.preventDefault(),r&&!s&&(r.is_fulfilled?h(!0):x())},isBusy:s,__next40pxDefaultSize:!0,children:(0,c.__)("Remove","woocommerce")}),f&&(0,z.jsxs)(a.Modal,{title:(0,c.__)("Remove fulfillment","woocommerce"),onRequestClose:p,size:"medium",isDismissible:!1,className:"woocommerce-fulfillment-modal",children:[(0,z.jsx)("p",{className:"woocommerce-fulfillment-modal-text",children:(0,c.__)("Are you sure you want to remove this fulfillment?","woocommerce")}),(0,z.jsx)(ye,{type:"remove"}),(0,z.jsxs)("div",{className:"woocommerce-fulfillment-modal-actions",children:[(0,z.jsx)(a.Button,{variant:"link",onClick:p,__next40pxDefaultSize:!0,children:(0,c.__)("Cancel","woocommerce")}),(0,z.jsx)(a.Button,{variant:"primary",onClick:()=>{x(),p()},isBusy:s,__next40pxDefaultSize:!0,children:(0,c.__)("Remove fulfillment","woocommerce")})]})]})]})}function Ie({setError:e}){const{setIsEditing:t}=te(),{order:l,fulfillment:o,notifyCustomer:r}=G(),{updateFulfillment:i}=(0,m.useDispatch)(E),[s,d]=(0,n.useState)(!1);return(0,z.jsx)(a.Button,{variant:"primary",onClick:async()=>{if(!o||!l)return void e((0,c.__)("An unexpected error has occurred. Please refresh the page and try again.","woocommerce"));if(0===M(o).length)return void e((0,c.__)("Select items to be fulfilled.","woocommerce"));e(null),d(!0),await i(l.id,o,r);const n=(0,m.select)(E).getError(l.id);n?e(n):(F(l.id),t(!1)),d(!1)},disabled:s,isBusy:s,__next40pxDefaultSize:!0,children:(0,c.__)("Update","woocommerce")})}function Le({fulfillment:e}){const t=(window.wcFulfillmentSettings?.fulfillment_statuses||{})[e.status]||{label:e.status,is_fulfilled:!1,background_color:"",text_color:""};return(0,z.jsx)("div",{className:`woocommerce-fulfillment-status-badge woocommerce-fulfillment-status-badge__${e.status}`,style:{backgroundColor:t.background_color,color:t.text_color},children:t.label})}function He({metaList:e}){return(0,z.jsx)("ul",{className:"woocommerce-fulfillment-meta-list",children:e.map(((e,t)=>(0,z.jsxs)("li",{className:"woocommerce-fulfillment-meta-list__item",children:[(0,z.jsx)("div",{className:"woocommerce-fulfillment-meta-list__item-label",children:e.label}),(0,z.jsx)("div",{className:"woocommerce-fulfillment-meta-list__item-value",children:(0,W.isEmpty)(String(e.value))?(0,c.__)("(empty)","woocommerce"):String(e.value)})]},t)))})}function Oe(){const{shipmentProvider:e,providerName:t,trackingNumber:l,trackingUrl:o,selectedOption:n}=$(),r=n!==R&&""!==l.trim(),i="other"!==e?u.find((t=>t.value===e)):null;return(0,z.jsx)(je,{isCollapsable:r,initialState:"collapsed",header:r?(0,z.jsxs)(z.Fragment,{children:[i?(0,z.jsx)("img",{src:i.icon||"",alt:i.label||""}):(0,z.jsx)(ue,{}),(0,z.jsxs)("h3",{children:[l," ",(0,z.jsx)(pe,{copyText:l})]})]}):(0,z.jsxs)(z.Fragment,{children:[(0,z.jsx)(ue,{}),(0,z.jsx)("h3",{children:(0,c.__)("No shipment information","woocommerce")})]}),children:r&&(0,z.jsx)(He,{metaList:[{label:(0,c.__)("Tracking number","woocommerce"),value:l},{label:(0,c.__)("Provider name","woocommerce"),value:(s=e,a=t,"other"===s?a:I(s)||a||(0,c.__)("Unknown","woocommerce"))},{label:(0,c.__)("Tracking URL","woocommerce"),value:o}]})});var s,a}function Te({fulfillment:e}){const t=e.meta_data.filter((e=>!1===e.key.startsWith("_")));return(0,z.jsxs)(je,{isCollapsable:!0,header:(0,z.jsxs)(z.Fragment,{children:[(0,z.jsx)(fe,{}),(0,z.jsx)("h3",{children:(0,c.__)("Fulfillment details","woocommerce")})]}),children:[0===t.length&&(0,z.jsx)("p",{children:(0,c.__)("No metadata available.","woocommerce")}),t.length>0&&(0,z.jsx)(He,{metaList:t.map((e=>({label:e.key,value:e.value})))})]})}function Ve({message:e}){return(0,z.jsxs)("div",{className:"woocommerce-fulfillment-lock-label",children:[(0,z.jsx)("span",{className:"woocommerce-fulfillment-lock-label__icon",children:(0,z.jsx)("svg",{width:"10",height:"15",viewBox:"0 0 10 15",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",children:(0,z.jsx)("path",{d:"M9.16667 6.33341H8.16667V3.83341C8.16667 2.08341 6.75 0.666748 5 0.666748C3.25 0.666748 1.83333 2.08341 1.83333 3.83341V6.33341H0.833333C0.333333 6.33341 0 6.66675 0 7.16675V13.8334C0 14.3334 0.333333 14.6667 0.833333 14.6667H9.16667C9.66667 14.6667 10 14.3334 10 13.8334V7.16675C10 6.66675 9.66667 6.33341 9.16667 6.33341ZM3.16667 3.83341C3.16667 2.83341 4 2.00008 5 2.00008C6 2.00008 6.83333 2.83341 6.83333 3.83341V6.33341H3.16667V3.83341ZM8.75 13.4167H1.25V7.58341H8.75V13.4167Z"})})}),(0,z.jsx)("span",{className:"woocommerce-fulfillment-lock-label__text",children:e||(0,c.__)("This item is locked and cannot be edited.","woocommerce")})]})}function Pe({index:e,expanded:t,onExpand:l,onCollapse:o,fulfillment:r,disabled:i=!1}){const{order:s,fulfillments:m,refunds:d}=te(),{isEditing:u,setIsEditing:f}=te(),[h,p]=(0,n.useState)(null),x=s?Y(s,r):[],_=s?X(m,s,d):[],w=Q([...x],[..._]),g=function(e){return{isLocked:S(e,"_is_locked",!1),reason:S(e,"_lock_message","")}}(r);(0,n.useEffect)((()=>{p(null)}),[s?.id]);const v=()=>{u||0===_.length&&1===m.length||(t?o():l())};return(0,z.jsxs)("div",{className:["woocommerce-fulfillment-stored-fulfillment-list-item",i?"woocommerce-fulfillment-stored-fulfillment-list-item__disabled":""].join(" "),children:[(0,z.jsxs)("div",{className:["woocommerce-fulfillment-stored-fulfillment-list-item-header",t?"is-open":""].join(" "),onClick:v,onKeyUp:e=>{"Enter"===e.key&&v()},role:"button",tabIndex:-1,children:[(0,z.jsx)("h3",{children:(0,c.sprintf)(u?(0,c.__)("Editing fulfillment #%s","woocommerce"):(0,c.__)("Fulfillment #%s","woocommerce"),e+1)}),(0,z.jsx)(Le,{fulfillment:r}),(_.length>0||m.length>1)&&(0,z.jsx)(a.Button,{__next40pxDefaultSize:!0,size:"small",children:(0,z.jsx)(a.Icon,{icon:t?"arrow-up-alt2":"arrow-down-alt2",size:16,color:u?"#dddddd":void 0})})]}),t&&(0,z.jsxs)("div",{className:"woocommerce-fulfillment-stored-fulfillment-list-item-content",children:[h&&(0,z.jsx)(me,{error:h}),(0,z.jsx)(q,{fulfillment:r,children:(0,z.jsxs)(K,{order:s,fulfillment:r,items:u?w:x,children:[(0,z.jsx)(ae,{editMode:u}),u&&(0,z.jsx)(Ce,{}),!u&&(0,z.jsxs)(z.Fragment,{children:[(0,z.jsx)(Oe,{}),(0,z.jsx)(Te,{fulfillment:r})]}),(r.is_fulfilled&&u||!r.is_fulfilled&&!u)&&(0,z.jsx)(ye,{type:"update"}),g.isLocked?(0,z.jsx)("div",{className:"woocommerce-fulfillment-item-lock-container",children:(0,z.jsx)(Ve,{message:g.reason})}):(0,z.jsx)("div",{className:"woocommerce-fulfillment-item-actions",children:u?(0,z.jsxs)(z.Fragment,{children:[(0,z.jsx)(Me,{onClick:()=>{p(null),f(!1)}}),(0,z.jsx)(Fe,{setError:e=>p(e)}),(0,z.jsx)(Ie,{setError:e=>p(e)})]}):(0,z.jsxs)(z.Fragment,{children:[(0,z.jsx)(Se,{onClick:()=>{f(!0)}}),!r.is_fulfilled&&(0,z.jsx)(ne,{setError:p})]})})]})})]})]})}function Be(){const{fulfillments:e,openSection:t,setOpenSection:l,isEditing:o}=te();return e.length>0&&(0,z.jsx)("div",{className:"woocommerce-fulfillment-stored-fulfillments-list",children:e.map(((e,n)=>(0,z.jsx)(Pe,{index:n,disabled:o&&t!=="fulfillment-"+e.id,expanded:t==="fulfillment-"+e.id,onExpand:()=>l("fulfillment-"+e.id),onCollapse:()=>l(""),fulfillment:e},e.id)))})}const Re=window.moment;var De=l.n(Re);function ze({onClose:e}){const{order:t,setIsEditing:l,setOpenSection:o}=te();return t?t&&(0,z.jsxs)("div",{className:"woocommerce-fulfillment-drawer__header",children:[(0,z.jsxs)("div",{className:"woocommerce-fulfillment-drawer__header__title",children:[(0,z.jsxs)("h2",{children:["#",t.id," ",t.billing.first_name+" "+t.billing.last_name]}),(0,z.jsx)("button",{className:"woocommerce-fulfillment-drawer__header__close-button",onClick:()=>{l(!1),o("order"),e()},children:"×"})]}),(0,z.jsx)("p",{children:De()(t.date_created).format("MMMM D, YYYY, H:mma")})]}):null}function Ue({children:e}){return(0,z.jsx)("div",{className:"woocommerce-fulfillment-drawer__body",children:e})}const Ze=({isOpen:e,hasBackdrop:t=!1,onClose:l,orderId:o})=>(0,z.jsxs)(z.Fragment,{children:[t&&(0,z.jsx)("div",{className:"woocommerce-fulfillment-drawer__backdrop",onClick:l,role:"presentation",style:{display:e?"block":"none"}}),(0,z.jsx)("div",{className:"woocommerce-fulfillment-drawer",children:(0,z.jsx)("div",{className:["woocommerce-fulfillment-drawer__panel",e?"is-open":"is-closed"].join(" "),children:(0,z.jsx)(Ee,{children:(0,z.jsxs)(le,{orderId:o,children:[(0,z.jsx)(ze,{onClose:l}),(0,z.jsxs)(Ue,{children:[(0,z.jsx)(ke,{}),(0,z.jsx)(Be,{})]})]})})})})]});function $e(){const[e,t]=(0,n.useState)(!1),[l,o]=(0,n.useState)(null),r=(0,n.useCallback)((()=>{document.querySelectorAll(".type-shop_order").forEach((e=>{e.classList.remove("is-selected")}))}),[]),i=(0,n.useCallback)((e=>{const t=e.closest("tr");r(),t?.classList.add("is-selected")}),[r]);(0,n.useLayoutEffect)((()=>{const e=e=>{const l=e.target;if(l.closest(".fulfillments-trigger")){const n=l.closest(".fulfillments-trigger"),r=parseInt(n.dataset.orderId||"",10);r&&(e.preventDefault(),e.stopPropagation(),i(n),o(r),t(!0))}};return document.body.addEventListener("click",e),()=>{document.body.removeEventListener("click",e)}}),[i]);const c=(0,s.getQuery)().hasOwnProperty("id");return(0,z.jsx)(Ze,{hasBackdrop:c,isOpen:e,orderId:l,onClose:()=>{r(),t(!1),setTimeout((()=>{o(null)}),300)}})}const qe=$e,Ae=document.querySelector("#wc_order_fulfillments_panel_container");(0,i.createRoot)(Ae).render((0,z.jsx)($e,{})),(window.wc=window.wc||{}).fulfillments=o})();