/*! For license information please see email-editor-integration.js.LICENSE.txt */
(()=>{"use strict";var e={47893:(e,t,o)=>{o.d(t,{initializeEditor:()=>an});var n={};o.r(n),o.d(n,{requestSendingNewsletterPreview:()=>no,setIsFetchingPersonalizationTags:()=>ro,setPersonalizationTagsList:()=>so,setTemplateToPost:()=>oo,togglePreviewModal:()=>eo,updateSendPreviewEmail:()=>to});var r={};o.r(r),o.d(r,{canUserEditGlobalEmailStyles:()=>So,canUserEditTemplates:()=>fo,getBlockPatternsForEmailTemplate:()=>xo,getCurrentTemplate:()=>jo,getCurrentTemplateContent:()=>ko,getEditedEmailContent:()=>yo,getEditedPostTemplate:()=>vo,getEmailPostId:()=>To,getEmailPostType:()=>Po,getEmailTemplates:()=>Eo,getGlobalEmailStylesPost:()=>Co,getGlobalStylesPostId:()=>Lo,getInitialEditorSettings:()=>No,getPaletteColors:()=>Bo,getPersonalizationTagsList:()=>Io,getPersonalizationTagsState:()=>Fo,getPreviewState:()=>Mo,getSentEmailEditorPosts:()=>wo,getStyles:()=>zo,getTheme:()=>Ro,getUrls:()=>Ao,hasEdits:()=>_o,hasEmptyContent:()=>go,isEmailSent:()=>ho,isFeatureActive:()=>uo});var s={};o.r(s),o.d(s,{getPersonalizationTagsList:()=>Oo});var i=o(39793),a=o(47143),l=o(86087),c=o(52619);window.wp.formatLibrary;var d=o(74997);const m=window.wp.blockLibrary,p=window.wp.blockEditor,u=window.wp.compose,_=(0,u.createHigherOrderComponent)((e=>function(t){return"core/columns"!==t.name?(0,i.jsx)(e,{...t}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(e,{...t}),(0,i.jsx)(p.InspectorControls,{children:(0,i.jsx)("style",{children:"\n      .components-panel__body .components-toggle-control .components-form-toggle { opacity: 0.3; }\n      .components-panel__body .components-toggle-control .components-form-toggle__input { pointer-events: none; }\n      .components-panel__body .components-toggle-control label { pointer-events: none; }\n    "})})]})}),"columnsEditCallback");var g=o(27723);function h({layoutClassNames:e}){const t=(0,p.useBlockProps)({className:e});return(0,i.jsxs)("div",{...t,children:[(0,i.jsx)("p",{children:(0,g.__)("This is the Content block.","woocommerce")}),(0,i.jsx)("p",{children:(0,g.__)("It will display all the blocks in the email content, which might be only simple text paragraphs. You can enrich your message with images, incorporate data through tables, explore different layout designs with columns, or use any other block type.","woocommerce")})]})}const y=(0,u.createHigherOrderComponent)((e=>function(t){return"core/image"!==t.name?(0,i.jsx)(e,{...t}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(e,{...t}),(0,i.jsx)(p.InspectorControls,{children:(0,i.jsx)("style",{children:"\n        .components-tools-panel .components-toggle-control { display: none; }\n      "})})]})}),"imageEditCallback"),w=window.wp.richText;var x=o(56427);const f=(e,t)=>{const o=e.current.ownerDocument.defaultView.getSelection();if(!o.rangeCount)return{start:0,end:0};const n=o.getRangeAt(0);if(null===o.anchorNode.previousSibling)return{start:o.anchorOffset,end:o.anchorOffset+n.toString().length};const r=(0,w.create)({html:t});let s=o.anchorNode.previousSibling;s=function(e){let t=e;for(;t&&t?.children?.length>0;)t=t.children[0];return t}(s);const i=function(e,t){let o=null;for(const[n,r]of t.entries())if(r)for(const t of r)t?.attributes&&e.tagName.toLowerCase()===t.tagName?.toLowerCase()&&e.getAttribute("data-link-href")===t?.attributes["data-link-href"]&&(o=n);return o}(s,r.formats);if(null!==i)return{start:i+o.anchorOffset+1,end:i+o.anchorOffset+n.toString().length};const a=function(e,t){for(const[o,n]of t.entries()){if(!n)continue;const{attributes:t}=n;if(e.getAttribute("data-rich-text-comment")===t["data-rich-text-comment"])return o}return null}(s,r.replacements);return null!==a?{start:a+o.anchorOffset+1,end:a+o.anchorOffset+n.toString().length}:{start:r.text.length,end:r.text.length+n.toString().length}},b=(e,t)=>(t.forEach((t=>{if(!e.includes(t.token.slice(0,t.token.length-1)))return;const o=t.token.substring(1,t.token.length-1).replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),n=new RegExp(`(?<!\x3c!--)(?<!["'])\\[(${o}(\\s[^\\]]*)?)\\](?!--\x3e)`,"g");e=e.replace(n,(e=>`\x3c!--${e}--\x3e`))})),e),v=({groupedTags:e,activeCategory:t,onCategorySelect:o})=>{const n=e=>e===t?"woocommerce-personalization-tags-modal-menu-item-active":"";return(0,i.jsxs)(x.MenuGroup,{className:"woocommerce-personalization-tags-modal-menu",children:[(0,i.jsx)(x.MenuItem,{onClick:()=>o(null),className:n(null),children:(0,g.__)("All","woocommerce")}),(0,i.jsx)("div",{className:"woocommerce-personalization-tags-modal-menu-separator","aria-hidden":"true",role:"presentation","data-testid":"woocommerce-personalization-tags-modal-menu-separator"}),Object.keys(e).map(((e,t,r)=>(0,i.jsxs)(l.Fragment,{children:[(0,i.jsx)(x.MenuItem,{onClick:()=>o(e),className:n(e),children:e}),t<r.length-1&&(0,i.jsx)("div",{className:"woocommerce-personalization-tags-modal-menu-separator","aria-hidden":"true",role:"presentation","data-testid":"woocommerce-personalization-tags-modal-menu-separator"})]},e)))]})},j=({groupedTags:e,activeCategory:t,onInsert:o,canInsertLink:n,closeCallback:r,openLinkModal:s})=>{const{updateBlockAttributes:l}=(0,a.useDispatch)(p.store),c=(0,a.useSelect)((e=>e(p.store).getSelectedBlockClientId())),d=(0,a.useSelect)((e=>e(p.store).getBlock(c))),m=["core/button"].includes(d?.name),u=null===t?Object.entries(e):[[t,e[t]||[]]];return(0,i.jsx)(i.Fragment,{children:u.map((([e,t])=>(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"woocommerce-personalization-tags-modal-category",children:e}),(0,i.jsx)("div",{className:"woocommerce-personalization-tags-modal-category-group",children:t.map((t=>{const a=/\burl\b/.test(t.token);return(0,i.jsxs)("div",{className:"woocommerce-personalization-tags-modal-category-group-item",children:[(0,i.jsxs)("div",{className:"woocommerce-personalization-tags-modal-item-text",children:[(0,i.jsx)("strong",{children:t.name}),t.valueToInsert]}),(0,i.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"flex-end"},children:[(0,i.jsx)(x.Button,{variant:"link",onClick:()=>{o&&o(t.valueToInsert,!1)},children:(0,g.__)("Insert","woocommerce")}),m&&a&&(0,i.jsx)(x.Button,{variant:"link",onClick:()=>{l(c,{url:t.valueToInsert}),r()},children:(0,g.__)("Set as URL","woocommerce")}),e===(0,g.__)("Link","woocommerce")&&n&&(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(x.Button,{variant:"link",onClick:()=>{r(),s(t)},children:(0,g.__)("Insert as link","woocommerce")})})]})]},t.token)}))})]},e)))})},k=({onInsert:e,isOpened:t,closeCallback:o,tag:n})=>{const[r,s]=(0,l.useState)((0,g.__)("Link","woocommerce"));return t?(0,i.jsxs)(x.Modal,{size:"small",title:(0,g.__)("Insert Link","woocommerce"),onRequestClose:o,className:"woocommerce-personalization-tags-modal",children:[(0,i.jsx)(x.TextControl,{label:(0,g.__)("Link Text","woocommerce"),value:r,onChange:s}),(0,i.jsx)(x.Button,{isPrimary:!0,onClick:()=>{e&&e(n.token,r)},children:(0,g.__)("Insert","woocommerce")})]}):null},S=window.lodash,C=()=>(0,c.applyFilters)("woocommerce_email_editor_events_tracking_enabled",!1),E="email_editor_events",T=new EventTarget,P=(e,t={})=>{if(!C())return;const o={name:`${E}_${e}`,..."object"!=typeof t?{data:t}:t};T.dispatchEvent(new CustomEvent(E,{detail:o}))},N=function(){const e={};return(t,o={})=>{if(!C())return;const n=`${t}_${JSON.stringify(o).length}`;e[n]||(P(t,o),e[n]=!0)}}(),B=(0,S.debounce)(P,700),M="email-editor/editor",F=({onInsert:e,isOpened:t,closeCallback:o,canInsertLink:n=!1,openedBy:r=""})=>{const[s,c]=(0,l.useState)(null),[d,m]=(0,l.useState)(""),[p,u]=(0,l.useState)(null),[_,h]=(0,l.useState)(!1),y=(0,a.useSelect)((e=>e(M).getPersonalizationTagsList()),[]);if(_)return(0,i.jsx)(k,{onInsert:(t,o)=>{e(t,o),h(!1)},isOpened:_,closeCallback:()=>h(!1),tag:p});if(!t)return null;N("personalization_tags_modal_opened",{openedBy:r});const w=y.reduce(((e,t)=>{const{category:o,name:n,token:r}=t;return(!d||n.toLowerCase().includes(d.toLowerCase())||r.toLowerCase().includes(d.toLowerCase()))&&(e[o]||(e[o]=[]),e[o].push(t)),e}),{});return(0,i.jsxs)(x.Modal,{size:"medium",title:(0,g.__)("Personalization Tags","woocommerce"),onRequestClose:()=>{o(),P("personalization_tags_modal_closed",{openedBy:r})},className:"woocommerce-personalization-tags-modal",children:[(0,i.jsxs)("p",{children:[(0,g.__)("Insert personalization tags to dynamically fill in information and personalize your emails.","woocommerce")," ",(0,i.jsx)(x.ExternalLink,{href:"https://kb.mailpoet.com/article/435-a-guide-to-personalisation-tags-for-tailored-newsletters#list",onClick:()=>P("personalization_tags_modal_learn_more_link_clicked",{openedBy:r}),children:(0,g.__)("Learn more","woocommerce")})]}),(0,i.jsx)(x.SearchControl,{onChange:e=>{m(e),N("personalization_tags_modal_search_control_input_updated",{openedBy:r})},value:d}),(0,i.jsx)(v,{groupedTags:w,activeCategory:s,onCategorySelect:e=>{c(e),P("personalization_tags_modal_category_menu_clicked",{category:e,openedBy:r})}}),(0,i.jsx)(j,{groupedTags:w,activeCategory:s,onInsert:t=>{e(t),P("personalization_tags_modal_tag_insert_button_clicked",{insertedTag:t,activeCategory:s,openedBy:r})},closeCallback:o,canInsertLink:n,openLinkModal:e=>{u(e),h(!0)}})]})},I=({contentRef:e,onUpdate:t})=>{const[o,n]=(0,l.useState)(!1),[r,s]=(0,l.useState)(null),[a,c]=(0,l.useState)(""),[d,m]=(0,l.useState)("");return(0,l.useEffect)((()=>{if(!e||!e.current)return;const t=e.current,o=e=>{const t=e.target.closest("span[data-rich-text-comment]");if(t){const e=t.innerText.replace(/^\[|\]$/g,"");m(e),c(e),s(t),n(!0)}};return t.addEventListener("click",o),()=>{t.removeEventListener("click",o)}}),[e]),(0,i.jsx)(i.Fragment,{children:o&&r&&(0,i.jsx)(x.Popover,{position:"bottom right",onClose:()=>n(!1),anchor:r,className:"woocommerce-personalization-tag-popover",children:(0,i.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content",children:[(0,i.jsx)(x.TextControl,{label:(0,g.__)("Personalization Tag","woocommerce"),value:a,onChange:e=>c(e),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),(0,i.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content-buttons",children:[(0,i.jsx)(x.Button,{isTertiary:!0,onClick:()=>{n(!1)},children:(0,g.__)("Cancel","woocommerce")}),(0,i.jsx)(x.Button,{isPrimary:!0,onClick:()=>{t(d,a),n(!1)},children:(0,g.__)("Update","woocommerce")})]})]})})})},z=({contentRef:e,onUpdate:t})=>{const[o,n]=(0,l.useState)(!1),[r,s]=(0,l.useState)(null),[c,d]=(0,l.useState)(""),[m,p]=(0,l.useState)(""),u=(0,a.useSelect)((e=>e(M).getPersonalizationTagsList()),[]);return(0,l.useEffect)((()=>{if(!e||!e.current)return;const t=e.current,o=e=>{const t=e.target.closest("a[data-link-href]");t&&(s(t),p(t.getAttribute("data-link-href")||""),d(t.textContent||""),n(!0))};return t.addEventListener("click",o),()=>{t.removeEventListener("click",o)}}),[e]),(0,i.jsx)(i.Fragment,{children:o&&r&&(0,i.jsx)(x.Popover,{position:"bottom left",onClose:()=>n(!1),anchor:r,className:"woocommerce-personalization-tag-popover",children:(0,i.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content",children:[(0,i.jsx)(x.TextControl,{label:(0,g.__)("Link Text","woocommerce"),value:c,onChange:e=>d(e),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,autoComplete:"off"}),(0,i.jsx)(x.SelectControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,label:(0,g.__)("Link tag","woocommerce"),value:m,onChange:e=>{p(e)},options:u.filter((e=>e.category===(0,g.__)("Link","woocommerce"))).map((e=>({label:e.name,value:e.token})))}),(0,i.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content-buttons",children:[(0,i.jsx)(x.Button,{isTertiary:!0,onClick:()=>{n(!1)},children:(0,g.__)("Cancel","woocommerce")}),(0,i.jsx)(x.Button,{isPrimary:!0,onClick:()=>{n(!1),t(r,m,c)},children:(0,g.__)("Update link","woocommerce")})]})]})})})};function R({contentRef:e}){const[t,o]=(0,l.useState)(!1),n=(0,a.useSelect)((e=>e("core/block-editor").getSelectedBlockClientId())),{updateBlockAttributes:r}=(0,a.useDispatch)("core/block-editor"),s=(0,a.useSelect)((e=>e("core/block-editor").getBlockAttributes(n))),c="text"in s?"text":"content",d=s?.[c]?.originalHTML||s?.[c]||"",m=(0,l.useCallback)(((t,o)=>{let{start:s,end:i}=f(e,d),a="";if(o){let e=(0,w.create)({html:d});e=(0,w.insert)(e,o,s,i),i=s+o.length,e=(0,w.applyFormat)(e,{type:"woocommerce-email-editor/link-shortcode",attributes:{"data-link-href":t,contenteditable:"false",style:"text-decoration: underline;"}},s,i),a=(0,w.toHTMLString)({value:e})}else{let e=(0,w.create)({html:d});e=(0,w.insert)(e,(0,w.create)({html:`\x3c!--${t}--\x3e&nbsp;`}),s,i),a=(0,w.toHTMLString)({value:e})}r(n,{[c]:a})}),[d,c,e,n,r]);return(0,i.jsx)(p.BlockControls,{children:(0,i.jsxs)(x.ToolbarGroup,{children:[(0,i.jsx)(x.ToolbarButton,{icon:"shortcode",title:(0,g.__)("Personalization Tags","woocommerce"),onClick:()=>{o(!0),P("block_controls_personalization_tags_button_clicked")}}),(0,i.jsx)(I,{contentRef:e,onUpdate:(e,t)=>{const o=d.replace(`\x3c!--[${e}]--\x3e`,`\x3c!--[${t}]--\x3e`);r(n,{[c]:o})}}),(0,i.jsx)(z,{contentRef:e,onUpdate:(e,t,o)=>{const s=e.getAttribute("data-link-href").replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),i=new RegExp(`<a([^>]*?)data-link-href="${s}"([^>]*?)>${e.textContent}</a>`,"gi"),a=d.replace(i,((e,n,r)=>`<a${n}data-link-href="${t}"${r}>${o}</a>`));r(n,{content:a})}}),(0,i.jsx)(F,{isOpened:t,onInsert:(e,t)=>{m(e,t),o(!1)},closeCallback:()=>o(!1),canInsertLink:!0,openedBy:"block-controls"})]})})}const L=(0,u.createHigherOrderComponent)((e=>t=>{const{attributes:o,setAttributes:n,name:r}=t,{content:s}=o,c=(0,a.useSelect)((e=>e(M).getPersonalizationTagsList()),[]),d=(0,l.useCallback)((()=>s?b(s,c):""),[s,c]),m=(0,l.useCallback)((e=>{if(void 0!==e.content){const t=b(e.content,c);n({...e,content:t})}else n(e)}),[c,n]);return"core/paragraph"===r||"core/heading"===r||"core/list-item"===r?(0,i.jsx)(e,{...t,attributes:{...o,content:d()},setAttributes:m}):(0,i.jsx)(e,{...t})}),"personalizationTagsLiveContentUpdate"),A=e=>t=>{const{setAttributes:o}=t,n=(0,l.useCallback)((e=>{e?.url&&e.url?.startsWith("http://[")&&(e.url=e.url.replace("http://[","[")),o(e)}),[o]);return(0,i.jsx)(e,{...t,setAttributes:n})},O=["behance","bluesky","chain","discord","facebook","feed","github","gravatar","instagram","linkedin","mail","mastodon","medium","patreon","pinterest","reddit","spotify","telegram","threads","tiktok","tumblr","twitch","twitter","vimeo","wordpress","whatsapp","x","youtube"],H=e=>t=>{if("core/social-links"!==t.name)return(0,i.jsx)(e,{...t});const o=`\n\t\t.block-editor-tools-panel-color-gradient-settings__item:has([title="${(0,g.__)("Icon color")}"]) {\n\t\t\tdisplay: none !important;\n\t\t}\n\t\t.block-editor-tools-panel-color-gradient-settings__item:nth-child(2 of .block-editor-tools-panel-color-gradient-settings__item){\n\t\t\tborder-top:1px solid #ddd;\n\t\t\tborder-top-left-radius:2px;\n\t\t\tborder-top-right-radius:2px;\n\t\t}\n\t\t`;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(e,{...t}),(0,i.jsx)(p.InspectorControls,{group:"color",children:(0,i.jsx)("style",{children:o})})]})};const V=window.wp.privateApis;var D=o(43656);const{unlock:W}=(0,V.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/edit-site"),{ColorPanel:G}=W(p.privateApis),{useGlobalStylesOutputWithConfig:$}=W(p.privateApis),{Editor:U,FullscreenMode:Z,ViewMoreMenuGroup:q,BackButton:J}=W(D.privateApis),{registerEntityAction:Y,unregisterEntityAction:K}=W((0,a.dispatch)(D.store));var X=o(3582);const Q=window.wp.notices;var ee=o(5573);const te=(0,i.jsx)(ee.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ee.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.5A2.25 2.25 0 0 0 9.878 7h4.244A2.251 2.251 0 0 0 12 5.5ZM12 4a3.751 3.751 0 0 0-3.675 3H5v1.5h1.27l.818 8.997a2.75 2.75 0 0 0 2.739 2.501h4.347a2.75 2.75 0 0 0 2.738-2.5L17.73 8.5H19V7h-3.325A3.751 3.751 0 0 0 12 4Zm4.224 4.5H7.776l.806 8.861a1.25 1.25 0 0 0 1.245 1.137h4.347a1.25 1.25 0 0 0 1.245-1.137l.805-8.861Z"})}),oe=window.wp.htmlEntities;function ne(e){return"string"==typeof e.title?(0,oe.decodeEntities)(e.title):e.title&&"rendered"in e.title?(0,oe.decodeEntities)(e.title.rendered):e.title&&"raw"in e.title?(0,oe.decodeEntities)(e.title.raw):""}function re(e,t){return t?e.length>1?(0,g.sprintf)((0,g._n)("Are you sure you want to permanently delete %d item?","Are you sure you want to permanently delete %d items?",e.length,"woocommerce"),e.length):(0,g.sprintf)((0,g.__)('Are you sure you want to permanently delete "%s"?',"woocommerce"),(0,oe.decodeEntities)(ne(e[0]))):e.length>1?(0,g.sprintf)((0,g._n)("Are you sure you want to move %d item to the trash ?","Are you sure you want to move %d items to the trash ?",e.length,"woocommerce"),e.length):(0,g.sprintf)((0,g.__)('Are you sure you want to move "%s" to the trash?',"woocommerce"),ne(e[0]))}const se=e=>{K("postType",e,"move-to-trash"),Y("postType",e,(()=>{const e=(0,c.applyFilters)("woocommerce_email_editor_trash_modal_should_permanently_delete",!1);return{id:"trash-email-post",label:e?(0,g.__)("Permanently delete","woocommerce"):(0,g.__)("Move to trash","woocommerce"),supportsBulk:!0,icon:te,isEligible(e){if("wp_template"===e.type||"wp_template_part"===e.type||"wp_block"===e.type)return!1;const{permissions:t}=e;return t?.delete},hideModalHeader:!0,modalFocusOnMount:"firstContentElement",RenderModal:({items:t,closeModal:o,onActionPerformed:n})=>{const[r,s]=(0,l.useState)(!1),{createSuccessNotice:c,createErrorNotice:d}=(0,a.useDispatch)(Q.store),{deleteEntityRecord:m}=(0,a.useDispatch)(X.store),{urls:p}=(0,a.useSelect)((e=>({urls:e(M).getUrls()})),[]);return(0,i.jsxs)(x.__experimentalVStack,{spacing:"5",children:[(0,i.jsx)(x.__experimentalText,{children:re(t,e)}),(0,i.jsxs)(x.__experimentalHStack,{justify:"right",children:[(0,i.jsx)(x.Button,{variant:"tertiary",onClick:()=>{o?.(),P("trash_modal_cancel_button_clicked")},disabled:r,__next40pxDefaultSize:!0,children:(0,g.__)("Cancel","woocommerce")}),(0,i.jsx)(x.Button,{variant:"primary",onClick:async()=>{P("trash_modal_move_to_trash_button_clicked"),s(!0);const r=await Promise.allSettled(t.map((t=>m("postType",t.type,t.id,{force:e},{throwOnError:!0}))));if(r.every((({status:e})=>"fulfilled"===e))){let o;o=1===r.length?e?(0,g.sprintf)((0,g.__)('"%s" permanently deleted.',"woocommerce"),ne(t[0])):(0,g.sprintf)((0,g.__)('"%s" moved to the trash.',"woocommerce"),ne(t[0])):e?(0,g.__)("The items were permanently deleted.","woocommerce"):(0,g.sprintf)((0,g._n)("%s item moved to the trash.","%s items moved to the trash.",t.length,"woocommerce"),t.length),c(o,{type:"snackbar",id:"trash-email-post-action"}),n?.(t),p?.listings&&(window.location.href=p.listings)}else{let e;if(1===r.length){const t=r[0];e=t.reason?.message?t.reason.message:(0,g.__)("An error occurred while performing the action.","woocommerce")}else{const t=new Set,o=r.filter((({status:e})=>"rejected"===e));for(const e of o){const o=e;o.reason?.message&&t.add(o.reason.message)}e=0===t.size?(0,g.__)("An error occurred while performing the action.","woocommerce"):1===t.size?(0,g.sprintf)((0,g.__)("An error occurred while performing the action: %s","woocommerce"),[...t][0]):(0,g.sprintf)((0,g.__)("Some errors occurred while performing the action: %s","woocommerce"),[...t].join(","))}P("trash_modal_move_to_trash_error",{errorMessage:e}),d(e,{type:"snackbar"})}s(!1),o?.()},isBusy:r,disabled:r,__next40pxDefaultSize:!0,children:e?(0,g.__)("Delete permanently","woocommerce"):(0,g.__)("Move to trash","woocommerce")})]})]})}}})())};function ie(){(0,c.addFilter)("editor.BlockEdit","woocommerce-email-editor/filter-set-url-attribute",A),(0,c.addFilter)("editor.BlockEdit","woocommerce-email-editor/deactivate-stack-on-mobile",_),(0,c.addFilter)("editor.BlockEdit","woocommerce-email-editor/hide-expand-on-click",y),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/deactivate-image-filter",((e,t)=>"core/image"===t?{...e,supports:{...e.supports,filter:{duetone:!1}}}:e)),(0,w.unregisterFormatType)("core/image"),(0,w.unregisterFormatType)("core/code"),(0,w.unregisterFormatType)("core/language"),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/disable-columns-layout",((e,t)=>"core/columns"===t||"core/column"===t?{...e,supports:{...e.supports,layout:!1}}:e)),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/disable-group-variations",((e,t)=>"core/group"===t?{...e,variations:e.variations.filter((e=>"group"===e.name)),supports:{...e.supports,layout:!1}}:e)),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-button",((e,t)=>"core/button"===t?{...e,styles:[]}:e)),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-buttons",((e,t)=>"core/buttons"===t?{...e,supports:{...e.supports,layout:!1,__experimentalEmailFlexLayout:!0}}:e)),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-column",((e,t)=>"core/column"===t?{...e,supports:{...e.supports,background:{backgroundImage:!0}}}:e)),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-columns",((e,t)=>"core/columns"===t?{...e,supports:{...e.supports,background:{backgroundImage:!0}}}:e)),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-post-content",((e,t)=>{return"core/post-content"===t?{...e,edit:(o=e.edit,function(e){const{postId:t,postType:n}=e.context,{__unstableLayoutClassNames:r}=e;return t&&n?(0,i.jsx)(o,{...e}):(0,i.jsx)(h,{layoutClassNames:r})})}:e;var o})),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-quote",((e,t)=>"core/quote"===t?{...e,styles:[],supports:{...e.supports,align:[]}}:e)),(0,w.registerFormatType)("woocommerce-email-editor/shortcode",{name:"woocommerce-email-editor/shortcode",title:(0,g.__)("Personalization Tags","woocommerce"),className:"woocommerce-email-editor-personalization-tags",tagName:"span",attributes:{},edit:R}),(0,w.registerFormatType)("woocommerce-email-editor/link-shortcode",{name:"woocommerce-email-editor/link-shortcode",title:(0,g.__)("Personalization Tags Link","woocommerce"),className:"woocommerce-email-editor-personalization-tags-link",tagName:"a",attributes:{"data-link-href":"data-link-href",contenteditable:"contenteditable",style:"style"},edit:null}),(0,c.addFilter)("editor.BlockEdit","woocommerce-email-editor/with-live-content-update",L),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/block-support",(e=>e.supports?.shadow?{...e,supports:{...e.supports,shadow:!1}}:e)),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/disable-social-link-variations",((e,t)=>"core/social-link"===t?{...e,variations:e.variations.filter((e=>O.includes(e.name))),supports:{...e.supports,layout:!1}}:e)),(0,d.registerBlockVariation)("core/social-links",{name:"social-links-default",title:"Social Icons",attributes:{openInNewTab:!0,showLabels:!1,align:"center",className:"is-style-logos-only"},isDefault:!0,innerBlocks:[{name:"core/social-link",attributes:{service:"wordpress",url:"https://wordpress.org"}},{name:"core/social-link",attributes:{service:"facebook",url:"https://www.facebook.com/WordPress/"}},{name:"core/social-link",attributes:{service:"x",url:"https://x.com/WordPress"}}]}),(0,c.addFilter)("editor.BlockEdit","woocommerce-email-editor/disable-social-links-icon-color",H),(0,c.addAction)("core.registerPostTypeSchema","woocommerce-email-editor/modify-move-to-trash-action",(e=>{se(e)})),(0,c.addAction)("core.registerPostTypeActions","woocommerce-email-editor/modify-move-to-trash-action",(e=>{se(e)})),(0,m.registerCoreBlocks)()}var ae=o(4921);const le=(0,i.jsx)(ee.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ee.Path,{d:"M9 9v6h11V9H9zM4 20h1.5V4H4v16z"})}),ce=(0,i.jsx)(ee.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ee.Path,{d:"M12.5 15v5H11v-5H4V9h7V4h1.5v5h7v6h-7Z"})}),de=(0,i.jsx)(ee.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ee.Path,{d:"M4 15h11V9H4v6zM18.5 4v16H20V4h-1.5z"})}),me="__experimentalEmailFlexLayout";function pe(e){return(0,d.hasBlockSupport)(e,me)}function ue({justificationValue:e,onChange:t,isToolbar:o=!1}){const n=[{value:"left",icon:le,label:(0,g.__)("Justify items left","woocommerce")},{value:"center",icon:ce,label:(0,g.__)("Justify items center","woocommerce")},{value:"right",icon:de,label:(0,g.__)("Justify items right","woocommerce")}];if(o){const o=n.map((e=>e.value));return(0,i.jsx)(p.JustifyContentControl,{value:e,onChange:t,allowedControls:o,popoverProps:{placement:"bottom-start"}})}return(0,i.jsx)(x.__experimentalToggleGroupControl,{__nextHasNoMarginBottom:!0,label:(0,g.__)("Justification","woocommerce"),value:e,onChange:t,className:"block-editor-hooks__flex-layout-justification-controls",children:n.map((({value:e,icon:t,label:o})=>(0,i.jsx)(x.__experimentalToggleGroupControlOptionIcon,{value:e,icon:t,label:o},e)))})}function _e({setAttributes:e,attributes:t,name:o}){if(!(0,d.getBlockSupport)(o,me,{}))return null;const{justifyContent:n="left"}=t.layout||{},r=o=>{e({layout:{...t.layout,justifyContent:o}})},s=()=>{const{justifyContent:o,...n}=t.layout||{};e({layout:n})};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(p.InspectorControls,{children:(0,i.jsx)(x.__experimentalToolsPanel,{label:(0,g.__)("Layout","woocommerce"),resetAll:s,children:(0,i.jsx)(x.__experimentalToolsPanelItem,{isShownByDefault:!0,onDeselect:s,hasValue:()=>t.layout?.justifyContent||!1,label:(0,g.__)("Justification","woocommerce"),children:(0,i.jsx)(x.Flex,{children:(0,i.jsx)(x.FlexItem,{children:(0,i.jsx)(ue,{justificationValue:n,onChange:r})})})})})}),(0,i.jsx)(p.BlockControls,{group:"block",__experimentalShareWithChildBlocks:!0,children:(0,i.jsx)(ue,{justificationValue:n,onChange:r,isToolbar:!0})})]})}function ge(e){return pe(e.name)?{...e,attributes:{...e.attributes,layout:{type:"object"}}}:e}const he=(0,u.createHigherOrderComponent)((e=>t=>[pe(t.name)&&(0,i.jsx)(_e,{...t},"layout"),(0,i.jsx)(e,{...t},"edit")]),"withLayoutControls");function ye({block:e,props:t}){const{attributes:o}=t,{layout:n}=o,r=`is-content-justification-${n?.justifyContent||"left"}`,s=(0,ae.A)(r,"is-layout-email-flex is-layout-flex");return(0,i.jsx)(e,{...t,className:s})}const we=(0,u.createHigherOrderComponent)((e=>function(t){return pe(t.name)?(0,i.jsx)(ye,{block:e,props:t}):(0,i.jsx)(e,{...t})}),"withLayoutStyles"),xe=window.wp.commands;var fe=o(76597),be=o.n(fe);function ve(){const{globalStylePost:e}=(0,a.useSelect)((e=>({globalStylePost:e(M).getGlobalEmailStylesPost()||null})),[]),t=(0,l.useCallback)((t=>{e&&(0,a.dispatch)(X.store).editEntityRecord("postType","wp_global_styles",e.id,{styles:t.styles,settings:t.settings})}),[e]);return{userTheme:{settings:e?.settings,styles:e?.styles},updateUserTheme:t}}function je(e){if("string"!=typeof e)return null;const t=e.match(/^var:preset\|([a-zA-Z0-9-]+)\|([a-zA-Z0-9-]+)$/);return t?`--wp--preset--${t[1]}--${t[2]}`:null}function ke(e){const t=je(e);return t?`var(${t})`:e}function Se(e){const t=je(e);if(!t)return e;const o=document.querySelector(":root");return o&&getComputedStyle(o).getPropertyValue(t).trim()||e}const Ce=[];function Ee(){const{userTheme:e}=ve(),{editorTheme:t,layout:o,deviceType:n,editorSettingsStyles:r}=(0,a.useSelect)((e=>{const{getEditorSettings:t,getDeviceType:o}=e(D.store),n=t();return{editorTheme:e(M).getTheme(),layout:n.__experimentalFeatures?.layout,deviceType:o(),editorSettingsStyles:n.styles}}),[]),s=(0,l.useMemo)((()=>be().all([{},t||{},e||{}])),[t,e]),[i]=$(s);let c="";o&&"Mobile"!==n&&(c=`display:flow-root; width:${o?.contentSize}; margin: 0 auto;box-sizing: border-box;`);const d=s.styles?.spacing?.padding;return d&&(c+=`padding-left:${ke(d.left)};`,c+=`padding-right:${ke(d.right)};`),[(0,l.useMemo)((()=>[...null!=i?i:[],{css:`.is-root-container{ ${c} }`},...null!=r?r:[]]),[i,r,c])||Ce]}const Te=[];function Pe(e,t){return e.map((e=>"core/post-content"===e.name?{...e,name:"core/group",innerBlocks:t}:e.innerBlocks?.length?{...e,innerBlocks:Pe(e.innerBlocks,t)}:e))}const Ne={};function Be(e=""){const{templates:t,patterns:o,emailPosts:n,hasEmailPosts:r}=(0,a.useSelect)((t=>{const o="swap"!==e?t(M).getSentEmailEditorPosts():void 0;return{templates:t(M).getEmailTemplates(),patterns:t(M).getBlockPatternsForEmailTemplate(),emailPosts:o,hasEmailPosts:!(!o||!o?.length)}}),[e]),s=(0,l.useMemo)((()=>{let n=[];const r=e&&(0,d.parse)(e);if(n=r?[{blocks:r}]:o,!n||!t)return Te;const s=[];return t?.filter((e=>"email-general"!==e.slug))?.forEach((e=>{n?.forEach((t=>{let o=(0,d.parse)(e.content?.raw);o=Pe(o,t.blocks),s.push({id:e.id,slug:e.slug,previewContentParsed:o,emailParsed:t.blocks,template:e,category:"basic",type:e.type,displayName:t.title?`${e.title.rendered} - ${t.title}`:e.title.rendered})}))})),s}),[t,o,e]),i=(0,l.useMemo)((()=>n?.map((e=>{const t=(0,c.applyFilters)("woocommerce_email_editor_preferred_template_title","",e),{postTemplateContent:o}=function(e,t=[]){const o=e.template,n={postTemplateContent:null};if(!o)return n;if(Ne[o])return Ne[o];const r=t.find((e=>e.slug===o));if(!r)return n;const s={postTemplateContent:r?.template};return Ne[o]=s,s}(e,s),n=(0,d.parse)(e.content?.raw);let r=n;o?.content?.raw&&(r=Pe((0,d.parse)(o?.content?.raw),n));const i={...e,title:{raw:e.title.raw,rendered:t||e.title.rendered}};return{id:e.id,slug:e.slug,previewContentParsed:r,emailParsed:n,category:"recent",type:e.type,displayName:i.title.rendered,template:i}}))),[n,s]);return[s||Te,i||Te,r]}const Me=(0,l.forwardRef)((function({icon:e,size:t=24,...o},n){return(0,l.cloneElement)(e,{width:t,height:t,...o,ref:n})})),Fe=(0,i.jsx)(ee.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ee.Path,{d:"M19 8h-1V6h-5v2h-2V6H6v2H5c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-8c0-1.1-.9-2-2-2zm.5 10c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5v-8c0-.3.2-.5.5-.5h14c.3 0 .5.2.5.5v8z"})}),Ie=(0,i.jsx)(ee.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ee.Path,{d:"M12 3.2c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8 0-4.8-4-8.8-8.8-8.8zm0 16c-4 0-7.2-3.3-7.2-7.2C4.8 8 8 4.8 12 4.8s7.2 3.3 7.2 7.2c0 4-3.2 7.2-7.2 7.2zM11 17h2v-6h-2v6zm0-8h2V7h-2v2z"})}),ze=(0,window.wp.priorityQueue.createQueue)();function Re({children:e,placeholder:t}){const[o,n]=(0,l.useState)(!1);return(0,l.useEffect)((()=>{const e={};return ze.add(e,(()=>{(0,l.flushSync)((()=>{n(!0)}))})),()=>{ze.cancel(e)}}),[]),o?e:t}function Le(){return(0,i.jsxs)("div",{className:"block-editor-inserter__no-results",children:[(0,i.jsx)(Me,{className:"block-editor-inserter__no-results-icon",icon:Fe}),(0,i.jsx)("p",{children:(0,g.__)("No recent templates.","woocommerce")}),(0,i.jsx)("p",{children:(0,g.__)("Your recent creations will appear here as soon as you begin.","woocommerce")})]})}const Ae=(0,l.memo)((function({templates:e,onTemplateSelection:t,selectedCategory:o}){const{layout:n}=(0,a.useSelect)((e=>{const{getEditorSettings:t}=e(D.store);return{layout:t().__experimentalFeatures.layout}})),[r]=Ee(),s=r.reduce(((e,t)=>{var o;return e+(null!==(o=t.css)&&void 0!==o?o:"")}),"")+`.is-root-container { width: ${n.contentSize}; margin: 0 auto; }`;return"recent"===o&&0===e.length?(0,i.jsx)(Le,{}):(0,i.jsx)("div",{className:"block-editor-block-patterns-list",role:"listbox",children:e.map((e=>(0,i.jsx)("div",{className:"block-editor-block-patterns-list__list-item email-editor-pattern__list-item",children:(0,i.jsx)("div",{className:"block-editor-block-patterns-list__item",role:"button",tabIndex:0,onClick:()=>{t(e)},onKeyPress:o=>{"Enter"!==o.key&&" "!==o.key||t(e)},children:(0,i.jsxs)(Re,{placeholder:(0,i.jsx)("p",{children:(0,g.__)("rendering template","woocommerce")}),children:[(0,i.jsx)(p.BlockPreview,{blocks:e.previewContentParsed,viewportWidth:900,minHeight:300,additionalStyles:[{css:s}]}),(0,i.jsx)(x.__experimentalHStack,{className:"block-editor-patterns__pattern-details",children:(0,i.jsx)("h4",{className:"block-editor-block-patterns-list__item-title",children:e.displayName})})]})})},`${e.slug}_${e.displayName}_${e.id}`)))})}),((e,t)=>e.templates.length===t.templates.length&&e.selectedCategory===t.selectedCategory));function Oe({templates:e,onTemplateSelection:t,selectedCategory:o}){const n=(0,l.useMemo)((()=>e.filter((e=>e.category===o))),[o,e]);return(0,i.jsxs)("div",{className:"block-editor-block-patterns-explorer__list",children:["recent"===o&&(0,i.jsx)("div",{className:"email-editor-recent-templates-info",children:(0,i.jsxs)(x.__experimentalHStack,{spacing:1,expanded:!1,justify:"start",children:[(0,i.jsx)(Me,{icon:Ie}),(0,i.jsx)("p",{children:(0,g.__)("Templates created on the legacy editor will not appear here.","woocommerce")})]})}),(0,i.jsx)(Ae,{templates:n,onTemplateSelection:t,selectedCategory:o})]})}function He({selectedCategory:e,templateCategories:t,onClickCategory:o}){const n="block-editor-block-patterns-explorer__sidebar";return(0,i.jsx)("div",{className:n,children:(0,i.jsx)("div",{className:`${n}__categories-list`,children:t.map((({name:t,label:r})=>(0,i.jsx)(x.Button,{label:r,className:`${n}__categories-list__item`,isPressed:e===t,onClick:()=>{o(t)},children:r},t)))})})}const Ve=[{name:"recent",label:"Recent"},{name:"basic",label:"Basic"}],De=(0,l.memo)((function({hasEmailPosts:e,templates:t,handleTemplateSelection:o,templateSelectMode:n}){const[r,s]=(0,l.useState)(Ve[1].name),a="swap"===n,c=Ve.filter((({name:e})=>"recent"!==e||!a));return(0,l.useEffect)((()=>{setTimeout((()=>{e&&!a&&s(Ve[0].name)}),1e3)}),[e,a]),(0,i.jsxs)("div",{className:"block-editor-block-patterns-explorer",children:[(0,i.jsx)(He,{templateCategories:c,selectedCategory:r,onClickCategory:e=>{P("template_select_modal_category_change",{category:e}),s(e)}}),(0,i.jsx)(Oe,{templates:t,onTemplateSelection:o,selectedCategory:r})]})}));function We({onSelectCallback:e,closeCallback:t=null,previewContent:o="",postType:n}){const r=o?"swap":"new";N("template_select_modal_opened",{templateSelectMode:r});const[s,l,c]=Be(o),d=s?.length>0,m=t=>{const s=t.type===n,i=t.template;P("template_select_modal_template_selected",{templateSlug:t.slug,templateSelectMode:r,templateType:t.type}),o||(0,a.dispatch)(D.store).resetEditorBlocks(t.emailParsed),(0,a.dispatch)(M).setTemplateToPost(s?i.template:t.slug),e()},p=()=>{var e;const t=null!==(e=s[0])&&void 0!==e?e:null;t&&(P("template_select_modal_handle_close_without_template_selected"),m(t))};return(0,i.jsxs)(x.Modal,{title:"new"===r?(0,g.__)("Start with an email preset","woocommerce"):(0,g.__)("Select a template","woocommerce"),onRequestClose:()=>(P("template_select_modal_closed",{templateSelectMode:r}),t?t():p()),isFullScreen:!0,children:[(0,i.jsx)(De,{hasEmailPosts:c,templates:[...s,...l],handleTemplateSelection:m,templateSelectMode:r}),(0,i.jsx)(x.Flex,{className:"email-editor-modal-footer",justify:"flex-end",children:(0,i.jsx)(x.FlexItem,{children:(0,i.jsx)(x.Button,{variant:"tertiary",className:"email-editor-start_from_scratch_button",onClick:()=>(P("template_select_modal_start_from_scratch_clicked"),p()),isBusy:!d,children:(0,g.__)("Start from scratch","woocommerce")})})})]})}function Ge(){const[e,t]=(0,l.useState)(!1),{emailContentIsEmpty:o,emailHasEdits:n,postType:r}=(0,a.useSelect)((e=>({emailContentIsEmpty:e(M).hasEmptyContent(),emailHasEdits:e(M).hasEdits(),postType:e(M).getEmailPostType()})),[]);return!o||n||e?null:(0,i.jsx)(We,{onSelectCallback:()=>t(!0),postType:r})}const $e=(0,i.jsx)(ee.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,i.jsx)(ee.Path,{d:"M12 4c-4.4 0-8 3.6-8 8v.1c0 4.1 3.2 7.5 7.2 7.9h.8c4.4 0 8-3.6 8-8s-3.6-8-8-8zm0 15V5c3.9 0 7 3.1 7 7s-3.1 7-7 7z"})}),Ue=(0,i.jsx)(ee.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ee.Path,{d:"M6.9 7L3 17.8h1.7l1-2.8h4.1l1 2.8h1.7L8.6 7H6.9zm-.7 6.6l1.5-4.3 1.5 4.3h-3zM21.6 17c-.1.1-.2.2-.3.2-.1.1-.2.1-.4.1s-.3-.1-.4-.2c-.1-.1-.1-.3-.1-.6V12c0-.5 0-1-.1-1.4-.1-.4-.3-.7-.5-1-.2-.2-.5-.4-.9-.5-.4 0-.8-.1-1.3-.1s-1 .1-1.4.2c-.4.1-.7.3-1 .4-.2.2-.4.3-.6.5-.1.2-.2.4-.2.7 0 .3.1.5.2.8.2.2.4.3.8.3.3 0 .6-.1.8-.3.2-.2.3-.4.3-.7 0-.3-.1-.5-.2-.7-.2-.2-.4-.3-.6-.4.2-.2.4-.3.7-.4.3-.1.6-.1.8-.1.3 0 .6 0 .8.1.2.1.4.3.5.5.1.2.2.5.2.9v1.1c0 .3-.1.5-.3.6-.2.2-.5.3-.9.4-.3.1-.7.3-1.1.4-.4.1-.8.3-1.1.5-.3.2-.6.4-.8.7-.2.3-.3.7-.3 1.2 0 .6.2 1.1.5 1.4.3.4.9.5 1.6.5.5 0 1-.1 1.4-.3.4-.2.8-.6 1.1-1.1 0 .4.1.7.3 1 .2.3.6.4 1.2.4.4 0 .7-.1.9-.2.2-.1.5-.3.7-.4h-.3zm-3-.9c-.2.4-.5.7-.8.8-.3.2-.6.2-.8.2-.4 0-.6-.1-.9-.3-.2-.2-.3-.6-.3-1.1 0-.5.1-.9.3-1.2s.5-.5.8-.7c.3-.2.7-.3 1-.5.3-.1.6-.3.7-.6v3.4z"})}),Ze=(0,i.jsx)(ee.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,i.jsx)(ee.Path,{d:"M17.2 10.9c-.5-1-1.2-2.1-2.1-3.2-.6-.9-1.3-1.7-2.1-2.6L12 4l-1 1.1c-.6.9-1.3 1.7-2 2.6-.8 1.2-1.5 2.3-2 3.2-.6 1.2-1 2.2-1 3 0 3.4 2.7 6.1 6.1 6.1s6.1-2.7 6.1-6.1c0-.8-.3-1.8-1-3zm-5.1 7.6c-2.5 0-4.6-2.1-4.6-4.6 0-.3.1-1 .8-2.3.5-.9 1.1-1.9 2-3.1.7-.9 1.3-1.7 1.8-2.3.7.8 1.3 1.6 1.8 2.3.8 1.1 1.5 2.2 2 3.1.7 1.3.8 2 .8 2.3 0 2.5-2.1 4.6-4.6 4.6z"})}),qe=(0,i.jsx)(ee.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ee.Path,{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})}),Je={};function Ye(e){const t=e=>{if("object"==typeof e&&null!==e||void 0===e){if(Array.isArray(e)&&0===e.length)return;for(const o in e)if(e.hasOwnProperty(o)){const n=t(e[o]);void 0===n?delete e[o]:e[o]=n}}return e};return t(e)}const Ke=()=>{const{userTheme:e,updateUserTheme:t}=ve(),o=(0,l.useMemo)((()=>e?Ye(function(e){const t=e=>{if("object"==typeof e&&null!==e)for(const o in e)e.hasOwnProperty(o)&&(e[o]=t(e[o]));else if("string"==typeof e)return e.replace(/var\(--([a-z]+)--([a-z]+(?:--[a-z0-9]+(?:-[a-z0-9]+)*)*)--([a-z0-9-]+)\)/g,((e,t,o,n)=>`var:${o.split("--").concat(n).join("|")}`));return e};return t(e)}(e?.styles)):Je),[e]),{styles:n}=(0,a.useSelect)((e=>({styles:e(M).getStyles()}))),r=(0,l.useCallback)((o=>{const n={...e,styles:Ye(o)};t(n)}),[t,e]),s=(0,l.useCallback)(((o,n)=>{const r=function(e,t,o){const n=Array.isArray(t)?[...t]:[t],r=Array.isArray(e)?[...e]:{...e},s=n.pop();let i=r;return n.forEach((e=>{const t=i[e];i[e]=Array.isArray(t)?[...t]:{...t},i=i[e]})),i[s]=o,r}(e,["styles",...o],n);t(r)}),[t,e]);return{styles:(0,l.useMemo)((()=>n?o?be().all([n,o]):n:Je),[n,o]),userStyles:e?.styles,defaultStyles:n,updateStyleProp:s,updateStyles:r}},Xe={start:{scale:1,opacity:1},hover:{scale:0,opacity:0}},Qe={hover:{opacity:1},start:{opacity:.5}},et={hover:{scale:1,opacity:1},start:{scale:0,opacity:0}};function tt({label:e,isFocused:t,withHoverView:o}){const{colors:n}=(0,a.useSelect)((e=>({colors:e(M).getPaletteColors()})),[]),r=(0,l.useMemo)((()=>n.theme.concat(n.default)),[n]),{styles:s}=Ke(),{backgroundColor:c,headingColor:d,textColorPaletteObject:m,buttonBackgroundColorPaletteObject:p}=(0,l.useMemo)((()=>{const e=Se(s?.color?.background)||"white",t=Se(s?.color?.text)||"black",o=Se(s?.elements?.h1?.color?.text)||t,n=Se(s?.elements?.link?.color?.text)||o,i=Se(s?.elements?.button?.color?.background)||n,a=r.find((({color:e})=>e.toLowerCase()===t.toLowerCase())),l=r.find((({color:e})=>e.toLowerCase()===i.toLowerCase()));return{backgroundColor:e,headingColor:o,buttonBackgroundColor:i,textColorPaletteObject:a,buttonBackgroundColorPaletteObject:l}}),[s,r]),u=s?.elements?.heading?.typography?.fontWeight||"inherit",_=s?.elements?.heading?.typography?.fontFamily||"inherit",g=[...m?[m]:[],...p?[p]:[],...r].filter((({color:e})=>e.toLowerCase()!==c.toLowerCase())).slice(0,2),[h,y]=(0,l.useState)(!1);return(0,i.jsx)("div",{onMouseEnter:()=>y(!0),onMouseLeave:()=>y(!1),children:(0,i.jsxs)(x.__unstableMotion.div,{style:{height:152,width:"100%",background:c,cursor:o?"pointer":void 0},initial:"start",animate:(h||t)&&e?"hover":"start",children:[(0,i.jsx)(x.__unstableMotion.div,{variants:Xe,style:{height:"100%",overflow:"hidden"},children:(0,i.jsxs)(x.__experimentalHStack,{spacing:10,justify:"center",style:{height:"100%",overflow:"hidden"},children:[(0,i.jsx)(x.__unstableMotion.div,{style:{fontFamily:_,fontSize:65,color:d,fontWeight:u},animate:{scale:1,opacity:1},initial:{scale:.1,opacity:0},transition:{delay:.3,type:"tween"},children:"Aa"}),(0,i.jsx)(x.__experimentalVStack,{spacing:4,children:g.map((({slug:e,color:t},o)=>(0,i.jsx)(x.__unstableMotion.div,{style:{height:32,width:32,background:t,borderRadius:16},animate:{scale:1,opacity:1},initial:{scale:.1,opacity:0},transition:{delay:1===o?.2:.1}},e)))})]})}),(0,i.jsx)(x.__unstableMotion.div,{variants:o&&Qe,style:{height:"100%",width:"100%",position:"absolute",top:0,overflow:"hidden",filter:"blur(60px)",opacity:.1},children:(0,i.jsx)(x.__experimentalHStack,{spacing:0,justify:"flex-start",style:{height:"100%",overflow:"hidden"},children:r.slice(0,4).map((({color:e})=>(0,i.jsx)("div",{style:{height:"100%",background:e,flexGrow:1}},e)))})}),(0,i.jsx)(x.__unstableMotion.div,{variants:et,style:{height:"100%",width:"100%",overflow:"hidden",position:"absolute",top:0},children:(0,i.jsx)(x.__experimentalVStack,{spacing:3,justify:"center",style:{height:"100%",overflow:"hidden",padding:10,boxSizing:"border-box"},children:e&&(0,i.jsx)("div",{style:{fontSize:40,fontFamily:_,color:d,fontWeight:u,lineHeight:"1em",textAlign:"center"},children:e})})})]})})}function ot(){return(0,i.jsx)(x.Card,{size:"small",className:"edit-site-global-styles-screen-root",variant:"primary",children:(0,i.jsx)(x.CardBody,{children:(0,i.jsxs)(x.__experimentalVStack,{spacing:4,children:[(0,i.jsx)(x.Card,{children:(0,i.jsx)(x.CardMedia,{children:(0,i.jsx)(tt,{})})}),(0,i.jsxs)(x.__experimentalItemGroup,{children:[(0,i.jsx)(x.__experimentalNavigatorButton,{path:"/typography",onClick:()=>P("styles_sidebar_navigation_click",{path:"typography"}),children:(0,i.jsx)(x.__experimentalItem,{children:(0,i.jsxs)(x.__experimentalHStack,{justify:"flex-start",children:[(0,i.jsx)(x.Icon,{icon:Ue,size:24}),(0,i.jsx)(x.FlexItem,{children:(0,g.__)("Typography","woocommerce")})]})})}),(0,i.jsx)(x.__experimentalNavigatorButton,{path:"/colors",onClick:()=>P("styles_sidebar_navigation_click",{path:"colors"}),children:(0,i.jsx)(x.__experimentalItem,{children:(0,i.jsxs)(x.__experimentalHStack,{justify:"flex-start",children:[(0,i.jsx)(x.Icon,{icon:Ze,size:24}),(0,i.jsx)(x.FlexItem,{children:(0,g.__)("Colors","woocommerce")})]})})}),(0,i.jsx)(x.__experimentalNavigatorButton,{path:"/layout",onClick:()=>P("styles_sidebar_navigation_click",{path:"layout"}),children:(0,i.jsx)(x.__experimentalItem,{children:(0,i.jsxs)(x.__experimentalHStack,{justify:"flex-start",children:[(0,i.jsx)(x.Icon,{icon:qe,size:24}),(0,i.jsx)(x.FlexItem,{children:(0,g.__)("Layout","woocommerce")})]})})})]})]})})})}const nt={typography:{},color:{}},rt=(e,t,o="heading",n=!1)=>{switch(t){case"text":return{typography:e.typography,color:e.color};case"heading":return((e,t="heading",o=!1)=>o?be().all([nt,e.elements.heading||{},e.elements[t]||{}]):{...nt,...e.elements.heading||{},...e.elements[t]||{}})(e,null!=o?o:"heading",n);default:return e.elements[t]||nt}};function st({element:e,label:t}){const{styles:o}=Ke(),n=rt(o,e,null,!0),{fontFamily:r,fontStyle:s,fontWeight:a,letterSpacing:l,textDecoration:c,textTransform:d}=n.typography,m=n.color?.text||"inherit",p=n.color?.background||"#f0f0f0",u=(0,g.sprintf)((0,g.__)("Typography %s styles","woocommerce"),t);return(0,i.jsx)(x.__experimentalItem,{children:(0,i.jsx)(x.__experimentalNavigatorButton,{path:`/typography/${e}`,"aria-label":u,onClick:()=>P("styles_sidebar_screen_typography_button_click",{element:e,label:t,path:`typography/${e}`}),children:(0,i.jsxs)(x.__experimentalHStack,{justify:"flex-start",children:[(0,i.jsx)(x.FlexItem,{className:"edit-site-global-styles-screen-typography__indicator",style:{fontFamily:null!=r?r:"serif",background:p,color:m,fontStyle:null!=s?s:"normal",fontWeight:null!=a?a:"normal",letterSpacing:null!=l?l:"normal",textDecoration:null!=c?c:"link"===e?"underline":"none",textTransform:null!=d?d:"none"},children:"Aa"}),(0,i.jsx)(x.FlexItem,{children:t})]})})})}const it=function(){return(0,i.jsx)(x.Card,{size:"small",variant:"primary",isBorderless:!0,children:(0,i.jsx)(x.CardBody,{children:(0,i.jsxs)(x.__experimentalVStack,{spacing:3,children:[(0,i.jsx)(x.__experimentalHeading,{level:3,className:"edit-site-global-styles-subtitle",children:(0,g.__)("Elements","woocommerce")}),(0,i.jsxs)(x.__experimentalItemGroup,{isBordered:!0,isSeparated:!0,size:"small",children:[(0,i.jsx)(st,{element:"text",label:(0,g.__)("Text","woocommerce")}),(0,i.jsx)(st,{element:"link",label:(0,g.__)("Links","woocommerce")}),(0,i.jsx)(st,{element:"heading",label:(0,g.__)("Headings","woocommerce")}),(0,i.jsx)(st,{element:"button",label:(0,g.__)("Buttons","woocommerce")})]})]})})})},at=(0,i.jsx)(ee.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ee.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"})}),lt=x.Navigator||x.__experimentalNavigatorProvider;function ct({title:e,description:t,onBack:o}){return(0,i.jsxs)(x.__experimentalVStack,{spacing:0,children:[(0,i.jsx)(x.__experimentalView,{children:(0,i.jsx)(x.__experimentalSpacer,{marginBottom:0,paddingX:4,paddingY:3,children:(0,i.jsxs)(x.__experimentalHStack,{spacing:2,children:[(0,i.jsx)(lt.BackButton,{style:{minWidth:24,padding:0},icon:at,size:"small","aria-label":(0,g.__)("Navigate to the previous view","woocommerce"),onClick:o}),(0,i.jsx)(x.__experimentalSpacer,{children:(0,i.jsx)(x.__experimentalHeading,{className:"woocommerce-email-editor-styles-header",level:2,size:13,children:e})})]})})}),t&&(0,i.jsx)("p",{className:"woocommerce-email-editor-styles-header-description",children:t})]})}x.Navigator||(lt.Screen=x.__experimentalNavigatorScreen,lt.BackButton=x.__experimentalNavigatorBackButton);const dt=ct;function mt(){return N("styles_sidebar_screen_typography_opened"),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(dt,{title:(0,g.__)("Typography","woocommerce"),description:(0,g.__)("Manage the typography settings for different elements.","woocommerce")}),(0,i.jsx)(it,{})]})}const pt={fontFamily:!0,fontSize:!0,fontAppearance:!0,lineHeight:!0,letterSpacing:!1,textTransform:!1,textDecoration:!1,writingMode:!0,textColumns:!0},ut=function({element:e,headingLevel:t,defaultControls:o=pt}){const[n,r]=(0,p.useSettings)("typography.fontSizes","typography.fontFamilies"),s=r?.default||[],{styles:a,defaultStyles:c,updateStyleProp:d}=Ke(),m=rt(a,e,t),u=rt(c,e,t),{fontFamily:_,fontSize:h,fontStyle:y,fontWeight:w,lineHeight:f,letterSpacing:b,textDecoration:v,textTransform:j}=m.typography,{fontFamily:k,fontSize:S,fontStyle:C,fontWeight:E,lineHeight:T,letterSpacing:N,textDecoration:M,textTransform:F}=u.typography,I="heading"!==e||"heading"!==t,z=(0,l.useCallback)(((o,n)=>{d("heading"===e?["elements",t,...o]:"text"===e?[...o]:["elements",e,...o],n)}),[e,d,t]),R=t=>{z(["typography","letterSpacing"],t),B("styles_sidebar_screen_typography_element_panel_set_letter_spacing",{element:e,newValue:t,selectedDefaultLetterSpacing:t===N})},L=t=>{z(["typography","lineHeight"],t),B("styles_sidebar_screen_typography_element_panel_set_line_height",{element:e,newValue:t,selectedDefaultLineHeight:t===T})},A=o=>{z(["typography","fontSize"],o),B("styles_sidebar_screen_typography_element_panel_set_font_size",{element:e,headingLevel:t,newValue:o,selectedDefaultFontSize:o===S})},O=t=>{z(["typography","fontFamily"],t),B("styles_sidebar_screen_typography_element_panel_set_font_family",{element:e,newValue:t,selectedDefaultFontFamily:t===k})},H=t=>{z(["typography","textDecoration"],t),B("styles_sidebar_screen_typography_element_panel_set_text_decoration",{element:e,newValue:t,selectedDefaultTextDecoration:t===M})},V=t=>{z(["typography","textTransform"],t),B("styles_sidebar_screen_typography_element_panel_set_text_transform",{element:e,newValue:t,selectedDefaultTextTransform:t===F})},D=({fontStyle:t,fontWeight:o})=>{z(["typography","fontStyle"],t),z(["typography","fontWeight"],o),B("styles_sidebar_screen_typography_element_panel_set_font_appearance",{element:e,newFontStyle:t,newFontWeight:o,selectedDefaultFontStyle:t===C,selectedDefaultFontWeight:o===E})};return(0,i.jsxs)(x.__experimentalToolsPanel,{label:(0,g.__)("Typography","woocommerce"),resetAll:()=>{z(["typography"],u.typography),P("styles_sidebar_screen_typography_element_panel_reset_all_styles_selected",{element:e,headingLevel:t})},children:[(0,i.jsx)(x.__experimentalToolsPanelItem,{label:(0,g.__)("Font family","woocommerce"),hasValue:()=>_!==k,onDeselect:()=>O(k),isShownByDefault:o.fontFamily,children:(0,i.jsx)(p.__experimentalFontFamilyControl,{value:_,onChange:O,size:"__unstable-large",fontFamilies:s,__nextHasNoMarginBottom:!0})}),I&&(0,i.jsx)(x.__experimentalToolsPanelItem,{label:(0,g.__)("Font size","woocommerce"),hasValue:()=>h!==S,onDeselect:()=>A(S),isShownByDefault:o.fontSize,children:(0,i.jsx)(x.FontSizePicker,{value:h,onChange:A,fontSizes:n,disableCustomFontSizes:!1,withReset:!1,withSlider:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0})}),(0,i.jsx)(x.__experimentalToolsPanelItem,{className:"single-column",label:(0,g.__)("Appearance","woocommerce"),hasValue:()=>w!==E||y!==C,onDeselect:()=>{D({fontStyle:C,fontWeight:E})},isShownByDefault:o.fontAppearance,children:(0,i.jsx)(p.__experimentalFontAppearanceControl,{value:{fontStyle:y,fontWeight:w},onChange:D,hasFontStyles:!0,hasFontWeights:!0,size:"__unstable-large"})}),(0,i.jsx)(x.__experimentalToolsPanelItem,{className:"single-column",label:(0,g.__)("Line height","woocommerce"),hasValue:()=>f!==T,onDeselect:()=>L(T),isShownByDefault:o.lineHeight,children:(0,i.jsx)(p.LineHeightControl,{__nextHasNoMarginBottom:!0,__unstableInputWidth:"auto",value:f,onChange:L,size:"__unstable-large"})}),(0,i.jsx)(x.__experimentalToolsPanelItem,{className:"single-column",label:(0,g.__)("Letter spacing","woocommerce"),hasValue:()=>b!==N,onDeselect:()=>R(N),isShownByDefault:o.letterSpacing,children:(0,i.jsx)(p.__experimentalLetterSpacingControl,{value:b,onChange:R,size:"__unstable-large",__unstableInputWidth:"auto"})}),(0,i.jsx)(x.__experimentalToolsPanelItem,{className:"single-column",label:(0,g.__)("Text decoration","woocommerce"),hasValue:()=>v!==M,onDeselect:()=>H(M),isShownByDefault:o.textDecoration,children:(0,i.jsx)(p.__experimentalTextDecorationControl,{value:v,onChange:H,size:"__unstable-large",__unstableInputWidth:"auto"})}),(0,i.jsx)(x.__experimentalToolsPanelItem,{label:(0,g.__)("Letter case","woocommerce"),hasValue:()=>j!==F,onDeselect:()=>V(F),isShownByDefault:o.textTransform,children:(0,i.jsx)(p.__experimentalTextTransformControl,{value:j,onChange:V,showNone:!0,isBlock:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0})})]})};function _t({element:e,headingLevel:t}){const{styles:o}=Ke(),n=rt(o,e,t,!0),{fontFamily:r,fontSize:s,fontStyle:a,fontWeight:l,lineHeight:c,letterSpacing:d,textDecoration:m,textTransform:p}=n.typography,u=n.color?.text||"inherit",_=n.color?.background||"#f0f0f0",g="link"===e?{textDecoration:null!=m?m:"underline"}:{};return(0,i.jsx)("div",{className:"edit-site-typography-preview",style:{fontFamily:null!=r?r:"serif",background:_,color:u,lineHeight:c,fontSize:s,fontStyle:a,fontWeight:l,letterSpacing:d,textDecoration:m,textTransform:p,...g},children:"Aa"})}const gt={text:{title:(0,g.__)("Text","woocommerce"),description:(0,g.__)("Manage the fonts and typography used on text.","woocommerce"),defaultControls:pt},link:{title:(0,g.__)("Links","woocommerce"),description:(0,g.__)("Manage the fonts and typography used on links.","woocommerce"),defaultControls:{...pt,textDecoration:!0}},heading:{title:(0,g.__)("Headings","woocommerce"),description:(0,g.__)("Manage the fonts and typography used on headings.","woocommerce"),defaultControls:{...pt,textTransform:!0}},button:{title:(0,g.__)("Buttons","woocommerce"),description:(0,g.__)("Manage the fonts and typography used on buttons.","woocommerce"),defaultControls:pt}};function ht({element:e}){N("styles_sidebar_screen_typography_element_opened",{element:e});const[t,o]=(0,l.useState)("heading");return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(dt,{title:gt[e].title,description:gt[e].description}),(0,i.jsx)(x.__experimentalSpacer,{marginX:4,children:(0,i.jsx)(_t,{element:e,headingLevel:t})}),"heading"===e&&(0,i.jsx)(x.__experimentalSpacer,{marginX:4,marginBottom:"1em",children:(0,i.jsxs)(x.__experimentalToggleGroupControl,{label:(0,g.__)("Select heading level","woocommerce"),hideLabelFromVision:!0,value:t,onChange:e=>{o(e.toString()),P("styles_sidebar_screen_typography_element_heading_level_selected",{value:e})},isBlock:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0,children:[(0,i.jsx)(x.__experimentalToggleGroupControlOption,{value:"heading",label:(0,g._x)("All","heading levels","woocommerce")}),(0,i.jsx)(x.__experimentalToggleGroupControlOption,{value:"h1",label:(0,g._x)("H1","Heading Level","woocommerce")}),(0,i.jsx)(x.__experimentalToggleGroupControlOption,{value:"h2",label:(0,g._x)("H2","Heading Level","woocommerce")}),(0,i.jsx)(x.__experimentalToggleGroupControlOption,{value:"h3",label:(0,g._x)("H3","Heading Level","woocommerce")}),(0,i.jsx)(x.__experimentalToggleGroupControlOption,{value:"h4",label:(0,g._x)("H4","Heading Level","woocommerce")}),(0,i.jsx)(x.__experimentalToggleGroupControlOption,{value:"h5",label:(0,g._x)("H5","Heading Level","woocommerce")}),(0,i.jsx)(x.__experimentalToggleGroupControlOption,{value:"h6",label:(0,g._x)("H6","Heading Level","woocommerce")})]})}),(0,i.jsx)(ut,{element:e,headingLevel:t,defaultControls:gt[e].defaultControls})]})}function yt(){N("styles_sidebar_screen_colors_opened");const{userStyles:e,styles:t,updateStyles:o}=Ke(),n=(0,a.useSelect)((e=>e(M).getTheme()),[]);return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(dt,{title:(0,g.__)("Colors","woocommerce"),description:(0,g.__)("Manage palettes and the default color of different global elements.","woocommerce")}),(0,i.jsx)(G,{value:e,inheritedValue:t,onChange:e=>{o(e),P("styles_sidebar_screen_colors_styles_updated")},settings:n?.settings,panelId:"colors"})]})}function wt(){const[e]=(0,p.useSettings)("spacing.units"),t=(0,x.__experimentalUseCustomUnits)({availableUnits:e}),{styles:o,defaultStyles:n,updateStyleProp:r}=Ke();return(0,i.jsxs)(x.__experimentalToolsPanel,{label:(0,g.__)("Dimensions","woocommerce"),resetAll:()=>{r(["spacing"],n.spacing),P("styles_sidebar_screen_layout_dimensions_reset_all_selected")},children:[(0,i.jsx)(x.__experimentalToolsPanelItem,{isShownByDefault:!0,hasValue:()=>!(0,S.isEqual)(o.spacing.padding,n.spacing.padding),label:(0,g.__)("Padding","woocommerce"),onDeselect:()=>{r(["spacing","padding"],n.spacing.padding),P("styles_sidebar_screen_layout_dimensions_padding_reset_clicked")},className:"tools-panel-item-spacing",children:(0,i.jsx)(p.__experimentalSpacingSizesControl,{allowReset:!0,values:o.spacing.padding,onChange:e=>{r(["spacing","padding"],e),B("styles_sidebar_screen_layout_dimensions_padding_updated",{value:e})},label:(0,g.__)("Padding","woocommerce"),sides:["horizontal","vertical","top","left","right","bottom"],units:t})}),(0,i.jsx)(x.__experimentalToolsPanelItem,{isShownByDefault:!0,label:(0,g.__)("Block spacing","woocommerce"),hasValue:()=>o.spacing.blockGap!==n.spacing.blockGap,onDeselect:()=>{r(["spacing","blockGap"],n.spacing.blockGap),P("styles_sidebar_screen_layout_dimensions_block_spacing_reset_clicked")},className:"tools-panel-item-spacing",children:(0,i.jsx)(p.__experimentalSpacingSizesControl,{label:(0,g.__)("Block spacing","woocommerce"),min:0,onChange:e=>{r(["spacing","blockGap"],e.top),B("styles_sidebar_screen_layout_dimensions_block_spacing_updated",{value:e})},showSideInLabel:!1,sides:["top"],values:{top:o.spacing.blockGap},allowReset:!0})})]})}function xt(){return N("styles_sidebar_screen_layout_opened"),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(ct,{title:(0,g.__)("Layout","woocommerce")}),(0,i.jsx)(wt,{})]})}const ft=(0,l.memo)((function(){const{userCanEditGlobalStyles:e}=(0,a.useSelect)((e=>{const{canEdit:t}=e(M).canUserEditGlobalEmailStyles();return{userCanEditGlobalStyles:t}}),[]);return e&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(D.PluginSidebarMoreMenuItem,{target:"email-styles-sidebar",icon:$e,children:(0,g.__)("Email styles","woocommerce")}),(0,i.jsx)(D.PluginSidebar,{name:"email-styles-sidebar",icon:$e,title:(0,g.__)("Styles","woocommerce"),className:"woocommerce-email-editor-styles-panel",header:(0,g.__)("Styles","woocommerce"),children:(0,i.jsxs)(lt,{initialPath:"/",children:[(0,i.jsx)(lt.Screen,{path:"/",children:(0,i.jsx)(ot,{})}),(0,i.jsx)(lt.Screen,{path:"/typography",children:(0,i.jsx)(mt,{})}),(0,i.jsx)(lt.Screen,{path:"/typography/text",children:(0,i.jsx)(ht,{element:"text"})}),(0,i.jsx)(lt.Screen,{path:"/typography/link",children:(0,i.jsx)(ht,{element:"link"})}),(0,i.jsx)(lt.Screen,{path:"/typography/heading",children:(0,i.jsx)(ht,{element:"heading"})}),(0,i.jsx)(lt.Screen,{path:"/typography/button",children:(0,i.jsx)(ht,{element:"button"})}),(0,i.jsx)(lt.Screen,{path:"/colors",children:(0,i.jsx)(yt,{})}),(0,i.jsx)(lt.Screen,{path:"/layout",children:(0,i.jsx)(xt,{})})]})})]})})),bt=(0,i.jsx)(ee.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ee.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})}),vt=(0,i.jsx)(ee.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ee.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})}),jt=window.wp.keycodes,kt=window.wp.url;var St;!function(e){e.SUCCESS="success",e.ERROR="error"}(St||(St={}));const Ct=(0,l.memo)((function(){const e=(0,l.useRef)(null),{requestSendingNewsletterPreview:t,togglePreviewModal:o,updateSendPreviewEmail:n}=(0,a.useDispatch)(M),{toEmail:r,isSendingPreviewEmail:s,sendingPreviewStatus:d,isModalOpened:m,errorMessage:p,postType:u}=(0,a.useSelect)((e=>({...e(M).getPreviewState(),postType:e(M).getEmailPostType()})),[]),_=()=>{t(r)},h=(0,l.useMemo)((()=>(0,c.applyFilters)("woocommerce_email_editor_check_sending_method_configuration_link",`https://www.mailpoet.com/blog/mailpoet-smtp-plugin/?utm_source=woocommerce_email_editor&utm_medium=plugin&utm_source_platform=${u}`)),[u]),y=()=>{P("send_preview_email_modal_closed"),o(!1)};return(0,l.useEffect)((()=>{m&&(e.current?.focus(),P("send_preview_email_modal_opened"))}),[m]),m?(0,i.jsxs)(x.Modal,{className:"woocommerce-send-preview-email",title:(0,g.__)("Send a test email","woocommerce"),onRequestClose:y,focusOnMount:!1,children:[d===St.ERROR?(0,i.jsxs)("div",{className:"woocommerce-send-preview-modal-notice-error",children:[(0,i.jsx)("p",{children:(0,g.__)("Sorry, we were unable to send this email.","woocommerce")}),(0,i.jsx)("strong",{children:p&&(0,g.sprintf)((0,g.__)("Error: %s","woocommerce"),p)}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:h&&(0,l.createInterpolateElement)((0,g.__)("Please check your <link>sending method configuration</link> with your hosting provider.","woocommerce"),{link:(0,i.jsx)("a",{href:h,target:"_blank",rel:"noopener noreferrer",onClick:()=>P("send_preview_email_modal_check_sending_method_configuration_link_clicked")})})}),(0,i.jsx)("li",{children:(0,l.createInterpolateElement)((0,g.__)("Or, sign up for MailPoet Sending Service to easily send emails. <link>Sign up for free</link>","woocommerce"),{link:(0,i.jsx)("a",{href:`https://account.mailpoet.com/?s=1&g=1&utm_source=woocommerce_email_editor&utm_medium=plugin&utm_source_platform=${u}`,target:"_blank",rel:"noopener noreferrer",onClick:()=>P("send_preview_email_modal_sign_up_for_mailpoet_sending_service_link_clicked")},"sign-up-for-free")})})]})]}):null,(0,i.jsx)("p",{children:(0,g.__)("Send yourself a test email to test how your email would look like in different email apps.","woocommerce")}),(0,i.jsx)(x.TextControl,{label:(0,g.__)("Send to","woocommerce"),onChange:e=>{n(e),N("send_preview_email_modal_send_to_field_updated")},onKeyDown:e=>{const{keyCode:t}=e;t===jt.ENTER&&(e.preventDefault(),_(),P("send_preview_email_modal_send_to_field_key_code_enter"))},className:"woocommerce-send-preview-email__send-to-field",value:r,type:"email",ref:e,required:!0,__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0}),d===St.SUCCESS?(0,i.jsxs)("p",{className:"woocommerce-send-preview-modal-notice-success",children:[(0,i.jsx)(Me,{icon:vt,style:{fill:"#4AB866"}}),(0,g.__)("Test email sent successfully!","woocommerce")]}):null,(0,i.jsxs)("div",{className:"woocommerce-send-preview-modal-footer",children:[(0,i.jsx)(x.Button,{variant:"tertiary",onClick:()=>{P("send_preview_email_modal_close_button_clicked"),y()},children:(0,g.__)("Cancel","woocommerce")}),(0,i.jsx)(x.Button,{variant:"primary",onClick:()=>{_(),P("send_preview_email_modal_send_test_email_button_clicked")},disabled:s||!(0,kt.isEmail)(r),children:s?(0,g.__)("Sending…","woocommerce"):(0,g.__)("Send test email","woocommerce")})]})]}):null}));function Et(){const{togglePreviewModal:e}=(0,a.useDispatch)(M);return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(D.PluginPreviewMenuItem,{icon:bt,onClick:()=>{P("header_preview_dropdown_send_test_email_selected"),e(!0)},children:(0,g.__)("Send a test email","woocommerce")}),(0,i.jsx)(Ct,{})]})}const Tt=window.wp.preferences,Pt=()=>{const e=(0,u.useViewportMatch)("large");return(0,i.jsx)(i.Fragment,{children:e&&(0,i.jsx)(q,{children:(0,i.jsx)(Tt.PreferenceToggleMenuItem,{scope:M,name:"fullscreenMode",label:(0,g.__)("Fullscreen mode","woocommerce"),info:(0,g.__)("Show and hide the admin user interface","woocommerce"),messageActivated:(0,g.__)("Fullscreen mode activated.","woocommerce"),messageDeactivated:(0,g.__)("Fullscreen mode deactivated.","woocommerce"),shortcut:jt.displayShortcut.secondary("f")})})})};function Nt({label:e,labelSuffix:t,help:o,placeholder:n,attributeName:r,attributeValue:s,updateProperty:c=()=>{}}){const[d,m]=(0,l.useState)(null),[u,_]=(0,l.useState)(!1),h=(0,a.useSelect)((e=>e(M).getPersonalizationTagsList()),[]),y=(0,l.useRef)(null),v=(0,l.useCallback)(((e,t,o)=>{var n,s;const i=null!==(n=o?.start)&&void 0!==n?n:t.length,a=null!==(s=o?.end)&&void 0!==s?s:t.length;let l=(0,w.create)({html:t});l=(0,w.insert)(l,(0,w.create)({html:`\x3c!--${e}--\x3e`}),i,a);const d=(0,w.toHTMLString)({value:l});c(r,d),m(null)}),[r,c]),j=(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("span",{children:e}),(0,i.jsx)(x.Button,{className:"woocommerce-settings-panel-personalization-tags-button",icon:"shortcode",title:(0,g.__)("Personalization Tags","woocommerce"),onClick:()=>{_(!0),P("rich_text_with_button_personalization_tags_shortcode_icon_clicked",{attributeName:r,label:e})}}),t]});return r?(0,i.jsxs)(x.BaseControl,{id:"",label:j,className:`woocommerce-settings-panel-${r}-text`,help:o,__nextHasNoMarginBottom:!0,children:[(0,i.jsx)(F,{isOpened:u,onInsert:e=>{v(e,null!=s?s:"",d),_(!1),P("rich_text_with_button_personalization_tags_inserted",{attributeName:r,value:e})},closeCallback:()=>_(!1),openedBy:"RichTextWithButton-BaseControl"}),(0,i.jsx)(I,{contentRef:y,onUpdate:(e,t)=>{const o=(null!=s?s:"").replace(`\x3c!--[${e}]--\x3e`,`\x3c!--[${t}]--\x3e`);c(r,o)}}),(0,i.jsx)(p.RichText,{ref:y,className:"woocommerce-settings-panel-richtext",placeholder:n,onFocus:()=>{m(f(y,null!=s?s:""))},onKeyUp:()=>{m(f(y,null!=s?s:""))},onClick:()=>{m(f(y,null!=s?s:""))},onChange:e=>{var t;e=b(null!==(t=e)&&void 0!==t?t:"",h),c(r,e),N("rich_text_with_button_input_field_updated",{attributeName:r})},value:null!=s?s:"","data-automation-id":`email_${r}`})]}):null}function Bt({close:e}){N("edit_template_modal_opened");const{onNavigateToEntityRecord:t,template:o}=(0,a.useSelect)((e=>{const{getEditorSettings:t}=e(D.store);return{onNavigateToEntityRecord:t().onNavigateToEntityRecord,template:e(M).getCurrentTemplate()}}),[]);return(0,i.jsxs)(x.Modal,{size:"medium",onRequestClose:e,__experimentalHideHeader:!0,children:[(0,i.jsx)("p",{children:(0,g.__)("This template is used by multiple emails. Any changes made would affect other emails on the site. Are you sure you want to edit the template?","woocommerce")}),(0,i.jsxs)(x.Flex,{justify:"end",children:[(0,i.jsx)(x.FlexItem,{children:(0,i.jsx)(x.Button,{variant:"tertiary",onClick:()=>{P("edit_template_modal_cancel_button_clicked"),e()},children:(0,g.__)("Cancel","woocommerce")})}),(0,i.jsx)(x.FlexItem,{children:(0,i.jsx)(x.Button,{variant:"primary",onClick:()=>{P("edit_template_modal_continue_button_clicked",{templateId:o.id}),t({postId:o.id,postType:"wp_template"})},disabled:!o.id,children:(0,g.__)("Edit template","woocommerce")})})]})]})}function Mt(){const{template:e,currentEmailContent:t,canUpdateTemplates:o,postType:n}=(0,a.useSelect)((e=>({template:e(M).getCurrentTemplate(),currentEmailContent:e(M).getEditedEmailContent(),canUpdateTemplates:e(M).canUserEditTemplates(),postType:e(M).getEmailPostType()})),[]),[r]=Be("swap"),[s,c]=(0,l.useState)(!1),[d,m]=(0,l.useState)(!1);return(0,i.jsxs)(i.Fragment,{children:[e&&(0,i.jsx)(x.PanelRow,{children:(0,i.jsxs)(x.Flex,{justify:"start",children:[(0,i.jsx)(x.FlexItem,{className:"editor-post-panel__row-label",children:(0,g.__)("Template","woocommerce")}),(0,i.jsxs)(x.FlexItem,{children:[!(r?.length>1||o)&&(0,i.jsx)("b",{children:e?.title}),(r?.length>1||o)&&(0,i.jsx)(x.DropdownMenu,{icon:null,text:e?.title,toggleProps:{variant:"tertiary"},label:(0,g.__)("Template actions","woocommerce"),onToggle:t=>P("sidebar_template_actions_clicked",{currentTemplate:e?.title,isOpen:t}),children:({onClose:e})=>(0,i.jsxs)(i.Fragment,{children:[o&&(0,i.jsx)(x.MenuItem,{onClick:()=>{P("sidebar_template_actions_edit_template_clicked"),c(!0),e()},children:(0,g.__)("Edit template","woocommerce")}),r?.length>1&&(0,i.jsx)(x.MenuItem,{onClick:()=>{P("sidebar_template_actions_swap_template_clicked"),m(!0),e()},children:(0,g.__)("Swap template","woocommerce")})]})})]})]})}),s&&(0,i.jsx)(Bt,{close:()=>(P("edit_template_modal_closed"),c(!1))}),d&&(0,i.jsx)(We,{onSelectCallback:()=>m(!1),closeCallback:()=>m(!1),previewContent:t,postType:n})]})}const Ft={recordEvent:P,recordEventOnce:N,debouncedRecordEvent:B};function It(){const e=(0,l.useMemo)((()=>(0,c.applyFilters)("woocommerce_email_editor_setting_sidebar_extension_component",Nt,Ft)),[]),t=(0,l.useMemo)((()=>(0,c.applyFilters)("woocommerce_email_editor_setting_sidebar_email_status_component",(()=>null),Ft)),[]);return(0,i.jsxs)(D.PluginDocumentSettingPanel,{name:"email-settings-panel",title:(0,g.__)("Settings","woocommerce"),className:"woocommerce-email-editor__settings-panel",children:[(0,i.jsx)(t,{}),(0,i.jsx)(Mt,{}),(0,i.jsx)(D.ErrorBoundary,{canCopyContent:!0,children:(0,i.jsx)(e,{})})]})}const zt={recordEvent:P,recordEventOnce:N,debouncedRecordEvent:B};function Rt(){const e=(0,c.applyFilters)("woocommerce_email_editor_template_sections",[],zt);return 0===e.length?null:(0,i.jsx)(D.PluginDocumentSettingPanel,{name:"template-settings-panel",title:(0,g.__)("Settings","woocommerce"),className:"woocommerce-email-editor__settings-panel",children:e.map((e=>(0,i.jsx)(D.ErrorBoundary,{children:(0,i.jsx)("div",{children:e.render()},e.id)},`error-boundary-${e.id}`)))})}function Lt(){const{isDirty:e}=(0,D.useEntitiesSavedStatesIsDirty)(),{hasEmptyContent:t,isEmailSent:o,urls:n}=(0,a.useSelect)((e=>({hasEmptyContent:e(M).hasEmptyContent(),isEmailSent:e(M).isEmailSent(),urls:e(M).getUrls()})),[]);function r(){n.send&&(window.location.href=n.send)}const s=t||o||e,l=(0,c.applyFilters)("woocommerce_email_editor_send_button_label",(0,g.__)("Send","woocommerce"));return(0,i.jsx)(x.Button,{variant:"primary",size:"compact",onClick:()=>{P("header_send_button_clicked"),(0,c.applyFilters)("woocommerce_email_editor_send_action_callback",r)()},disabled:s,"data-automation-id":"email_editor_send_button",children:l})}function At({children:e}){const t=(0,l.useRef)(document.createElement("div"));return(0,l.useEffect)((()=>{const e=document.getElementsByClassName("editor-post-publish-button__button")[0];e&&e.parentNode?.insertBefore(t.current,e.nextSibling)}),[t]),(0,l.createPortal)((0,i.jsx)(i.Fragment,{children:e}),t.current)}function Ot(){const e=(0,l.useRef)(null),{hasNonPostEntityChanges:t,isEditedPostDirty:o}=(0,a.useSelect)((e=>({hasNonPostEntityChanges:e(D.store).hasNonPostEntityChanges(),isEditedPostDirty:e(D.store).isEditedPostDirty()})),[]),n=t||o,r=(0,l.useCallback)(((e,t)=>{t&&e.classList.contains("force-hidden")&&e.classList.remove("force-hidden"),t||e.classList.contains("force-hidden")||e.classList.add("force-hidden")}),[]);return(0,l.useEffect)((()=>{const t=document.getElementsByClassName("editor-post-publish-button__button")[0];return r(t,n),t?(e.current&&e.current.disconnect(),e.current=new MutationObserver((()=>{r(t,n)})),e.current.observe(t,{attributes:!0,childList:!0,subtree:!1}),()=>e.current?.disconnect()):()=>e.current?.disconnect()}),[n,r]),(0,i.jsx)(At,{children:!n&&(0,i.jsx)(Lt,{})})}const Ht=()=>{const e="email-validation",t=(0,a.useSelect)((t=>t(Q.store).getNotices(e)));return{notices:t,hasValidationNotice:(0,l.useCallback)((e=>e?void 0!==t.find((t=>t.id===e)):t?.length>0),[t]),addValidationNotice:(0,l.useCallback)(((t,o,n=[])=>{(0,a.dispatch)(Q.store).createNotice("error",o,{id:t,isDismissible:!1,actions:n,context:e})}),[e]),removeValidationNotice:(0,l.useCallback)((t=>{(0,a.dispatch)(Q.store).removeNotice(t,e)}),[e])}};function Vt(){const{notices:e}=Ht();return 0===e.length?null:(0,i.jsx)(x.Notice,{status:"error",className:"woocommerce-email-editor-validation-errors components-editor-notices__pinned",isDismissible:!1,children:(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("strong",{children:(0,g.__)("Fix errors to continue:","woocommerce")}),(0,i.jsx)("ul",{children:e.map((({id:e,content:t,actions:o})=>(0,i.jsxs)("li",{children:[t,o.length>0?o.map((({label:e,onClick:t})=>(0,i.jsx)(x.Button,{onClick:t,variant:"link",children:e},e))):null]},e)))})]})})}function Dt({context:e="email-editor"}){const{notices:t}=(0,a.useSelect)((t=>({notices:t(Q.store).getNotices(e)})),[e]),o=(0,l.useMemo)((()=>({"site-editor-save-success":{content:(0,g.__)("Email design updated.","woocommerce"),removeActions:!0},"editor-save":{content:(0,g.__)("Email saved.","woocommerce"),removeActions:!1,contentCheck:e=>e.content.includes((0,g.__)("Post updated."))}})),[]),{removeNotice:n}=(0,a.useDispatch)(Q.store),r=t.filter((({type:e})=>"snackbar"===e)).map((e=>o[e.id]?o[e.id].contentCheck&&!o[e.id].contentCheck(e)?e:{...e,content:o[e.id].content,spokenMessage:o[e.id].content,actions:o[e.id].removeActions?[]:e.actions}:e));return(0,i.jsx)(x.SnackbarList,{notices:r,className:"components-editor-notices__snackbar",onRemove:t=>n(t,e)})}function Wt({children:e}){const[t]=(0,l.useState)(document.createElement("div"));return(0,l.useEffect)((()=>{const e=document.getElementsByClassName("editor-visual-editor ")[0];e&&e.parentNode?.insertBefore(t,e)}),[t]),(0,l.createPortal)((0,i.jsx)(i.Fragment,{children:e}),t)}function Gt(){const{notices:e}=(0,a.useSelect)((e=>({notices:e(Q.store).getNotices("email-editor")})),[]),{removeNotice:t}=(0,a.useDispatch)(Q.store),o=e.filter((({isDismissible:e,type:t})=>e&&"default"===t)),n=e.filter((({isDismissible:e,type:t})=>!e&&"default"===t));return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(Wt,{children:[(0,i.jsx)(x.NoticeList,{notices:n,className:"components-editor-notices__pinned"}),(0,i.jsx)(x.NoticeList,{notices:o,className:"components-editor-notices__dismissible",onRemove:e=>t(e,"email-editor")}),(0,i.jsx)(Vt,{})]}),(0,i.jsx)(Dt,{context:"global"}),(0,i.jsx)(Dt,{context:"email-editor"})]})}const $t=e=>{const t=(0,d.getBlockSupport)(e,"background");return t&&!1!==t?.backgroundImage};function Ut(){const e=(0,a.useSelect)((e=>e("core/block-editor").getSelectedBlock()),[]),t=(0,d.hasBlockSupport)(e?.name,"border",!1)||(0,d.hasBlockSupport)(e?.name,"__experimentalBorder",!1);return(0,i.jsxs)(i.Fragment,{children:[t&&(0,i.jsx)(x.Fill,{name:"InspectorControlsBorder",children:(0,i.jsxs)(x.Notice,{className:"woocommerce-grid-full-width",status:"warning",isDismissible:!1,children:[(0,g.__)("Border display may vary or be unsupported in some email clients.","woocommerce"),(0,i.jsx)("br",{}),(0,g.__)("Units other than pixels (px) lack support in old email clients.","woocommerce")]})}),$t(e?.name)&&(0,i.jsx)(x.Fill,{name:"InspectorControlsBackground",children:(0,i.jsx)(x.Notice,{className:"woocommerce-grid-full-width",status:"warning",isDismissible:!1,children:(0,g.__)("Select a background color for email clients that do not support background images.","woocommerce")})})]})}const Zt=(0,i.jsx)(ee.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24",children:(0,i.jsx)(ee.Path,{d:"M20 10c0-5.51-4.49-10-10-10C4.48 0 0 4.49 0 10c0 5.52 4.48 10 10 10 5.51 0 10-4.48 10-10zM7.78 15.37L4.37 6.22c.55-.02 1.17-.08 1.17-.08.5-.06.44-1.13-.06-1.11 0 0-1.45.11-2.37.11-.18 0-.37 0-.58-.01C4.12 2.69 6.87 1.11 10 1.11c2.33 0 4.45.87 6.05 2.34-.68-.11-1.65.39-1.65 1.58 0 .74.45 1.36.9 2.1.35.61.55 1.36.55 2.46 0 1.49-1.4 5-1.4 5l-3.03-8.37c.54-.02.82-.17.82-.17.5-.05.44-1.25-.06-1.22 0 0-1.44.12-2.38.12-.87 0-2.33-.12-2.33-.12-.5-.03-.56 1.2-.06 1.22l.92.08 1.26 3.41zM17.41 10c.24-.64.74-1.87.43-4.25.7 1.29 1.05 2.71 1.05 4.25 0 3.29-1.73 6.24-4.4 7.78.97-2.59 1.94-5.2 2.92-7.78zM6.1 18.09C3.12 16.65 1.11 13.53 1.11 10c0-1.3.23-2.48.72-3.59C3.25 10.3 4.67 14.2 6.1 18.09zm4.03-6.63l2.58 6.98c-.86.29-1.76.45-2.71.45-.79 0-1.57-.11-2.29-.33.81-2.38 1.62-4.74 2.42-7.1z"})}),qt=(0,i.jsx)(ee.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,i.jsx)(ee.Path,{d:"M20 11.2H6.8l3.7-3.7-1-1L3.9 12l5.6 5.5 1-1-3.7-3.7H20z"})}),Jt={edit:{opacity:0,scale:.2},hover:{opacity:1,scale:1,clipPath:"inset( 22% round 2px )"}},Yt={edit:{clipPath:"inset(0% round 0px)"},hover:{clipPath:"inset( 22% round 2px )"},tap:{clipPath:"inset(0% round 0px)"}},Kt=()=>{const{urls:e}=(0,a.useSelect)((e=>({urls:e(M).getUrls()})),[]);function t(){e.listings&&(window.location.href=e.back)}return(0,i.jsx)(J,{children:({length:e})=>e<=1&&(0,i.jsxs)(x.__unstableMotion.div,{className:"woocommerce-email-editor__view-mode-toggle",transition:{duration:.2},animate:"edit",initial:"edit",whileHover:"hover",whileTap:"tap",children:[(0,i.jsx)(x.Button,{label:(0,g.__)("Close editor","woocommerce"),showTooltip:!0,tooltipPosition:"middle right",onClick:()=>{P("header_close_button_clicked"),(0,c.applyFilters)("woocommerce_email_editor_close_action_callback",t)()},children:(0,i.jsx)(x.__unstableMotion.div,{variants:Yt,children:(0,i.jsx)("div",{className:"woocommerce-email-editor__view-mode-toggle-icon",children:(0,i.jsx)(Me,{className:"woocommerce-email-editor-icon__icon",icon:Zt,size:48})})})}),(0,i.jsx)(x.__unstableMotion.div,{className:"woocommerce-email-editor-icon",variants:Jt,children:(0,i.jsx)(Me,{icon:qt})})]})})};function Xt({postId:e,postType:t,settings:o}){const{currentPost:n,onNavigateToEntityRecord:r,onNavigateToPreviousEntityRecord:s}=function(e,t,o){const[n,r]=(0,l.useReducer)(((e,{type:t,post:o,previousRenderingMode:n})=>"push"===t?[...e,{post:o,previousRenderingMode:n}]:"pop"===t&&e.length>1?e.slice(0,-1):e),[{post:{postId:e,postType:t}}]),{post:s,previousRenderingMode:i}=n[n.length-1],{getRenderingMode:c}=(0,a.useSelect)(D.store),{setRenderingMode:d}=(0,a.useDispatch)(D.store),m=(0,l.useCallback)((e=>{r({type:"push",post:{postId:e.postId,postType:e.postType},previousRenderingMode:c()}),d(o)}),[c,d,o]),p=(0,l.useCallback)((()=>{r({type:"pop"}),i&&d(i)}),[d,i]);return{currentPost:s,onNavigateToEntityRecord:m,onNavigateToPreviousEntityRecord:n.length>1?p:void 0}}(e,t,"post-only"),{post:c,template:d,isFullscreenEnabled:m}=(0,a.useSelect)((e=>{const{getEntityRecord:t}=e(X.store),{getEditedPostTemplate:o}=e(M),r=t("postType",n.postType,n.postId);return{template:"wp_template"!==n.postType?o():null,post:r,isFullscreenEnabled:e(M).isFeatureActive("fullscreenMode")}}),[n.postType,n.postId]),{isFullScreenForced:p,displaySendEmailButton:u}=o,{removeEditorPanel:_}=(0,a.useDispatch)(D.store);(0,l.useEffect)((()=>{_("post-status")}),[_]);const[g]=Ee(),h=(0,l.useMemo)((()=>({...o,onNavigateToEntityRecord:r,onNavigateToPreviousEntityRecord:s,defaultRenderingMode:"wp_template"===n.postType?"post-only":"template-locked",supportsTemplateMode:!0})),[o,r,s,n.postType]);return!c||"wp_template"!==n.postType&&!d?(0,i.jsx)("div",{className:"spinner-container",children:(0,i.jsx)(x.Spinner,{style:{width:"80px",height:"80px"}})}):(N("editor_layout_loaded"),(0,i.jsx)(x.SlotFillProvider,{children:(0,i.jsxs)(D.ErrorBoundary,{canCopyContent:!0,children:[(0,i.jsx)(xe.CommandMenu,{}),(0,i.jsxs)(U,{postId:n.postId,postType:n.postType,settings:h,templateId:d&&d.id,styles:g,children:[(0,i.jsx)(D.AutosaveMonitor,{}),(0,i.jsx)(D.LocalAutosaveMonitor,{}),(0,i.jsx)(D.UnsavedChangesWarning,{}),(0,i.jsx)(D.EditorKeyboardShortcutsRegister,{}),(0,i.jsx)(D.PostLockedModal,{}),(0,i.jsx)(Ge,{}),(0,i.jsx)(ft,{}),(0,i.jsx)(Et,{}),(0,i.jsx)(Z,{isActive:p||m}),(p||m)&&(0,i.jsx)(Kt,{}),!p&&(0,i.jsx)(Pt,{}),"wp_template"===n.postType?(0,i.jsx)(Rt,{}):(0,i.jsx)(It,{}),u&&(0,i.jsx)(Ot,{}),(0,i.jsx)(Gt,{}),(0,i.jsx)(Ut,{})]})]})}))}const Qt=window.wp.dataControls;function eo(e){return{type:"CHANGE_PREVIEW_STATE",state:{isModalOpened:e}}}function to(e){return{type:"CHANGE_PREVIEW_STATE",state:{toEmail:e}}}const oo=e=>async({registry:t})=>{const o=t.select(M).getEmailPostId(),n=t.select(M).getEmailPostType();t.dispatch(X.store).editEntityRecord("postType",n,o,{template:e})};function*no(e){if(!(0,a.select)(M).getPreviewState().isSendingPreviewEmail){yield{type:"CHANGE_PREVIEW_STATE",state:{sendingPreviewStatus:null,isSendingPreviewEmail:!0}};try{const t=(0,a.select)(M).getEmailPostId();yield(0,Qt.apiFetch)({path:"/woocommerce-email-editor/v1/send_preview_email",method:"POST",data:{email:e,postId:t}}),yield{type:"CHANGE_PREVIEW_STATE",state:{sendingPreviewStatus:St.SUCCESS,isSendingPreviewEmail:!1}},P("sent_preview_email",{postId:t,email:e})}catch(t){P("sent_preview_email_error",{email:e}),yield{type:"CHANGE_PREVIEW_STATE",state:{sendingPreviewStatus:St.ERROR,isSendingPreviewEmail:!1,errorMessage:JSON.stringify(t?.error)}}}}}function ro(e){return{type:"SET_IS_FETCHING_PERSONALIZATION_TAGS",state:{isFetching:e}}}function so(e){return{type:"SET_PERSONALIZATION_TAGS_LIST",state:{list:e}}}function io(){if(!window.WooCommerceEmailEditor)throw new Error("WooCommerceEmailEditor global object is not available. This is required for the email editor to work.");const{current_post_id:e,current_post_type:t}=window.WooCommerceEmailEditor;if(null==e)throw new Error("current_post_id is required but not provided.");if(!t)throw new Error("current_post_type is required but not provided.");return{postId:e,postType:t,editorSettings:window.WooCommerceEmailEditor.editor_settings,theme:window.WooCommerceEmailEditor.editor_theme,styles:{globalStylesPostId:window.WooCommerceEmailEditor.user_theme_post_id},urls:window.WooCommerceEmailEditor.urls,preview:{toEmail:window.WooCommerceEmailEditor.current_wp_user_email,isModalOpened:!1,isSendingPreviewEmail:!1,sendingPreviewStatus:null},personalizationTags:{list:[],isFetching:!1}}}function ao(e,t){switch(t.type){case"CHANGE_PREVIEW_STATE":return{...e,preview:{...e.preview,...t.state}};case"CHANGE_PERSONALIZATION_TAGS_STATE":case"SET_IS_FETCHING_PERSONALIZATION_TAGS":case"SET_PERSONALIZATION_TAGS_LIST":return{...e,personalizationTags:{...e.personalizationTags,...t.state}};case"SET_PERSONALIZATION_TAGS":return{...e,personalizationTags:{...e.personalizationTags,list:t.personalizationTags}};default:return e}}function lo(e){return e?.content&&"function"==typeof e.content?e.content(e):e?.blocks?(0,d.serialize)(e.blocks):e?.content?e.content:""}const co=new WeakMap;function mo(e){let t=co.get(e);return t||(t={...e,get blocks(){return(0,d.parse)(e.content)}},co.set(e,t)),t}function po(e){return e?{...e,title:e?.title?.raw||e?.title||"",content:e?.content?.raw||e?.content||""}:null}const uo=(0,a.createRegistrySelector)((e=>(t,o)=>!!e(Tt.store).get(M,o))),_o=(0,a.createRegistrySelector)((e=>()=>{const t=e(M).getEmailPostId(),o=e(M).getEmailPostType();return!!e(X.store).hasEditsForEntityRecord("postType",o,t)})),go=(0,a.createRegistrySelector)((e=>()=>{const t=e(M).getEmailPostId(),o=e(M).getEmailPostType(),n=e(X.store).getEntityRecord("postType",o,t);if(!n)return!0;const{content:r}=n;return!r.raw})),ho=(0,a.createRegistrySelector)((e=>()=>{const t=e(M).getEmailPostId(),o=e(M).getEmailPostType(),n=e(X.store).getEntityRecord("postType",o,t);return!!n&&"sent"===n.status})),yo=(0,a.createRegistrySelector)((e=>()=>{const t=e(M).getEmailPostId(),o=e(M).getEmailPostType(),n=e(X.store).getEditedEntityRecord("postType",o,t);return n?lo(n):""})),wo=(0,a.createRegistrySelector)((e=>()=>{const t=e(M).getEmailPostType();return e(X.store).getEntityRecords("postType",t,{per_page:30,status:"publish,sent"})?.filter((e=>""!==e?.content?.raw))||[]})),xo=(0,a.createRegistrySelector)((e=>(0,a.createSelector)((()=>e(X.store).getBlockPatterns().filter((({templateTypes:e})=>Array.isArray(e)&&e.includes("email-template"))).map(mo)),(()=>[e(X.store).getBlockPatterns()])))),fo=(0,a.createRegistrySelector)((e=>()=>e(X.store).canUser("create",{kind:"postType",name:"wp_template"})));function bo(e,t){return fo()?e(X.store).getEditedEntityRecord("postType","wp_template",t):po(e(X.store).getEntityRecord("postType","wp_template",t,{context:"view"}))}const vo=(0,a.createRegistrySelector)((e=>()=>{const t=e(D.store).getEditedPostAttribute("template");if(t){const o=e(X.store).getEntityRecords("postType","wp_template",{per_page:-1,context:"view"})?.find((e=>e.slug===t));return o?bo(e,o.id):po(o)}const o=e(X.store).getDefaultTemplateId({slug:"email-general"});return bo(e,o)})),jo=(0,a.createRegistrySelector)((e=>()=>{if("wp_template"===e(D.store).getCurrentPostType()){const t=e(D.store).getCurrentPostId();return e(X.store).getEditedEntityRecord("postType","wp_template",t)}return vo()})),ko=()=>{const e=jo();return e?lo(e):""},So=(0,a.createRegistrySelector)((e=>()=>{const t=e(M).getGlobalStylesPostId();return{postId:t,canEdit:e(X.store).canUser("update",{kind:"root",name:"globalStyles",id:t})}})),Co=(0,a.createRegistrySelector)((e=>()=>{const{postId:t,canEdit:o}=So();return t&&void 0!==o&&t?o?e(X.store).getEditedEntityRecord("postType","wp_global_styles",t):po(e(X.store).getEntityRecord("postType","wp_global_styles",t,{context:"view"})):null})),Eo=(0,a.createRegistrySelector)((e=>()=>{const t=e(M).getEmailPostType();return e(X.store).getEntityRecords("postType","wp_template",{per_page:-1,post_type:t,context:"view"})?.filter((e=>e.post_types.includes(t)))}));function To(e){return e.postId}function Po(e){return e.postType}function No(e){return e.editorSettings}function Bo(e){return e.editorSettings.__experimentalFeatures.color.palette}function Mo(e){return e.preview}function Fo(e){return e.personalizationTags}function Io(e){return e.personalizationTags.list}function zo(e){return e.theme.styles}function Ro(e){return e.theme}function Lo(e){return e.styles.globalStylesPostId}function Ao(e){return e.urls}function*Oo(){const e=yield(0,a.select)(M),t=e.personalizationTags?.isFetching;if(!t){yield ro(!0);try{const e=yield(0,Qt.apiFetch)({path:"/woocommerce-email-editor/v1/get_personalization_tags",method:"GET"});yield so(e.result)}finally{yield ro(!1)}}}const Ho=()=>{const e=(0,a.createReduxStore)(M,{actions:n,controls:Qt.controls,selectors:r,resolvers:s,reducer:ao,initialState:io()});return(0,a.register)(e),e},Vo=window.wp.mediaUtils,Do=()=>{(0,c.addFilter)("editor.MediaUpload","woocommerce/email-editor/replace-media-upload",(()=>Vo.MediaUpload))},Wo=()=>{const e={"You’ve tried to select a block that is part of a template that may be used elsewhere on your site. Would you like to edit the template?":{domain:"default",replacementText:(0,g.__)("You’ve tried to select a block that is part of a template that may be used in other emails. Would you like to edit the template?","woocommerce")}};(0,c.addFilter)("i18n.gettext","woocommerce/email-editor/override-text",((t,o,n)=>e[o]&&e[o].domain===(n||"default")?e[o].replacementText:t))},Go=e=>{(0,c.doAction)("woocommerce_email_editor_events",e.detail)},$o=()=>{C()&&T.addEventListener(E,Go)};window.addEventListener("unload",(function(){C()&&T.removeEventListener(E,Go)}));const Uo=(...e)=>{const t=(0,a.select)(D.store).isInserterOpened(),o=!!document.getElementsByClassName("block-editor-inserter__quick-inserter").length;let n="other_inserter";t?n="inserter_sidebar":o&&(n="quick_inserter");const r=e[0],s=e[5];!1===Array.isArray(r)&&"object"==typeof r&&P(`${n}_library_block_selected`,{blockName:r.name}),Array.isArray(r)&&s&&s.patternName&&P(`${n}_library_pattern_selected`,{patternName:s.patternName})},Zo={"core/editor":{autosave:"editor_content_auto_saved",setDeviceType:e=>{P(`header_preview_dropdown_${e.toLowerCase()}_selected`)},setRenderingMode:e=>{(0,a.select)(D.store).getRenderingMode()!==e&&document.querySelector(`[aria-label="${(0,g.__)("View options")}"]`)&&P("preview_dropdown_rendering_mode_changed",{renderingMode:e})}},"core/block-editor":{insertBlock:Uo,insertBlocks:Uo},"core/preferences":{set:(e,t,o)=>{if((0,a.select)(Tt.store).get(e,t)===o)return;const n={focusMode:"focus_mode_toggle",fullscreenMode:"full_screen_mode_toggle",distractionFree:"distraction_free_toggle",fixedToolbar:"fixed_toolbar_toggle"};n[t]&&P(n[t],{isEnabled:o})}},"core/commands":{open:"command_menu_opened",close:"command_menu_closed"}},qo={},Jo={},Yo=()=>{C()&&(0,a.use)((e=>({dispatch:t=>{const o="object"==typeof t?t.name:t,n=e.dispatch(o),r=Zo[o];if(!r)return n;qo[o]||(qo[o]={}),Jo[o]||(Jo[o]={});for(const[e,t]of Object.entries(r))Jo[o][e]||(Jo[o][e]=n[e],qo[o][e]=(...n)=>{try{"function"==typeof t?t(...n):"string"==typeof t&&P(t)}catch(e){console.error("Error tracking event",e)}return Jo[o][e](...n)}),n[e]=qo[o][e];return n}})))};let Ko=[];function Xo(e){Ko.forEach((t=>{const o=e.target?.matches?.(t.selector)?e.target:e.target?.closest?.(t.selector);o&&("function"==typeof t.track?t.track(o,e):P(t.track))}))}const Qo=window.wp.isShallowEqual;var en=o.n(Qo);function tn(e){const t=(0,l.useRef)(e);return en()(e,t.current)||(t.current=e),t.current}const on=[],nn=()=>{const{addValidationNotice:e,hasValidationNotice:t,removeValidationNotice:o}=Ht(),{editedContent:n,editedTemplateContent:r}=(0,a.useSelect)((e=>({editedContent:e(M).getEditedEmailContent(),editedTemplateContent:e(M).getCurrentTemplateContent()}))),s=tn(n),i=tn(r),d=(0,l.useCallback)((()=>((e,t,{addValidationNotice:o,hasValidationNotice:n,removeValidationNotice:r})=>{const s=(0,c.applyFilters)("woocommerce_email_editor_content_validation_rules",on);let i=!0;return s.forEach((({id:s,testContent:a,message:l,actions:c})=>{a(e+t)?(o(s,l,c),i=!1):n(s)&&r(s)})),i})(s,i,{addValidationNotice:e,hasValidationNotice:t,removeValidationNotice:o})),[s,i,e,o,t]);return(0,l.useEffect)((()=>((0,c.addFilter)("editor.preSavePost","woocommerce/email-editor/validate-content",(async e=>{if(!d())throw new Error;return e})),()=>{(0,c.removeFilter)("editor.preSavePost","woocommerce/email-editor/validate-content")})),[d]),(0,l.useEffect)((()=>{const e=(0,a.subscribe)((()=>{t()&&d()}),X.store);return()=>e()}),[t,d]),{isInvalid:t(),validateContent:d}};function rn(){const{postId:e,postType:t,settings:o}=(0,a.useSelect)((e=>({postId:e(M).getEmailPostId(),postType:e(M).getEmailPostType(),settings:e(M).getInitialEditorSettings()})),[]);return nn(),o.allowedBlockTypes=function(){try{return(0,d.getBlockTypes)().filter((e=>!0===e.supports?.email)).map((e=>e.name))}catch(e){return console.error("Failed to get allowed block names:",e),[]}}(),(0,i.jsx)(l.StrictMode,{children:(0,i.jsx)(Xt,{postId:e,postType:t,settings:o})})}function sn(e){const t=document.getElementById(e);if(!t)return;const o=(0,c.applyFilters)("woocommerce_email_editor_wrap_editor_component",rn);$o(),Yo(),C()&&(Ko=[{track:"header_preview_dropdown_preview_in_new_tab_selected",selector:".editor-preview-dropdown__button-external"},{track:()=>{const e=document.getElementsByClassName("is-collapsed editor-collapsible-block-toolbar").length;P("header_blocks_tool_button_clicked",{isBlockToolsCollapsed:e})},selector:".editor-collapsible-block-toolbar__toggle"},{track:e=>{const t=e.classList.contains("is-opened");P("header_more_menu_dropdown_toggle",{isOpened:t})},selector:`.components-dropdown-menu__toggle[aria-label="${(0,g.__)("Options")}"]`},{track:e=>{(e.textContent===(0,g.__)("Save")&&"false"===e.getAttribute("aria-disabled")||e.textContent===(0,g.__)("Saving…"))&&P("header_save_button_clicked")},selector:".editor-post-publish-button"},{track:"header_save_email_button_clicked",selector:".editor-post-saved-state.is-saving"},{track:"inserter_sidebar_library_close_icon_clicked",selector:".block-editor-inserter__menu .block-editor-tabbed-sidebar__close-button"},{track:e=>{const t=e.classList.contains("is-opened");P("header_preview_dropdown_clicked",{isOpened:t})},selector:".editor-preview-dropdown__toggle"},{track:()=>{P("sidebar_tab_selected",{tab:"document"})},selector:'[data-tab-id="edit-post/document"]'},{track:()=>{P("sidebar_tab_selected",{tab:"block"})},selector:'[data-tab-id="edit-post/block"]'},{track:e=>{const t=e.classList.contains("is-pressed");P("header_inserter_sidebar_clicked",{isOpened:t})},selector:".editor-document-tools__inserter-toggle"},{track:e=>{const t=e.classList.contains("is-pressed");P("header_listview_sidebar_clicked",{isOpened:t})},selector:".editor-document-tools__document-overview-toggle"},{track:e=>{P("command_bar_command_clicked",{command:e.dataset?.value})},selector:'.commands-command-menu__container [role="option"]'}],document.addEventListener("click",Xo)),Ho(),(0,c.addFilter)("blocks.registerBlockType","woocommerce-email-editor/layout/addAttribute",ge),(0,c.addFilter)("editor.BlockListBlock","woocommerce-email-editor/with-layout-styles",we),(0,c.addFilter)("editor.BlockEdit","woocommerce-email-editor/with-inspector-controls",he),ie(),Do(),Wo(),(0,l.createRoot)(t).render((0,i.jsx)(o,{}))}function an(e){"loading"===document.readyState?window.addEventListener("DOMContentLoaded",(()=>{sn(e)}),{once:!0}):sn(e)}},76597:e=>{var t=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var t=Object.prototype.toString.call(e);return"[object RegExp]"===t||"[object Date]"===t||function(e){return e.$$typeof===o}(e)}(e)},o="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(e,t){return!1!==t.clone&&t.isMergeableObject(e)?a((o=e,Array.isArray(o)?[]:{}),e,t):e;var o}function r(e,t,o){return e.concat(t).map((function(e){return n(e,o)}))}function s(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return Object.propertyIsEnumerable.call(e,t)})):[]}(e))}function i(e,t){try{return t in e}catch(e){return!1}}function a(e,o,l){(l=l||{}).arrayMerge=l.arrayMerge||r,l.isMergeableObject=l.isMergeableObject||t,l.cloneUnlessOtherwiseSpecified=n;var c=Array.isArray(o);return c===Array.isArray(e)?c?l.arrayMerge(e,o,l):function(e,t,o){var r={};return o.isMergeableObject(e)&&s(e).forEach((function(t){r[t]=n(e[t],o)})),s(t).forEach((function(s){(function(e,t){return i(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,s)||(i(e,s)&&o.isMergeableObject(t[s])?r[s]=function(e,t){if(!t.customMerge)return a;var o=t.customMerge(e);return"function"==typeof o?o:a}(s,o)(e[s],t[s],o):r[s]=n(t[s],o))})),r}(e,o,l):n(o,l)}a.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,o){return a(e,o,t)}),{})};var l=a;e.exports=l},94931:(e,t,o)=>{var n=o(51609),r=Symbol.for("react.element"),s=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,a=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(e,t,o){var n,s={},c=null,d=null;for(n in void 0!==o&&(c=""+o),void 0!==t.key&&(c=""+t.key),void 0!==t.ref&&(d=t.ref),t)i.call(t,n)&&!l.hasOwnProperty(n)&&(s[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===s[n]&&(s[n]=t[n]);return{$$typeof:r,type:e,key:c,ref:d,props:s,_owner:a.current}}t.Fragment=s,t.jsx=c,t.jsxs=c},39793:(e,t,o)=>{e.exports=o(94931)},51609:e=>{e.exports=window.React},74997:e=>{e.exports=window.wp.blocks},56427:e=>{e.exports=window.wp.components},3582:e=>{e.exports=window.wp.coreData},47143:e=>{e.exports=window.wp.data},43656:e=>{e.exports=window.wp.editor},86087:e=>{e.exports=window.wp.element},52619:e=>{e.exports=window.wp.hooks},27723:e=>{e.exports=window.wp.i18n},5573:e=>{e.exports=window.wp.primitives},4921:(e,t,o)=>{function n(e){var t,o,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(t=0;t<s;t++)e[t]&&(o=n(e[t]))&&(r&&(r+=" "),r+=o)}else for(o in e)e[o]&&(r&&(r+=" "),r+=o);return r}o.d(t,{A:()=>r});const r=function(){for(var e,t,o=0,r="",s=arguments.length;o<s;o++)(e=arguments[o])&&(t=n(e))&&(r&&(r+=" "),r+=t);return r}}},t={};function o(n){var r=t[n];if(void 0!==r)return r.exports;var s=t[n]={exports:{}};return e[n](s,s.exports,o),s.exports}o.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return o.d(t,{a:t}),t},o.d=(e,t)=>{for(var n in t)o.o(t,n)&&!o.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),o.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var n=o(52619),r=o(74997),s=o(27723),i=o(47893),a=o(86087),l=o(47143),c=o(56427),d=o(43656),m=o(39793);function p(){return(0,m.jsx)("div",{style:{margin:"20vh auto",maxWidth:400,padding:20,backgroundColor:"#fff",borderRadius:4,boxShadow:"0 0 10px 0 rgba(0, 0, 0, 0.1)",textAlign:"center",color:"#000"},children:(0,m.jsxs)(c.__experimentalText,{children:[" ",(0,s.__)("Autogenerated content","woocommerce")," "]})})}const u=(e,t)=>{e?.current?.contentWindow?.document.body&&(e.current.contentWindow.document.body.style.overflow="hidden",e.current.contentWindow.document.body.style.pointerEvents="none",e.current.contentWindow.document.body.style.backgroundColor=t?"#00000059":e.current.contentWindow?.document?.bgColor)};function _(){const{postSlug:e}=(0,l.useSelect)((e=>({postSlug:e(d.store).getCurrentPost()?.slug})),[]),t=(0,a.useRef)(null),[o,n]=(0,a.useState)(!1),r=window.WooCommerceEmailEditor?.block_preview_url;return(0,a.useEffect)((()=>{if(!e)return;const o=(n=e||"",window.WooCommerceEmailEditor.email_types.find((e=>e.value===n))?.id);var n;o&&t.current&&((e,t)=>{e?.current?.contentWindow?.location.replace(t)})(t,`${r}&type=${o}`)}),[e,t,r]),(0,m.jsxs)("div",{style:{position:"relative"},children:[(0,m.jsx)("iframe",{style:{width:"100%",height:t?.current?.contentWindow?.document?.body?.clientHeight||"750px",backgroundColor:"initial",minHeight:"100px"},ref:t,src:`${r}&type=WC_Email_Customer_Processing_Order`,title:(0,s.__)("Email preview frame","woocommerce"),onMouseEnter:()=>{n(!0),u(t,!0)},onMouseLeave:()=>{n(!1),u(t,!1)}}),o&&(0,m.jsx)("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",zIndex:1e3,pointerEvents:"none"},children:(0,m.jsx)(p,{})})]})}const g={title:(0,s.__)("Woo Email Content","woocommerce"),category:"text",attributes:{},edit:function(){return(0,m.jsx)(_,{})},save:function(){return(0,m.jsx)("div",{children:"##WOO_CONTENT##"})}},h="woocommerce/email-editor-integration";var y=o(3582);function w({debouncedRecordEvent:e}){const[t,o]=(0,y.useEntityProp)("postType","wp_template","woocommerce_data"),n=(0,a.useRef)(null),r=(0,a.useCallback)((n=>{o({...t,sender_settings:{...t?.sender_settings,from_name:n}}),e("email_from_name_input_updated",{value:n})}),[t,o]),i=(0,a.useCallback)((r=>{o({...t,sender_settings:{...t?.sender_settings,from_address:r}}),n.current&&(n.current.checkValidity(),n.current.reportValidity()),e("email_from_address_input_updated",{value:r})}),[t,o]);return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("h2",{children:(0,s.__)("Sender Options","woocommerce")}),(0,m.jsx)(c.PanelRow,{children:(0,m.jsx)("p",{children:(0,s.__)("This is how your sender name and email address would appear in outgoing WooCommerce emails.","woocommerce")})}),(0,m.jsx)(c.PanelRow,{children:(0,m.jsx)(c.TextControl,{className:"woocommerce-email-sidebar-template-settings-sender-options-input",label:(0,s.__)("“from” name","woocommerce"),name:"from_name",type:"text",value:t?.sender_settings?.from_name||"",onChange:r})}),(0,m.jsx)(c.PanelRow,{children:(0,m.jsx)(c.TextControl,{ref:n,className:"woocommerce-email-sidebar-template-settings-sender-options-input",label:(0,s.__)("“from” email","woocommerce"),name:"from_email",type:"email",value:t?.sender_settings?.from_address||"",onChange:i,required:!0})})]})}var x=o(4921),f=o(5573);const b=(0,m.jsx)(f.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,m.jsx)(f.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})}),v=(0,m.jsx)(f.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,m.jsx)(f.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 18.5a6.5 6.5 0 1 1 0-13 6.5 6.5 0 0 1 0 13ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm11.53-1.47-1.06-1.06L11 12.94l-1.47-1.47-1.06 1.06L11 15.06l4.53-4.53Z"})}),j=(0,m.jsx)(f.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,m.jsx)(f.Path,{d:"M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21ZM15.5303 8.46967C15.8232 8.76256 15.8232 9.23744 15.5303 9.53033L13.0607 12L15.5303 14.4697C15.8232 14.7626 15.8232 15.2374 15.5303 15.5303C15.2374 15.8232 14.7626 15.8232 14.4697 15.5303L12 13.0607L9.53033 15.5303C9.23744 15.8232 8.76256 15.8232 8.46967 15.5303C8.17678 15.2374 8.17678 14.7626 8.46967 14.4697L10.9393 12L8.46967 9.53033C8.17678 9.23744 8.17678 8.76256 8.46967 8.46967C8.76256 8.17678 9.23744 8.17678 9.53033 8.46967L12 10.9393L14.4697 8.46967C14.7626 8.17678 15.2374 8.17678 15.5303 8.46967Z"})}),k=(0,m.jsx)(f.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,m.jsx)(f.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 18.5a6.5 6.5 0 1 1 0-13 6.5 6.5 0 0 1 0 13ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm9 1V8h-1.5v3.5h-2V13H13Z"})}),S=[{value:"enabled",label:(0,s.__)("Enabled","woocommerce"),icon:v,description:(0,s.__)("Email would be sent if trigger is met","woocommerce")},{value:"disabled",label:(0,s.__)("Inactive","woocommerce"),icon:j,description:(0,s.__)("Email would not be sent","woocommerce")},{value:"manual",label:(0,s.__)("Manually sent","woocommerce"),icon:k,description:(0,s.__)("Email can only be sent manually from the order screen","woocommerce")}];function C({className:e,recordEvent:t}){var o;const[n]=(0,y.useEntityProp)("postType","woo_email","woocommerce_data"),r=n?.is_manual;let i="enabled";r?i="manual":n?.enabled||(i="disabled");const a=null!==(o=S.find((e=>e.value===i)))&&void 0!==o?o:S[1];return(0,m.jsx)(c.PanelRow,{className:e,children:(0,m.jsxs)(c.Flex,{justify:"start",children:[(0,m.jsx)(c.FlexItem,{className:"editor-post-panel__row-label",children:(0,s.__)("Email Status","woocommerce")}),(0,m.jsx)(c.FlexItem,{children:(0,m.jsx)(c.Dropdown,{popoverProps:{placement:"bottom-start",offset:0,shift:!0},renderToggle:({isOpen:e,onToggle:t})=>(0,m.jsx)(c.Button,{variant:"tertiary",className:"editor-post-status__toggle",icon:a.icon,size:"compact",onClick:t,"aria-label":(0,s.sprintf)((0,s.__)("Change status: %s","woocommerce"),a.label),"aria-expanded":e,disabled:r,children:a.label}),renderContent:({onClose:e})=>(0,m.jsxs)("div",{style:{minWidth:230},children:[(0,m.jsxs)(c.Flex,{justify:"space-between",align:"center",style:{padding:"8px 0"},children:[(0,m.jsx)("h2",{className:"block-editor-inspector-popover-header__heading",style:{margin:0},children:(0,s.__)("Status","woocommerce")}),(0,m.jsx)(c.Button,{size:"small",className:"block-editor-inspector-popover-header__action",label:(0,s.__)("Close","woocommerce"),icon:b,onClick:e})]}),(0,m.jsx)(c.RadioControl,{selected:i,options:S.filter((e=>"manual"!==e.value)).map((e=>({label:e.label,value:e.value,description:e.description}))),onChange:o=>{(e=>{const o=(0,l.select)(y.store).getEditedEntityRecord("postType","woo_email",window.WooCommerceEmailEditor.current_post_id),n=o?.woocommerce_data||{};(0,l.dispatch)(y.store).editEntityRecord("postType","woo_email",window.WooCommerceEmailEditor.current_post_id,{woocommerce_data:{...n,enabled:e}}),t("email_status_changed",{status:e?"active":"inactive"})})("enabled"===o),e()},disabled:r})]})})})]})})}const E=({RichTextWithButton:e,recordEvent:t,debouncedRecordEvent:o})=>{var n;const[r]=(0,y.useEntityProp)("postType","woo_email","woocommerce_data"),[i,d]=(0,a.useState)(!!r?.bcc),[p,u]=(0,a.useState)(!!r?.cc);if(!r)return null;const _=(e,t)=>{const o=(0,l.select)(y.store).getEditedEntityRecord("postType","woo_email",window.WooCommerceEmailEditor.current_post_id),n=o?.woocommerce_data||{};(0,l.dispatch)(y.store).editEntityRecord("postType","woo_email",window.WooCommerceEmailEditor.current_post_id,{woocommerce_data:{...n,[e]:t}})},g=null!==(n=r?.preheader?.length)&&void 0!==n?n:0;return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)("br",{}),"customer_refunded_order"===r.email_type?(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(e,{attributeName:"subject_full",attributeValue:r.subject_full,updateProperty:_,label:(0,s.__)("Full Refund Subject","woocommerce"),placeholder:r.default_subject}),(0,m.jsx)("br",{}),(0,m.jsx)(e,{attributeName:"subject_partial",attributeValue:r.subject_partial,updateProperty:_,label:(0,s.__)("Partial Refund Subject","woocommerce"),placeholder:r.default_subject})]}):(0,m.jsx)(e,{attributeName:"subject",attributeValue:r.subject,updateProperty:_,label:(0,s.__)("Subject","woocommerce"),placeholder:r.default_subject}),(0,m.jsx)("br",{}),(0,m.jsx)(e,{attributeName:"preheader",attributeValue:r.preheader,updateProperty:_,label:(0,s.__)("Preview text","woocommerce"),help:(0,m.jsxs)("span",{className:(0,x.A)("woocommerce-settings-panel__preview-text-length",{"woocommerce-settings-panel__preview-text-length-warning":g>80,"woocommerce-settings-panel__preview-text-length-error":g>150}),children:[g,"/",150]}),placeholder:(0,s.__)("Shown as a preview in the inbox, next to the subject line.","woocommerce")}),(0,m.jsx)(c.PanelRow,{children:(0,m.jsx)(c.BaseControl,{__nextHasNoMarginBottom:!0,label:(0,s.__)("Recipients","woocommerce"),id:"woocommerce-email-editor-recipients",children:null===r.recipient?(0,m.jsx)("p",{className:"woocommerce-email-editor-recipients-help",children:(0,s.__)("This email is sent to Customer.","woocommerce")}):(0,m.jsx)(c.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,name:"recipient","data-testid":"email_recipient",value:r.recipient,onChange:e=>{_("recipient",e)},help:(0,s.__)("Separate with commas to add multiple email addresses.","woocommerce")})})}),(0,m.jsx)(c.PanelRow,{children:(0,m.jsx)(c.BaseControl,{__nextHasNoMarginBottom:!0,children:(0,m.jsx)(c.ToggleControl,{__nextHasNoMarginBottom:!0,name:"add_cc",checked:p,label:(0,s.__)("Add CC","woocommerce"),onChange:e=>{u(e),e||_("cc",""),t("email_cc_toggle_clicked",{isEnabled:e})}})})}),p&&(0,m.jsx)(c.PanelRow,{children:(0,m.jsx)(c.BaseControl,{__nextHasNoMarginBottom:!0,children:(0,m.jsx)(c.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,"data-testid":"email_cc",value:r?.cc||"",onChange:e=>{_("cc",e),o("email_cc_input_updated",{value:e})},help:(0,s.__)("Add recipients who will receive a copy of the email. Separate multiple addresses with commas.","woocommerce")})})}),(0,m.jsx)(c.PanelRow,{children:(0,m.jsx)(c.BaseControl,{__nextHasNoMarginBottom:!0,children:(0,m.jsx)(c.ToggleControl,{__nextHasNoMarginBottom:!0,name:"add_bcc",checked:i,label:(0,s.__)("Add BCC","woocommerce"),onChange:e=>{d(e),e||_("bcc",""),t("email_bcc_toggle_clicked",{isEnabled:e})}})})}),i&&(0,m.jsx)(c.PanelRow,{children:(0,m.jsx)(c.BaseControl,{__nextHasNoMarginBottom:!0,children:(0,m.jsx)(c.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,"data-testid":"email_bcc",value:r?.bcc||"",onChange:e=>{_("bcc",e),o("email_bcc_input_updated",{value:e})},help:(0,s.__)("Add recipients who will receive a hidden copy of the email. Separate multiple addresses with commas.","woocommerce")})})})]})};function T(){return(0,l.select)("core").getEditedEntityRecord("postType",window.WooCommerceEmailEditor.current_post_type,window.WooCommerceEmailEditor.current_post_id)?.woocommerce_data}function P(e){const t=document.createElement("input");return t.type="email",t.value=e,t.checkValidity()}function N(e){return e.split(",").filter((e=>!!e.trim()&&!P(e.trim())))}function B(e,t){return{id:`${e}-email-validation`,testContent:()=>{const t=T();return!(!(e in t)||!t[e])&&N(t[e]).length>0},get message(){var o;const n=N(null!==(o=T()[e])&&void 0!==o?o:"");return(0,s.sprintf)(t,n.join(","))},actions:[]}}const M={id:"sender-email-validation",testContent:()=>{var e;const t=T(),o=null!==(e=t?.sender_settings?.from_address)&&void 0!==e?e:"";return!!o.trim()&&!P(o.trim())},message:(0,s.__)('The "from" email address is invalid. Please enter a valid email address that will appear as the sender in outgoing WooCommerce emails.',"woocommerce"),actions:[]},F=B("recipient",(0,s.__)("One or more Recipient email addresses are invalid: “%s”. Please enter valid email addresses separated by commas.","woocommerce")),I=B("cc",(0,s.__)("One or more CC email addresses are invalid: “%s”. Please enter valid email addresses separated by commas.","woocommerce")),z=B("bcc",(0,s.__)("One or more BCC email addresses are invalid: “%s”. Please enter valid email addresses separated by commas.","woocommerce"));(0,n.addFilter)("woocommerce_email_editor_send_button_label",h,(()=>(0,s.__)("Save email","woocommerce"))),(0,n.addFilter)("woocommerce_email_editor_check_sending_method_configuration_link",h,(()=>"https://woocommerce.com/document/email-faq/")),(0,n.addFilter)("woocommerce_email_editor_trash_modal_should_permanently_delete",h,(()=>!0)),(0,r.registerBlockType)("woo/email-content",g),(0,n.addFilter)("woocommerce_email_editor_setting_sidebar_email_status_component",h,((e,t)=>()=>(0,m.jsx)(C,{recordEvent:t.recordEvent}))),(0,n.addFilter)("woocommerce_email_editor_setting_sidebar_extension_component",h,((e,t)=>()=>(0,m.jsx)(E,{RichTextWithButton:e,recordEvent:t.recordEvent,debouncedRecordEvent:t.debouncedRecordEvent}))),(0,n.addFilter)("woocommerce_email_editor_template_sections","my-plugin/template-settings",((e,t)=>[...e,{id:"my-custom-section",render:()=>(0,m.jsx)(w,{debouncedRecordEvent:t.debouncedRecordEvent})}])),(0,n.addFilter)("woocommerce_email_editor_content_validation_rules",h,(e=>[...e||[],M,F,I,z])),(0,i.initializeEditor)("woocommerce-email-editor"),(window.wc=window.wc||{}).emailEditorIntegration={}})();