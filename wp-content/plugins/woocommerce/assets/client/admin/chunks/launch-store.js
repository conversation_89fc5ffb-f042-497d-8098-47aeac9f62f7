"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[3678],{5751:(e,t,o)=>{o.d(t,{A:()=>n});var s=o(39793);const n=()=>(0,s.jsxs)("svg",{width:"91",height:"24",viewBox:"0 0 91 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"wc-icon wc-icon__woo-logo new-branding",children:[(0,s.jsx)("path",{d:"M79.0537 0C72.2755 0 67.0874 5.10851 67.0874 12C67.0874 18.8915 72.2755 24 79.0537 24C85.832 24 91.0002 18.8915 91.0002 12C91.0002 5.10851 85.7923 0 79.0537 0ZM79.0537 16.6277C76.5094 16.6277 74.7602 14.6644 74.7602 12C74.7602 9.33555 76.4895 7.37228 79.0537 7.37228C81.6179 7.37228 83.3473 9.33555 83.3473 12C83.3473 14.6644 81.5981 16.6277 79.0537 16.6277Z",fill:"#873DFF"}),(0,s.jsx)("path",{d:"M53.7285 0C46.9503 0 41.7622 5.10851 41.7622 12C41.7622 18.8915 46.9701 24 53.7285 24C60.4869 24 65.675 18.8915 65.675 12C65.675 5.10851 60.4671 0 53.7285 0ZM53.7285 16.6277C51.1842 16.6277 49.435 14.6644 49.435 12C49.435 9.33555 51.1643 7.37228 53.7285 7.37228C56.2928 7.37228 58.0221 9.33555 58.0221 12C58.0221 14.6644 56.2928 16.6277 53.7285 16.6277Z",fill:"#873DFF"}),(0,s.jsx)("path",{d:"M11.688 24C14.3715 24 16.5183 22.6577 18.1483 19.5726L21.7461 12.7813V18.5509C21.7461 21.9365 23.9327 24 27.3317 24C29.9556 24 31.8837 22.798 33.792 19.5726L42.1207 5.44908C43.9494 2.36394 42.6574 0 38.6421 0C36.4953 0 35.1039 0.721201 33.8516 3.08514L28.107 13.9232V4.28714C28.107 1.40234 26.7553 0 24.2308 0C22.2629 0 20.6926 0.861435 19.4602 3.26544L14.0535 13.9032V4.38731C14.0535 1.30217 12.8012 0 9.74004 0H3.53822C1.19266 0 0 1.10184 0 3.14524C0 5.18864 1.23241 6.33054 3.53822 6.33054H6.08255V18.5309C6.10243 21.9365 8.3486 24 11.688 24Z",fill:"#873DFF"})]})},66637:(e,t,o)=>{o.r(t),o.d(t,{default:()=>Ae});var s=o(84437),n=o(51609),a=o.n(n),r=o(47143),c=o(40314),i=o(4921),l=o(24060),m=o(51513),d=o(53187),u=o(97233),_=o(71529),p=o(4316),h=o(96476),y=o(83306),g=o(1455),w=o.n(g),v=o(86087),S=o(27723),C=o(59783),x=o(56427),b=o(45260),k=o(46445),P=o(61288),T=o(39793);const f=({title:e,description:t,footer:o,children:s})=>{const n=(0,S.isRTL)()?b.A:k.A;return(0,T.jsxs)(T.Fragment,{children:[(0,T.jsxs)(x.__experimentalVStack,{className:(0,i.A)("woocommerce-edit-site-sidebar-navigation-screen__main",{"has-footer":!!o}),spacing:0,justify:"flex-start",children:[(0,T.jsxs)(x.__experimentalHStack,{spacing:4,alignment:"flex-start",className:"woocommerce-edit-site-sidebar-navigation-screen__title-icon",children:[(0,T.jsx)(P.A,{onClick:(r=e,a().isValidElement(r)&&"function"==typeof r.props.onClick?e.props.onClick:void 0),icon:n,label:(0,S.__)("Back","woocommerce"),showTooltip:!1}),(0,T.jsx)(x.__experimentalHeading,{className:"woocommerce-edit-site-sidebar-navigation-screen__title",level:1,as:"h1",children:e})]}),(0,T.jsxs)("div",{className:"woocommerce-edit-site-sidebar-navigation-screen__content",children:[t&&(0,T.jsx)("p",{className:"woocommerce-edit-site-sidebar-navigation-screen__description",children:t}),s]})]}),o&&(0,T.jsx)("footer",{className:"woocommerce-edit-site-sidebar-navigation-screen__footer",children:o})]});var r};var j=o(72744),E=o(20273),L=o(24652),N=o(44412),A=o(35166),O=o(33484),R=o(5573);const W=(0,T.jsx)(R.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,T.jsx)(R.Path,{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M12 18.5C10.2761 18.5 8.62279 17.8152 7.40381 16.5962C6.18482 15.3772 5.5 13.7239 5.5 12C5.5 10.2761 6.18482 8.62279 7.40381 7.40381C8.62279 6.18482 10.2761 5.5 12 5.5C13.7239 5.5 15.3772 6.18482 16.5962 7.40381C17.8152 8.62279 18.5 10.2761 18.5 12C18.5 13.7239 17.8152 15.3772 16.5962 16.5962C15.3772 17.8152 13.7239 18.5 12 18.5ZM4 12C4 9.87827 4.84285 7.84344 6.34315 6.34315C7.84344 4.84285 9.87827 4 12 4C14.1217 4 16.1566 4.84285 17.6569 6.34315C19.1571 7.84344 20 9.87827 20 12C20 14.1217 19.1571 16.1566 17.6569 17.6569C16.1566 19.1571 14.1217 20 12 20C9.87827 20 7.84344 19.1571 6.34315 17.6569C4.84285 16.1566 4 14.1217 4 12ZM15.53 10.53L14.47 9.47L11 12.94L9.53 11.47L8.47 12.53L11 15.06L15.53 10.53Z",fill:"#00A32A"})}),M=(0,T.jsx)(R.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,T.jsx)(R.Path,{d:"M5 17.7C5.4 18.2 5.8 18.6 6.2 18.9L7.3 17.5C6.9 17.2 6.6 16.9 6.3 16.5L5 17.7ZM5 6.3L6.4 7.4C6.7 7 7 6.7 7.4 6.4L6.3 5C5.8 5.4 5.4 5.8 5 6.3ZM5.1 14.1L3.4 14.6C3.6 15.2 3.8 15.7 4.1 16.2L5.6 15.4C5.4 15 5.2 14.6 5.1 14.1ZM4.8 12V11.3L3 11.1V12.9L4.7 12.7C4.8 12.5 4.8 12.2 4.8 12ZM7.8 19.9C8.3 20.2 8.9 20.4 9.4 20.6L9.9 18.9C9.4 18.8 9 18.6 8.6 18.4L7.8 19.9ZM19 6.3C18.6 5.8 18.2 5.4 17.8 5.1L16.7 6.5C17.1 6.8 17.4 7.1 17.7 7.5L19 6.3ZM18.9 9.9L20.6 9.4C20.4 8.8 20.2 8.3 19.9 7.8L18.4 8.6C18.6 9 18.8 9.4 18.9 9.9ZM5.6 8.6L4.1 7.8C3.8 8.3 3.6 8.8 3.4 9.4L5.1 9.9C5.2 9.4 5.4 9 5.6 8.6ZM7.8 4.1L8.6 5.6C9 5.4 9.4 5.2 9.9 5.1L9.4 3.4C8.8 3.6 8.3 3.8 7.8 4.1ZM16.6 17.6L17.7 19C18.2 18.6 18.6 18.2 18.9 17.8L17.5 16.7C17.3 17 17 17.3 16.6 17.6ZM18.4 15.4L19.9 16.2C20.2 15.7 20.4 15.1 20.6 14.6L18.9 14.1C18.8 14.6 18.6 15 18.4 15.4ZM21 11.1L19.3 11.3V12.7L21 12.9V12V11.1ZM11.1 3L11.3 4.7H12.7L12.9 3H11.1ZM14.1 5.1C14.6 5.2 15 5.4 15.4 5.6L16.2 4.1C15.7 3.8 15.1 3.6 14.6 3.4L14.1 5.1ZM12 19.2H11.3L11.1 21H12.9L12.7 19.3C12.5 19.2 12.2 19.2 12 19.2ZM14.1 18.9L14.6 20.6C15.2 20.4 15.7 20.2 16.2 19.9L15.4 18.4C15 18.6 14.6 18.8 14.1 18.9Z",fill:"#949494"})}),H=j.A,D={tax:E.A,shipping:L.A,"customize-store":N.A,payments:A.A,"woocommerce-payments":A.A,products:O.A,activePaymentStep:M,completedPaymentStep:W};var I=o(64155),U=o(52619),B=o(85816),F=o(15703),Y=o(1069),K=o(22861);const{getWithExpiry:Z,setWithExpiry:$}=(0,B.createStorageUtils)("lys_recently_actioned_tasks",604800),V=({task:e,classNames:t})=>(0,T.jsx)(C.A,{className:(0,i.A)(e.id,"is-complete",t),icon:H,disabled:!0,children:e.title}),Q=({task:e,classNames:t,onClick:o})=>(0,T.jsx)(C.A,{className:(0,i.A)(e.id,t),icon:D[e.id],withChevron:!0,onClick:o,children:e.title}),G=e=>{const{context:{tasklist:t,removeTestOrders:o,testOrderCount:s,launchStoreError:n}}=e,a=(0,T.jsx)(x.Button,{onClick:()=>{e.sendEventToSidebar({type:"POP_BROWSER_STACK"})},children:(0,S.__)("Launch Your Store","woocommerce")}),r=(0,S.__)("Ready to start selling? Before you launch your store, make sure you’ve completed these essential tasks. If you’d like to change your store visibility, go to WooCommerce | Settings | Site visibility.","woocommerce"),c=t&&!t.tasks.every((e=>e.isComplete)),[l,m]=(0,v.useState)(null==o||o),[d,u]=(0,v.useState)(!1),[_,p]=(0,v.useState)(!1),h=()=>{p(!0),e.sendEventToSidebar({type:"LAUNCH_STORE",removeTestOrders:l})};return(0,v.useEffect)((()=>{n?.message&&p(!1)}),[n?.message]),(0,T.jsxs)("div",{className:(0,i.A)("launch-store-sidebar__container",e.className),children:[(0,T.jsx)(x.__unstableMotion.div,{className:"woocommerce-edit-site-layout__header-container",animate:"view",children:(0,T.jsx)(I.b,{variants:{view:{x:0}},isTransparent:!1,className:"woocommerce-edit-site-layout__hub"})}),(0,T.jsxs)(f,{title:a,description:r,children:[(0,T.jsx)("div",{className:"woocommerce-edit-site-sidebar-navigation-screen-essential-tasks__group-header",children:(0,T.jsx)(x.__experimentalHeading,{level:2,children:(0,S.__)("Essential Tasks","woocommerce")})}),(0,T.jsxs)(x.__experimentalItemGroup,{className:"woocommerce-edit-site-sidebar-navigation-screen-essential-tasks__group",children:[t&&c&&t.tasks.map((t=>t.isComplete?(0,T.jsx)(V,{task:t},t.id):(0,T.jsx)(Q,{task:t,onClick:()=>{e.sendEventToSidebar({type:"TASK_CLICKED",task:t})}},t.id))),t&&!c&&(0,T.jsx)(C.A,{className:"all-tasks-complete",icon:H,children:(0,S.__)("Fantastic job! Your store is ready to go — no pending tasks to complete.","woocommerce")})]}),s>0&&(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)("div",{className:"woocommerce-edit-site-sidebar-navigation-screen-test-data__group-header",children:(0,T.jsx)(x.__experimentalHeading,{level:2,children:(0,S.__)("Test data","woocommerce")})}),(0,T.jsxs)(x.__experimentalItemGroup,{className:"woocommerce-edit-site-sidebar-navigation-screen-remove-test-data__group",children:[(0,T.jsx)(x.ToggleControl,{__nextHasNoMarginBottom:!0,label:(0,S.sprintf)((0,S.__)("Remove %d test orders","woocommerce"),s),checked:l,onChange:m}),(0,T.jsx)("p",{children:(0,S.__)("Remove test orders and associated data, including analytics and transactions, once your store goes live. ","woocommerce")})]})]}),(0,T.jsxs)(x.__experimentalItemGroup,{className:"woocommerce-edit-site-sidebar-navigation-screen-launch-store-button__group",children:[n?.message&&!d&&(0,T.jsx)(x.Notice,{className:"launch-store-error-notice",isDismissible:!0,onRemove:()=>u(!0),status:"error",children:(0,v.createInterpolateElement)((0,S.__)("Oops! We encountered a problem while launching your store. <retryButton/>","woocommerce"),{retryButton:(0,T.jsx)(x.Button,{onClick:h,variant:"tertiary",children:(0,S.__)("Please try again","woocommerce")})})}),(0,T.jsx)(x.Button,{variant:"primary",onClick:h,children:_?(0,T.jsx)(x.Spinner,{}):(0,S.__)("Launch your store","woocommerce")})]})]})]})};var z=o(99096);const X=({rows:e})=>{const t=Array.from({length:e}).map(((e,t)=>(0,T.jsxs)(x.__unstableMotion.div,{className:"step-placeholder__item payment-step payment-step--disabled",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.1*t,ease:"easeOut"},children:[(0,T.jsx)("div",{className:"step-placeholder__icon step-placeholder__shimmer"}),(0,T.jsx)("div",{className:"step-placeholder__content",children:(0,T.jsx)("div",{className:"step-placeholder__text step-placeholder__shimmer"})})]},t)));return(0,T.jsx)(x.__unstableMotion.div,{className:"step-placeholder",initial:{opacity:0},animate:{opacity:1},transition:{duration:.2},children:t})};var J=o(75854);const q=(0,v.createContext)({isWooPaymentsActive:!1,isWooPaymentsInstalled:!1,wooPaymentsRecentlyActivated:!1,setWooPaymentsRecentlyActivated:()=>{}}),ee=()=>(0,v.useContext)(q),te=({children:e,closeModal:t})=>{const o=(0,r.useSelect)((e=>e(c.pluginsStore).getActivePlugins().includes("woocommerce-payments")),[]),s=(0,r.useSelect)((e=>e(c.pluginsStore).getInstalledPlugins().includes("woocommerce-payments")),[]),[n,a]=(0,v.useState)(!1),i={buildStepURL:(e,t={})=>(0,h.getNewPath)({path:e,...t},"/launch-your-store"+e,{page:"wc-admin",path:"/launch-your-store/woopayments/onboarding",sidebar:"hub",content:"payments"}),preserveParams:["sidebar","content"]};return(0,T.jsxs)(q.Provider,{value:{isWooPaymentsActive:o,isWooPaymentsInstalled:s,wooPaymentsRecentlyActivated:n,setWooPaymentsRecentlyActivated:a},children:[o&&(0,T.jsx)(z.X,{closeModal:t,onboardingSteps:J.PK,urlStrategy:i,sessionEntryPoint:"lys",onFinish:t,children:e}),!o&&e]})},oe=(e,t)=>{let o=(0,h.getHistory)().location;const s=(0,h.getHistory)().listen((({action:s,location:n})=>{if("POP"===s){const s=new URLSearchParams(o.search),a=new URLSearchParams(n.search);s.get(e)!==a.get(e)&&(o=n,t({type:"EXTERNAL_URL_UPDATE"}))}o=n}));return()=>{s()}},se=e=>{const t=(0,h.getQuery)(),o=Object.entries(e).reduce(((e,[o,s])=>(t[o]!==s&&(e[o]=s),e)),{});Object.keys(o).length>0&&(0,h.updateQueryString)(o)};let ne=null;const ae=async()=>{if(null!==ne)return ne;const e=await w()({path:"/wc-admin/launch-your-store/survey-completed"});return ne=e,ne},re=(0,d.Sx)((async()=>{const[e,t,o]=await Promise.all([ae(),(0,r.resolveSelect)(c.onboardingStore).getTaskListsByIds(["setup","extended"]),(0,r.resolveSelect)(c.pluginsStore).getActivePlugins()]);return{surveyCompleted:e,tasklists:t,activePlugins:o}})),ce=(0,d.SP)((({sendBack:e})=>oe("sidebar",e))),ie=async({url:e})=>{try{const t=await fetch(e,{method:"GET",credentials:"omit",cache:"no-store"});if(!t.ok)throw new Error(`Failed to fetch ${e}`);const o=await t.text();return!!(new DOMParser).parseFromString(o,"text/html").querySelector('meta[name="woo-coming-soon-page"]')}catch(t){throw new Error(`Error fetching ${e}: ${t}`)}},le=async()=>{const e=await(0,r.resolveSelect)(c.settingsStore).getSettings("wc_admin"),t=[];return e?.shopUrl&&t.push(ie({url:e.shopUrl})),e?.siteUrl&&t.push(ie({url:e.siteUrl})),(await Promise.all(t)).some((e=>e))},me=(0,u.mj)({types:{},actions:{showLaunchStoreSuccessPage:(0,_.c)((({context:e})=>e.mainContentMachineRef),{type:"SHOW_LAUNCH_STORE_SUCCESS"}),showLaunchStorePendingCache:(0,_.c)((({context:e})=>e.mainContentMachineRef),{type:"SHOW_LAUNCH_STORE_PENDING_CACHE"}),showLoadingPage:(0,_.c)((({context:e})=>e.mainContentMachineRef),{type:"SHOW_LOADING"}),showSitePreview:(0,_.c)((({context:e})=>e.mainContentMachineRef),{type:"EXTERNAL_URL_UPDATE"}),updateQueryParams:(e,t)=>{se(t)},taskClicked:({event:e,self:t})=>{if("TASK_CLICKED"===e.type){const o=function(e){var t;const o=null!==(t=Z())&&void 0!==t?t:[];$([...o,e.task.id]),window.sessionStorage.setItem("lysWaiting","yes");const{setWithExpiry:s}=(0,B.accessTaskReferralStorage)({taskId:e.task.id,referralLifetime:86400});if(s({referrer:"launch-your-store",returnUrl:(0,F.getAdminLink)("admin.php?page=wc-admin&path=/launch-your-store")}),(0,y.recordEvent)("launch_your_store_hub_task_clicked",{task:e.task.id}),"payments"===e.task.id){var n;const{wooPaymentsIsActive:t,wooPaymentsSettingsCountryIsSupported:o,wooPaymentsIsOnboarded:s,wooPaymentsHasTestAccount:a,wooPaymentsHasOtherProvidersEnabled:r,wooPaymentsHasOtherProvidersNeedSetup:c}=null!==(n=e.task?.additionalData)&&void 0!==n?n:{};if(o&&(!t&&!r||t&&!s||t&&a||t&&c))return(0,Y.W7)("woopayments_onboarding_modal_opened",{from:"lys_sidebar_task",source:K.K7}),{type:"SHOW_PAYMENTS"}}e.task.actionUrl?(0,h.navigateTo)({url:e.task.actionUrl}):(0,h.navigateTo)({url:(0,h.getNewPath)({task:e.task.id},"/",{})})}(e);o&&"object"==typeof o&&"type"in o&&"SHOW_PAYMENTS"===o.type&&t.send({type:"SHOW_PAYMENTS"})}},openWcAdminUrl:({event:e})=>{"OPEN_WC_ADMIN_URL"===e.type&&(0,h.navigateTo)({url:e.url})},windowHistoryBack:()=>{window.history.back()},recordStoreLaunchAttempt:(0,_.a)({launchStoreAttemptTimestamp:({context:e})=>{const t=e.tasklist?.fullLysTaskList.length||0,o=e.tasklist?.tasks.filter((e=>!e.isComplete)).map((e=>e.id))||[],s=e.tasklist?.fullLysTaskList.filter((e=>e.isComplete)).map((e=>e.id))||[],n=s.filter((t=>e.tasklist?.recentlyActionedTasks.includes(t)));return(0,y.recordEvent)("launch_your_store_hub_store_launch_attempted",{tasks_total_count:t,tasks_completed:s,tasks_completed_count:s.length,tasks_completed_in_lys:n,tasks_completed_in_lys_count:n.length,incomplete_tasks:o,incomplete_tasks_count:o.length,delete_test_orders:e.removeTestOrders||!1}),performance.now()}}),recordStoreLaunchResults:({context:e},{success:t})=>{((e,t)=>{(0,y.recordEvent)("launch_your_store_hub_store_launch_results",{success:t,duration:(0,l.D8)(performance.now()-e)})})(e.launchStoreAttemptTimestamp||0,t)},recordStoreLaunchCachedContentDetected:()=>{(0,y.recordEvent)("launch_your_store_hub_store_launch_cached_content_detected")},showPaymentsContent:(0,_.c)((({context:e})=>e.mainContentMachineRef),{type:"SHOW_PAYMENTS"}),triggerTasklistRefresh:({self:e})=>{e.send({type:"REFRESH_TASKLIST"})},navigateToWcAdmin:()=>{window.location.href="/wp-admin/admin.php?page=wc-admin"}},guards:{hasSidebarLocation:(e,{sidebar:t})=>{const{sidebar:o}=(0,h.getQuery)();return!!o&&o===t},hasPaymentsContent:()=>{const{content:e}=(0,h.getQuery)();return"payments"===e},hasWooPaymentsOnboardingPath:()=>{const e=(0,h.getQuery)();return!!e.path&&e.path.includes("/woopayments/onboarding")},hasWooPayments:({context:e})=>!!e.hasWooPayments,siteIsShowingCachedContent:({context:e})=>!!e.siteIsShowingCachedContent},actors:{sidebarQueryParamListener:ce,getTasklist:(0,d.Sx)((async()=>{var e;const t=(0,U.applyFilters)("woocommerce_launch_your_store_tasklist_whitelist",["products","customize-store","payments","shipping","tax"]),o=await(0,r.resolveSelect)(c.onboardingStore).getTaskListsByIds(["setup"]),s=null!==(e=Z())&&void 0!==e?e:[];o[0].tasks.forEach((e=>{if("payments"===e.id){let t=!1;(e.additionalData?.wooPaymentsHasOnlineGatewaysEnabled&&!e.additionalData?.wooPaymentsIsOnboarded||e.additionalData?.wooPaymentsIsOnboarded&&!e.additionalData?.wooPaymentsHasTestAccount)&&(t=!0),e.isComplete=t,e.title=(0,S.__)("Set up payments","woocommerce")}}));const n=o[0].tasks.filter((e=>t.includes(e.id)&&(!e.isComplete||s.includes(e.id))));return{...o[0],tasks:n,recentlyActionedTasks:s,fullLysTaskList:o[0].tasks.filter((e=>t.includes(e.id)))}})),getTestOrderCount:(0,d.Sx)((async()=>(await w()({path:"/wc-admin/launch-your-store/woopayments/test-orders/count",method:"GET"})).count)),getSiteCachedStatus:(0,d.Sx)(le),updateLaunchStoreOptions:(0,d.Sx)((async()=>{const e=await(0,r.dispatch)(c.optionsStore).updateOptions({woocommerce_coming_soon:"no"});if(e.success)return e;throw new Error(JSON.stringify(e))})),deleteTestOrders:(0,d.Sx)((async({input:e})=>e.removeTestOrders?await w()({path:"/wc-admin/launch-your-store/woopayments/test-orders",method:"DELETE"}):null)),fetchCongratsData:re,getWooPaymentsStatus:(0,d.Sx)((async()=>{if(!1===window?.wcSettings?.admin?.plugins?.activePlugins.includes("woocommerce-payments"))return!1;const e=(await(0,r.resolveSelect)(c.paymentGatewaysStore).getPaymentGateways()).filter((e=>e.enabled));return 1===e.length&&(0,Y.j4)(e[0].id)}))}}).createMachine({id:"sidebar",initial:"navigate",context:({input:e})=>({externalUrl:null,testOrderCount:0,mainContentMachineRef:e.mainContentMachineRef}),invoke:{id:"sidebarQueryParamListener",src:"sidebarQueryParamListener"},states:{navigate:{always:[{guard:{type:"hasWooPaymentsOnboardingPath"},target:"payments"},{guard:{type:"hasPaymentsContent"},target:"payments"},{guard:{type:"hasSidebarLocation",params:{sidebar:"hub"}},target:"launchYourStoreHub"},{guard:{type:"hasSidebarLocation",params:{sidebar:"launch-success"}},target:"storeLaunchSuccessful"},{target:"launchYourStoreHub"}]},launchYourStoreHub:{initial:"preLaunchYourStoreHub",states:{preLaunchYourStoreHub:{entry:[(0,p.P)("fetchCongratsData",{id:"prefetch-congrats-data "})],invoke:{src:"getTasklist",onDone:{actions:(0,_.a)({tasklist:({event:e})=>e.output}),target:"checkWooPayments"}}},checkWooPayments:{invoke:{src:"getWooPaymentsStatus",onDone:{actions:(0,_.a)({hasWooPayments:({event:e})=>e.output}),target:"maybeCountTestOrders"},onError:{target:"maybeCountTestOrders"}}},maybeCountTestOrders:{always:[{guard:"hasWooPayments",target:"countTestOrders"},{target:"launchYourStoreHub"}]},countTestOrders:{invoke:{src:"getTestOrderCount",onDone:{actions:(0,_.a)({testOrderCount:({event:e})=>e.output}),target:"launchYourStoreHub"},onError:{target:"launchYourStoreHub"}}},launchYourStoreHub:{id:"launchYourStoreHub",tags:"sidebar-visible",meta:{component:G},on:{LAUNCH_STORE:{target:"#storeLaunching"},POP_BROWSER_STACK:{actions:["navigateToWcAdmin"]},REFRESH_TASKLIST:{target:"backgroundRefresh"}}},backgroundRefresh:{id:"backgroundRefresh",tags:"sidebar-visible",meta:{component:G},invoke:[{src:"getTasklist",onDone:{actions:(0,_.a)({tasklist:({event:e})=>e.output}),target:"backgroundCheckWooPayments"},onError:{target:"launchYourStoreHub"}}],on:{LAUNCH_STORE:{target:"#storeLaunching"},POP_BROWSER_STACK:{actions:["navigateToWcAdmin"]}}},backgroundCheckWooPayments:{tags:"sidebar-visible",meta:{component:G},invoke:{src:"getWooPaymentsStatus",onDone:{actions:(0,_.a)({hasWooPayments:({event:e})=>e.output}),target:"backgroundMaybeCountTestOrders"},onError:{target:"backgroundMaybeCountTestOrders"}},on:{LAUNCH_STORE:{target:"#storeLaunching"},POP_BROWSER_STACK:{actions:["navigateToWcAdmin"]}}},backgroundMaybeCountTestOrders:{tags:"sidebar-visible",meta:{component:G},always:[{guard:"hasWooPayments",target:"backgroundCountTestOrders"},{target:"launchYourStoreHub"}]},backgroundCountTestOrders:{tags:"sidebar-visible",meta:{component:G},invoke:{src:"getTestOrderCount",onDone:{actions:(0,_.a)({testOrderCount:({event:e})=>e.output}),target:"launchYourStoreHub"},onError:{target:"launchYourStoreHub"}},on:{LAUNCH_STORE:{target:"#storeLaunching"},POP_BROWSER_STACK:{actions:["navigateToWcAdmin"]}}}}},storeLaunching:{id:"storeLaunching",initial:"launching",states:{launching:{entry:[(0,_.a)({launchStoreError:void 0}),"recordStoreLaunchAttempt"],invoke:[{src:"updateLaunchStoreOptions",onDone:{actions:[{type:"recordStoreLaunchResults",params:{success:!0}}],target:"checkingForCachedContent"},onError:{actions:[(0,_.a)({launchStoreError:({event:e})=>({message:JSON.stringify(e.error)})}),{type:"recordStoreLaunchResults",params:{success:!1}}],target:"#launchYourStoreHub"}},{src:"deleteTestOrders",input:({event:e})=>({removeTestOrders:e.removeTestOrders})}]},checkingForCachedContent:{invoke:[{src:"getSiteCachedStatus",onDone:{target:"#storeLaunchSuccessful",actions:(0,_.a)({siteIsShowingCachedContent:({event:e})=>e.output})},onError:{target:"#storeLaunchSuccessful"}}]}}},storeLaunchSuccessful:{id:"storeLaunchSuccessful",tags:"fullscreen",entry:[{type:"updateQueryParams",params:{sidebar:"launch-success",content:"launch-store-success"}},(0,_.b)((({check:e,enqueue:t})=>{if(e("siteIsShowingCachedContent"))return t({type:"showLaunchStorePendingCache"}),void t({type:"recordStoreLaunchCachedContentDetected"});t({type:"showLaunchStoreSuccessPage"})}))]},payments:{id:"payments",meta:{component:e=>{const{wooPaymentsRecentlyActivated:t,isWooPaymentsActive:o}=ee(),{steps:s,currentStep:n,justCompletedStepId:a,isLoading:r}=(0,z.w)(),{context:c}=e,l=c.tasklist?.tasks?.find((e=>"payments"===e.id)),m=s.findIndex((e=>e.id===n?.id)),d=e=>e.id===a||"completed"===e.status||m===s.length,u=s.sort(((e,t)=>{const o=d(e);return o===d(t)?0:o?-1:1})),_=(0,T.jsx)(x.Button,{onClick:()=>{(0,y.recordEvent)("launch_your_store_payments_back_to_hub_click"),(0,Y.W7)("woopayments_onboarding_modal_closed",{from:"lys_sidebar_back_to_hub",source:K.K7}),window.sessionStorage.setItem("lysWaiting","no"),e.sendEventToSidebar({type:"RETURN_FROM_PAYMENTS"})},children:(0,S.__)("Set up WooPayments","woocommerce")}),p=({isStepComplete:e})=>(0,T.jsx)(C.A,{className:(0,i.A)("install-woopayments",{active:e,"payment-step":!0,"payment-step--active":e,"payment-step--disabled":e,"is-complete":e}),icon:e?H:D.activePaymentStep,disabled:!0,showChevron:!1,children:l?.additionalData?.wooPaymentsIsInstalled?(0,S.sprintf)((0,S.__)("Enable %s","woocommerce"),"WooPayments"):(0,S.sprintf)((0,S.__)("Install %s","woocommerce"),"WooPayments")},"install-woopayments");return(0,T.jsxs)("div",{className:(0,i.A)("launch-store-sidebar__container",e.className),children:[(0,T.jsx)(x.__unstableMotion.div,{className:"woocommerce-edit-site-layout__header-container",animate:"view",children:(0,T.jsx)(I.b,{variants:{view:{x:0}},isTransparent:!1,className:"woocommerce-edit-site-layout__hub"})}),(0,T.jsx)(f,{title:_,children:(0,T.jsxs)(x.__experimentalItemGroup,{className:"woocommerce-edit-site-sidebar-navigation-screen-essential-tasks__group",children:[!o&&(0,T.jsx)(x.__unstableMotion.div,{initial:{opacity:0,y:0},animate:{opacity:1,y:0},transition:{duration:.7,delay:.2},children:(0,T.jsx)(p,{isStepComplete:!1})}),o&&r&&(0,T.jsx)(x.__unstableMotion.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},children:(0,T.jsx)(X,{rows:3})}),o&&!r&&(0,T.jsxs)(x.__unstableMotion.div,{initial:{opacity:0,y:0},animate:{opacity:1,y:0},transition:{duration:.7,delay:.2},children:[t&&(0,T.jsx)(p,{isStepComplete:!0}),u.map((e=>(0,T.jsx)(C.A,{className:(0,i.A)(e.id,{active:n?.id===e.id,"payment-step":!0,"payment-step--active":n?.id===e.id,"payment-step--disabled":n?.id!==e.id,"is-complete":d(e)}),icon:d(e)?H:D.activePaymentStep,disabled:!0,showChevron:!1,children:e.label},e.id)))]})]})})]})}},entry:["showPaymentsContent",{type:"updateQueryParams",params:{sidebar:"hub",content:"payments"}}],on:{POP_BROWSER_STACK:{actions:[{type:"navigateToWcAdmin"}]},RETURN_FROM_PAYMENTS:{target:"#launchYourStoreHub",actions:[{type:"updateQueryParams",params:{sidebar:"hub",content:"site-preview"}},(0,_.c)((({context:e})=>e.mainContentMachineRef),{type:"RETURN_FROM_PAYMENTS"}),"triggerTasklistRefresh"]}}}},on:{EXTERNAL_URL_UPDATE:{target:".navigate"},TASK_CLICKED:{actions:"taskClicked"},OPEN_WC_ADMIN_URL:{actions:"openWcAdminUrl"},OPEN_WC_ADMIN_URL_IN_CONTENT_AREA:{},SHOW_PAYMENTS:{target:".payments"}}}),de=({children:e,className:t})=>(0,T.jsx)("div",{className:(0,i.A)("launch-your-store-layout__sidebar",t),children:e});var ue=o(98846);const _e=()=>(0,T.jsx)("div",{className:"spinner-container",children:(0,T.jsx)(ue.Spinner,{})});var pe=o(29491),he=o(56109),ye=o(61208),ge=o(58016),we=o(46772);const ve=({installWooPayments:e,isPluginInstalling:t,isPluginInstalled:o})=>{(0,v.useEffect)((()=>{(0,Y.W7)("woopayments_onboarding_modal_step_view",{step:"install_woopayments",from:"lys",source:K.K7})}),[]);const s=(0,r.useSelect)((e=>e(c.paymentSettingsStore).getIsWooPayEligible()),[]),n=(0,r.useSelect)((e=>e(c.paymentSettingsStore).getPaymentProviders().find((e=>(0,Y.j4)(e.id)))),[]),a=window.wcSettings?.admin?.woocommerce_payments_nox_profile?.business_country_code||null;let i=(0,S.__)("Install","woocommerce");return o&&!t&&(i=(0,S.__)("Enable","woocommerce")),o&&t&&(i=(0,S.__)("Enabling","woocommerce")),!o&&t&&(i=(0,S.__)("Installing","woocommerce")),(0,T.jsxs)("div",{className:"launch-your-store-payments-content__step--install-woopayments",children:[(0,T.jsx)("div",{className:"launch-your-store-payments-content__step--install-woopayments-logo",children:(0,T.jsx)("img",{src:`${he.GZ}images/woo-logo.svg`,alt:"",role:"presentation"})}),(0,T.jsx)("h1",{className:"launch-your-store-payments-content__step--install-woopayments-title",children:(0,S.__)("Accept payments with Woo","woocommerce")}),(0,T.jsx)("p",{className:"launch-your-store-payments-content__step--install-woopayments-description",children:(0,S.__)("Set up payments for your store in just a few steps. With WooPayments, you can accept online and in-person payments, track revenue, and handle all payment activity from one place.","woocommerce")}),(0,T.jsx)("div",{className:"launch-your-store-payments-content__step--install-woopayments-logos",children:(0,T.jsx)(B.WooPaymentsMethodsLogos,{maxElements:10,isWooPayEligible:s})}),(0,T.jsx)(x.Button,{className:"launch-your-store-payments-content__step--install-woopayments-button",onClick:()=>{n?.onboarding?._links?.preload?.href&&w()({url:n?.onboarding?._links?.preload?.href,method:"POST",data:{location:a}}),e()},isBusy:t,disabled:t,variant:"primary",children:i})]})};var Se=o(5751);const Ce={congratsScreen:({context:e})=>(w()({path:"/wc-admin/launch-your-store/update-survey-status",data:{status:"yes"},method:"POST"}).catch((()=>{})),{...e.congratsScreen,hasCompleteSurvey:!0})},xe=({activePlugins:e,allTasklists:t})=>{const o=(0,v.useMemo)((()=>(({activePlugins:e,allTasklists:t})=>{const o=[],s=(e,t)=>{o.length<3&&t&&o.push(e)},n=t.find((({id:e})=>"setup"===e))?.tasks?.reduce(((e,{id:t,isComplete:o})=>(e[t]=o||!1,e)),{}),a=t.find((({id:e})=>"extended"===e))?.tasks?.reduce(((e,{id:t,isComplete:o})=>(e[t]=o||!1,e)),{}),r=a?.marketing||!1,c=n?.payments||!1,i=a?.["get-mobile-app"]||!1,l=e.includes("mailchimp-for-woocommerce"),m={title:(0,S.__)("Promote your products","woocommerce"),description:(0,S.__)("Grow your customer base by promoting your products to millions of engaged shoppers.","woocommerce"),link:`${he.kY}admin.php?page=wc-admin&task=marketing`,linkText:(0,S.__)("Promote products","woocommerce"),trackEvent:"launch_you_store_congrats_marketing_click"},d={title:(0,S.__)("Provide more ways to pay","woocommerce"),description:(0,S.__)("Give your shoppers more ways to pay by adding additional payment methods to your store.","woocommerce"),link:`${he.kY}admin.php?page=wc-settings&tab=checkout`,linkText:(0,S.__)("Add payment methods","woocommerce"),trackEvent:"launch_you_store_congrats_payments_click"},u={title:(0,S.__)("Build customer relationships","woocommerce"),description:(0,S.__)("Keep your shoppers up to date with what’s new in your store and set up clever post-purchase automations.","woocommerce"),link:l?`${he.kY}admin.php?page=mailchimp-woocommerce`:"https://woo.com/products/mailchimp-for-woocommerce/?utm_source=launch_your_store&utm_medium=product",linkText:l?(0,S.__)("Manage Mailchimp","woocommerce"):(0,S.__)("Install Mailchimp","woocommerce"),trackEvent:"launch_you_store_congrats_mailchimp_click"},_={title:(0,S.__)("Power up your store","woocommerce"),description:(0,S.__)("Add extra features and functionality to your store with Woo extensions.","woocommerce"),link:`${he.kY}admin.php?page=wc-admin&path=%2Fextensions`,linkText:(0,S.__)("Add extensions","woocommerce"),trackEvent:"launch_you_store_congrats_extensions_click"},p={title:(0,S.__)("Manage your store on the go","woocommerce"),description:(0,S.__)("Manage your store anywhere with the free WooCommerce Mobile App.","woocommerce"),link:`${he.kY}admin.php?page=wc-admin&mobileAppModal=true`,linkText:(0,S.__)("Get the app","woocommerce"),trackEvent:"launch_you_store_congrats_mobile_app_click"},h={title:(0,S.__)("Help is on hand","woocommerce"),description:(0,S.__)("Detailed guides and our support team are always available if you’re feeling stuck or need some guidance.","woocommerce"),link:"https://woo.com/documentation/woocommerce/?utm_source=launch_your_store&utm_medium=product",linkText:(0,S.__)("Explore support resources","woocommerce"),trackEvent:"launch_you_store_congrats_external_documentation_click"};return s(m,!r),s(d,!c),s(_,!0),s(p,!i),s(u,!l),s(h,!0),s(d,!0),s(_,!0),s(h,!0),o})({activePlugins:e,allTasklists:t})),[e,t]);return(0,T.jsx)("div",{className:"woocommerce-launch-store__congrats-main-actions",children:o.map(((e,t)=>(0,T.jsx)("div",{className:"woocommerce-launch-store__congrats-action",children:(0,T.jsxs)("div",{className:"woocommerce-launch-store__congrats-action__content",children:[(0,T.jsx)("h3",{children:e.title}),(0,T.jsx)("p",{children:e.description}),(0,T.jsx)(x.Button,{variant:"link",href:e.link,target:-1===e.link.indexOf(he.kY)?"_blank":"_self",onClick:()=>{(0,y.recordEvent)(e.trackEvent)},children:e.linkText})]})},t)))})};var be=o(66087),ke=o(48214),Pe=o(27752),Te=o(97687);const fe=({hasCompleteSurvey:e,onSubmit:t})=>{const[o,s]=(0,v.useState)(null),[n,a]=(0,v.useState)(""),[r,c]=(0,v.useState)(!1),[i,l]=(0,v.useState)(!e),m=(0,be.isInteger)(o);return(0,T.jsxs)(T.Fragment,{children:[i&&(0,T.jsx)("hr",{className:"separator"}),i&&(0,T.jsxs)("div",{className:"woocommerce-launch-store__congrats-survey",children:[r?(0,T.jsxs)("div",{className:"woocommerce-launch-store__congrats-thanks",children:[(0,T.jsxs)("p",{className:"thanks-copy",children:["🙌"," ",(0,S.__)("We appreciate your feedback!","woocommerce")]}),(0,T.jsx)(x.Button,{className:"close-button",label:(0,S.__)("Close","woocommerce"),icon:(0,T.jsx)(x.Icon,{icon:ke.A,viewBox:"6 4 12 14"}),iconSize:14,onClick:()=>{c(!1),l(!1)}})]}):(0,T.jsxs)("div",{className:"woocommerce-launch-store__congrats-section_1",children:[(0,T.jsx)("div",{className:"woocommerce-launch-store__congrats-survey__selection",children:(0,T.jsx)(Pe.CustomerFeedbackSimple,{label:(0,S.__)("How was the experience of launching your store?","woocommerce"),onSelect:s,selectedValue:o})}),m&&(0,T.jsxs)("div",{className:"woocommerce-launch-store__congrats-survey__comment",children:[(0,T.jsx)("label",{className:"comment-label",htmlFor:"launch-your-store-comment",children:(0,v.createInterpolateElement)((0,S.__)("Why do you feel that way? <smallText>(optional)</smallText>","woocommerce"),{smallText:(0,T.jsx)("span",{className:"small-text"})})}),(0,T.jsx)(x.TextareaControl,{__nextHasNoMarginBottom:!0,id:"launch-your-store-comment","data-testid":"launch-your-store-comment",value:n,onChange:e=>{a(e)}}),(0,T.jsx)("span",{className:"privacy-text",children:(0,v.createInterpolateElement)((0,S.__)("Your feedback will be only be shared with WooCommerce and treated in accordance with our <privacyLink>privacy policy</privacyLink>.","woocommerce"),{privacyLink:(0,T.jsx)(ue.Link,{href:"https://automattic.com/privacy/",type:"external",target:"_blank",children:(0,T.jsx)(T.Fragment,{})})})})]})]}),m&&!r&&(0,T.jsx)("div",{className:"woocommerce-launch-store__congrats-section_2",children:(0,T.jsxs)("div",{className:"woocommerce-launch-store__congrats-buttons",children:[(0,T.jsx)(x.Button,{className:"",variant:"tertiary",onClick:()=>{s(null)},children:(0,S.__)("Cancel","woocommerce")}),(0,T.jsx)(x.Button,{className:"",variant:"primary",onClick:()=>{(0,y.recordEvent)((0,Te.E)()?"launch_your_store_congrats_survey_click":"launch_your_store_on_core_congrats_survey_click"),t({action:"lys_experience",score:o,comments:n}),c(!0)},children:(0,S.__)("Send","woocommerce")})]})})]})]})},je=(0,d.SP)((({sendBack:e})=>oe("content",e))),Ee=(0,u.mj)({types:{},actions:{updateQueryParams:(e,t)=>{se(t)},cleanupPaymentsUrl:()=>{const e=new URL(window.location.href);e.searchParams.delete("content"),e.searchParams.get("path")?.includes("woopayments")&&e.searchParams.set("path","/launch-your-store"),window.history.replaceState(null,"",e.toString())},assignSiteCachedStatus:(0,_.a)({siteIsShowingCachedContent:!0}),recordSurveyResults:({event:e})=>{(0,u.DT)(e,"COMPLETE_SURVEY"),(0,y.recordEvent)("launch_your_store_congrats_survey_complete",{action:e.payload.action,score:e.payload.score,comments:e.payload.comments})},recordBackToHomeClick:()=>{(0,y.recordEvent)("launch_your_store_congrats_back_to_home_click")},recordPreviewStoreClick:()=>{(0,y.recordEvent)("launch_your_store_congrats_preview_store_click")},navigateToPreview:()=>{const e=(0,F.getSetting)("homeUrl","");window.open(e,"_blank")},navigateToHome:()=>{const{invalidateResolutionForStoreSelector:e}=(0,r.dispatch)(c.onboardingStore);e("getTaskLists"),(0,h.navigateTo)({url:"/"})}},guards:{hasContentLocation:(e,{content:t})=>{const{content:o}=(0,h.getQuery)();return!!o&&o===t},hasWooPaymentsOnboardingPath:()=>{const e=(0,h.getQuery)();return!!e.path&&e.path.includes("/woopayments/onboarding")}},actors:{contentQueryParamListener:je,fetchCongratsData:re,getSiteCachedStatus:(0,d.Sx)(le)}}).createMachine({id:"mainContent",initial:"navigate",context:{congratsScreen:{hasLoadedCongratsData:!1,hasCompleteSurvey:!1,allTasklists:[],activePlugins:[]},siteIsShowingCachedContent:void 0},invoke:{id:"contentQueryParamListener",src:"contentQueryParamListener"},states:{navigate:{always:[{guard:{type:"hasWooPaymentsOnboardingPath"},target:"payments"},{guard:{type:"hasContentLocation",params:{content:"site-preview"}}},{guard:{type:"hasContentLocation",params:{content:"launch-store-success"}},target:"launchStoreSuccess"},{guard:{type:"hasContentLocation",params:{content:"payments"}},target:"payments"},{target:"#sitePreview"}]},sitePreview:{id:"sitePreview",meta:{component:e=>{const t=(0,he.Qk)("siteUrl")+"?site-preview=1",[o,s]=(0,v.useState)(!0),n=(0,v.useRef)(null),[a,r]=(0,pe.useResizeObserver)(),[c,l]=(0,v.useState)(!1);return(0,v.useEffect)((()=>{const e=n.current?.contentWindow,t=()=>{s(!0)};return e&&e.addEventListener("beforeunload",t),()=>{e&&e.removeEventListener("beforeunload",t)}}),[n,s,o]),(0,T.jsxs)("div",{className:(0,i.A)("launch-store-site-preview-page__container",{"is-loading":o},e.className),children:[a,!!r.width&&(0,T.jsx)(x.__unstableMotion.div,{initial:!1,layout:"position",className:"launch-store-preview-layout__canvas",children:(0,T.jsxs)(ye.A,{isReady:!o,isHandleVisibleByDefault:!1,isFullWidth:!1,defaultSize:{width:r.width-24,height:r.height},isOversized:c,setIsOversized:l,innerContentStyle:{},children:[o&&(0,T.jsx)("div",{className:"launch-store-site-preview-site__loading-overlay",children:(0,T.jsx)(ue.Spinner,{})}),(0,T.jsx)("iframe",{ref:n,className:"launch-store-site__preview-site-iframe",src:t,title:(0,S.__)("Preview","woocommerce"),onLoad:()=>s(!1)})]})})]})}}},payments:{id:"payments",meta:{component:({})=>{const{isWooPaymentsActive:e,isWooPaymentsInstalled:t,setWooPaymentsRecentlyActivated:o}=ee(),{refreshStoreData:s}=(0,z.w)(),[a,i]=(0,v.useState)(!1),{installAndActivatePlugins:l}=(0,r.useDispatch)(c.pluginsStore),m=(0,n.useCallback)((()=>{i(!0),(0,Y.TH)("recommendations_setup",{extension_selected:K.bw,extension_action:t?"activate":"install",provider_id:K.$8,suggestion_id:K.eD,provider_extension_slug:K.bw,from:"lys",source:K.K7}),l([K.bw]).then((async e=>{(0,we.R)(e),o(!0),s(),t||(0,Y.TH)("provider_installed",{provider_id:K.$8,suggestion_id:K.eD,provider_extension_slug:K.bw,from:"lys",source:K.K7}),i(!1)})).catch((e=>{let o="provider_extension_installation_failed";t&&(o="provider_extension_activation_failed"),(0,Y.TH)(o,{provider_id:K.$8,suggestion_id:K.eD,provider_extension_slug:K.bw,from:"lys",source:K.K7,reason:"error"}),(0,we.R)(e),i(!1)}))}),[i,l,s,o]);return(0,T.jsx)("div",{className:"launch-your-store-payments-content",children:(0,T.jsx)("div",{className:"launch-your-store-payments-content__canvas",children:e?(0,T.jsx)(ge.A,{includeSidebar:!1}):(0,T.jsx)(ve,{installWooPayments:m,isPluginInstalled:t,isPluginInstalling:a})})})}},entry:[{type:"updateQueryParams",params:{content:"payments"}}],on:{EXTERNAL_URL_UPDATE:{target:"navigate"}}},launchStoreSuccess:{id:"launchStoreSuccess",initial:"loading",states:{loading:{invoke:[{src:"fetchCongratsData",onDone:{actions:(0,_.a)({congratsScreen:({context:e,event:t})=>({...e.congratsScreen,hasLoadedCongratsData:!0,hasCompleteSurvey:"yes"===t.output.surveyCompleted,allTasklists:t.output.tasklists,activePlugins:t.output.activePlugins})})}},{src:"getSiteCachedStatus",onDone:{actions:(0,_.a)({siteIsShowingCachedContent:({event:e})=>e.output})},onError:{actions:(0,_.a)({siteIsShowingCachedContent:!1})}}],always:{guard:({context:e})=>e.congratsScreen.hasLoadedCongratsData&&void 0!==e.siteIsShowingCachedContent,target:"congrats"},meta:{component:_e}},congrats:{entry:[{type:"updateQueryParams",params:{content:"launch-store-success"}}],meta:{component:({context:{congratsScreen:{activePlugins:e,allTasklists:t,hasCompleteSurvey:o},siteIsShowingCachedContent:s},sendEventToMainContent:n,className:a})=>{const r=(0,S.__)("Copy link","woocommerce"),c=(0,S.__)("Copied!","woocommerce"),m=(0,F.getSetting)("homeUrl",""),d=new URL(m);let u=d?.hostname;d?.port&&(u+=":"+d.port);const[_,p]=(0,v.useState)(r),h=(0,pe.useCopyToClipboard)(m,(()=>{p(c),setTimeout((()=>{p(r)}),2e3)}));return(0,l.xG)(["woocommerce-launch-your-store-success"]),(0,T.jsx)("div",{className:(0,i.A)("launch-store-success-page__container",a),children:(0,T.jsxs)("div",{className:"woocommerce-launch-store__congrats",children:[(0,T.jsx)(ue.ConfettiAnimation,{delay:1e3,colors:["#DFD1FB","#FB79D9","#FFA60E","#03D479","#AD86E9","#7F54B3","#3C2861"]}),(0,T.jsxs)("div",{className:"woocommerce-launch-store__congrats-header-container",children:[(0,T.jsx)("span",{className:"woologo",children:(0,T.jsx)(Se.A,{})}),(0,T.jsxs)(x.Button,{onClick:()=>{n({type:"BACK_TO_HOME"})},className:"back-to-home-button",variant:"link",children:[(0,T.jsx)(x.Dashicon,{icon:"arrow-left-alt2"}),(0,T.jsx)("span",{children:(0,S.__)("Back to Home","woocommerce")})]})]}),(0,T.jsxs)("div",{className:"woocommerce-launch-store__congrats-content",children:[(0,T.jsx)("h1",{className:"woocommerce-launch-store__congrats-heading",children:s?(0,S.__)("Congratulations! Your store will launch soon","woocommerce"):(0,S.__)("Congratulations! Your store is now live","woocommerce")}),(0,T.jsx)("h2",{className:"woocommerce-launch-store__congrats-subheading",children:s?(0,v.createInterpolateElement)((0,S.__)("It’ll be ready to view as soon as your <link></link> have updated. Please wait, or contact your web host to find out how to do this manually.","woocommerce"),{link:(0,T.jsx)("a",{href:"https://woocommerce.com/document/configuring-woocommerce-settings/coming-soon-mode/#server-caches",target:"_blank",rel:"noreferrer",children:(0,S.__)("server caches","woocommerce")})}):(0,S.__)("You’ve successfully launched your store and are ready to start selling! We can’t wait to see your business grow.","woocommerce")}),(0,T.jsxs)("div",{className:"woocommerce-launch-store__congrats-midsection-container",children:[(0,T.jsxs)("div",{className:"woocommerce-launch-store__congrats-visit-store",children:[(0,T.jsx)("p",{className:"store-name",children:u}),(0,T.jsxs)("div",{className:"buttons-container",children:[(0,T.jsx)(x.Button,{className:"",variant:"secondary",ref:h,onClick:()=>{(0,y.recordEvent)("launch_your_store_congrats_copy_store_link_click")},children:_}),(0,T.jsx)(x.Button,{className:"",variant:"primary",onClick:()=>{n({type:"PREVIEW_STORE"})},children:(0,S.__)("Visit your store","woocommerce")})]})]}),(0,T.jsx)(fe,{hasCompleteSurvey:o,onSubmit:e=>{n({type:"COMPLETE_SURVEY",payload:e})}})]}),(0,T.jsx)("h2",{className:"woocommerce-launch-store__congrats-main-actions-title",children:(0,S.__)("What’s next?","woocommerce")}),(0,T.jsx)(xe,{activePlugins:e,allTasklists:t})]})]})})}}}},on:{COMPLETE_SURVEY:{actions:[(0,_.a)(Ce),"recordSurveyResults"]},PREVIEW_STORE:{actions:["recordPreviewStoreClick","navigateToPreview"]},BACK_TO_HOME:{actions:["recordBackToHomeClick","navigateToHome"]}}},loading:{id:"loading",meta:{component:_e}}},on:{EXTERNAL_URL_UPDATE:{target:".navigate"},SHOW_LAUNCH_STORE_SUCCESS:{target:"#launchStoreSuccess"},SHOW_LAUNCH_STORE_PENDING_CACHE:{actions:["assignSiteCachedStatus"],target:"#launchStoreSuccess"},SHOW_LOADING:{target:"#loading"},SHOW_PAYMENTS:{target:"#payments"},POP_BROWSER_STACK:{actions:(0,_.a)({siteIsShowingCachedContent:void 0}),target:"#sitePreview"},RETURN_FROM_PAYMENTS:{actions:[(0,_.a)({siteIsShowingCachedContent:void 0}),"cleanupPaymentsUrl"],target:"#sitePreview"}}}),Le=({children:e})=>(0,T.jsx)("div",{className:"launch-your-store-layout__content",children:e});var Ne=o(89677);const Ae=()=>{(0,l.xG)(["woocommerce-launch-your-store"]),(0,n.useEffect)((()=>{window.sessionStorage.setItem("lysWaiting","no")}),[]);const{xstateV5Inspector:e}=(0,Ne.D)("V5"),{invalidateResolutionForStoreSelector:t}=(0,r.useDispatch)(c.onboardingStore),[o,a,d]=(0,s.zl)(Ee,{inspect:e}),[u,_,p]=(0,s.zl)(me,{inspect:e,input:{mainContentMachineRef:d}}),h=!u.hasTag("fullscreen"),[y]=(0,m.$)(p),[g]=(0,m.$)(d);return(0,T.jsx)("div",{className:"launch-your-store-layout__container",children:(0,T.jsxs)(te,{closeModal:()=>{(0,Y.W7)("woopayments_onboarding_modal_closed",{from:"lys_modal_close_button",source:K.K7}),window.sessionStorage.setItem("lysWaiting","no"),t("getTaskLists"),t("getTaskListsByIds"),_({type:"RETURN_FROM_PAYMENTS"})},children:[(0,T.jsx)(de,{className:(0,i.A)({"is-sidebar-hidden":!h}),children:y&&(0,T.jsx)(y,{sendEventToSidebar:_,sendEventToMainContent:a,context:u.context})}),(0,T.jsx)(Le,{children:g&&(0,T.jsx)(g,{sendEventToSidebar:_,sendEventToMainContent:a,context:o.context},o.value.toString())})]})})}},68345:(e,t,o)=>{o.d(t,{M:()=>d});var s=o(27723),n=o(56427),a=o(86087),r=o(47143),c=o(40314),i=o(1069),l=o(22861),m=o(39793);const d=({isOpen:e,onClose:t,isTestMode:o,isEmbeddedResetFlow:d=!1})=>{const[u,_]=(0,a.useState)(!1),{invalidateResolutionForStoreSelector:p}=(0,r.useDispatch)(c.paymentSettingsStore),{invalidateResolutionForStoreSelector:h}=(0,r.useDispatch)(c.woopaymentsOnboardingStore),{createNotice:y}=(0,r.useDispatch)("core/notices");let g=o?(0,s.sprintf)((0,s.__)("When you reset your test account, all payment data — including your %s account details, test transactions, and payouts history — will be lost. Your order history will remain. This action cannot be undone, but you can create a new test account at any time.","woocommerce"),"WooPayments"):(0,s.sprintf)((0,s.__)("When you reset your account, all payment data — including your %s account details, test transactions, and payouts history — will be lost. Your order history will remain. This action cannot be undone, but you can create a new test account at any time.","woocommerce"),"WooPayments");return d&&(g=(0,s.sprintf)((0,s.__)("You need to reset your test account to continue onboarding with %1$s. This will create a new test account and reset any existing %2$s account details and test transactions.","woocommerce"),"WooPayments","WooPayments")),(0,m.jsx)(m.Fragment,{children:e&&(0,m.jsxs)(n.Modal,{title:(0,s.__)("Reset your test account","woocommerce"),className:"woocommerce-woopayments-modal",isDismissible:!0,onRequestClose:t,children:[(0,m.jsxs)("div",{className:"woocommerce-woopayments-modal__content",children:[(0,m.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,m.jsx)("div",{children:(0,m.jsx)("span",{children:g})})}),(0,m.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,m.jsx)("h3",{children:(0,s.__)("Are you sure you'd like to continue?","woocommerce")})})]}),(0,m.jsx)("div",{className:"woocommerce-woopayments-modal__actions",children:(0,m.jsx)(n.Button,{className:d?"":"danger",variant:d?"primary":"secondary",isBusy:u,disabled:u,onClick:()=>{(0,i.TH)("provider_reset_onboarding_confirmation_click",{provider_id:l.$8,suggestion_id:l.eD,provider_extension_slug:l.bw}),_(!0),(0,i.pF)().then((()=>{(0,i.TH)("provider_reset_onboarding_success",{provider_id:l.$8,suggestion_id:l.eD,provider_extension_slug:l.bw}),p("getPaymentProviders"),h("getOnboardingData")})).catch((()=>{(0,i.TH)("provider_reset_onboarding_failed",{provider_id:l.$8,suggestion_id:l.eD,provider_extension_slug:l.bw,reason:"error"}),y("error",(0,s.__)("Failed to reset your WooPayments account.","woocommerce"),{isDismissible:!0})})).finally((()=>{_(!1),t()}))},children:(0,s.__)("Yes, reset account","woocommerce")})})]})})}},22861:(e,t,o)=>{o.d(t,{$8:()=>n,Fx:()=>c,K7:()=>i,Lw:()=>r,bw:()=>s,eD:()=>a,nF:()=>l});const s="woocommerce-payments",n="woocommerce_payments",a="woopayments",r="_wc_pes_woopayments",c="settings_payments",i="lys",l="/wc-admin/settings/payments"},75854:(e,t,o)=>{o.d(t,{CX:()=>d,PK:()=>u}),o(51609);var s=o(27723),n=o(10432),a=o(98404),r=o(91289),c=o(33623),i=o(59530),l=o(8148),m=o(39793);const d="test_account",u=((0,s.__)("Choose your payment methods","woocommerce"),r.A,(0,s.sprintf)((0,s.__)("Connect with %s","woocommerce"),"WordPress.com"),n.A,(0,s.__)("Activate payments","woocommerce"),(0,s.__)("Test or live account","woocommerce"),i.A,(0,s.__)("Ready to test payments","woocommerce"),c.A,(0,s.__)("Activate payments","woocommerce"),a.A,(0,s.__)("Submit for verification","woocommerce"),l.A,[{id:"payment_methods",order:1,type:"backend",label:(0,s.__)("Choose your payment methods","woocommerce"),content:(0,m.jsx)(r.A,{})},{id:"wpcom_connection",order:2,type:"backend",label:(0,s.sprintf)((0,s.__)("Connect with %s","woocommerce"),"WordPress.com"),content:(0,m.jsx)(n.A,{}),dependencies:["payment_methods"]},{id:"business_verification",order:3,type:"backend",label:(0,s.__)("Activate payments","woocommerce"),dependencies:["wpcom_connection"],content:(0,m.jsx)(a.A,{})}])},1069:(e,t,o)=>{o.d(t,{LI:()=>u,TH:()=>p,TO:()=>_,W7:()=>h,j4:()=>c,js:()=>d,pF:()=>i,wJ:()=>l}),o(15703);var s=o(1455),n=o.n(s),a=o(83306),r=(o(96476),o(56109),o(22861));const c=e=>[r.Lw,r.$8,r.eD].includes(e),i=async()=>{try{return await n()({path:r.nF+"/woopayments/onboarding/reset",method:"POST"})}catch(e){throw e}},l=async()=>{try{return await n()({path:r.nF+"/woopayments/onboarding/test_account/disable",method:"POST"})}catch(e){throw e}},m=e=>t=>t.find((t=>t.id===e))||null,d=e=>{const t=m("apple_pay")(e),o=m("google_pay")(e);return t&&o?e.map((e=>"apple_pay"===e.id?{...e,id:"apple_google",extraTitle:o.title,extraDescription:o.description,extraIcon:o.icon}:"google_pay"===e.id?null:e)).filter((e=>null!==e)):e},u=e=>Object.keys(e).reduce(((t,o)=>("apple_pay"===o||"google_pay"===o?t.apple_google=e[o]:t[o]=e[o],t)),{}),_=(e,t)=>"primary"===e.category||!!e.enabled||null!=t&&t,p=(e,t={})=>{var o;e.startsWith("settings_payments_")||(e=`settings_payments_${e}`),t.business_country||(t.business_country=null!==(o=window.wcSettings?.admin?.woocommerce_payments_nox_profile?.business_country_code)&&void 0!==o?o:"unknown"),(0,a.recordEvent)(e,t)},h=(e,t={})=>{var o;e.startsWith("settings_payments_")||(e=`settings_payments_${e}`),t.business_country||(t.business_country=null!==(o=window.wcSettings?.admin?.woocommerce_payments_nox_profile?.business_country_code)&&void 0!==o?o:"unknown");const s=new URLSearchParams(window.location.search);t.source||(t.source=s.get("source")?.replace(/[^\w-]+/g,"")||"unknown"),(0,a.recordEvent)(e,t)}},51513:(e,t,o)=>{o.d(t,{$:()=>r});var s=o(84437),n=o(51609),a=o(42843);function r(e){const t=(0,s.d4)(e,(e=>{var t;return(0,a.Q)(null!==(t=e.getMeta())&&void 0!==t?t:void 0)})),[o,r]=(0,n.useState)(null);return(0,n.useEffect)((()=>{t?.component&&r((()=>t.component))}),[t?.component]),[o||null]}}}]);