"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[1226],{47804:(e,t,n)=>{n.d(t,{A:()=>i});var o=n(5573),s=n(39793);const i=(0,s.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(o.Path,{d:"M13 11.8l6.1-6.3-1-1-6.1 6.2-6.1-6.2-1 1 6.1 6.3-6.5 6.7 1 1 6.5-6.6 6.5 6.6 1-1z"})})},75753:(e,t,n)=>{n.d(t,{LO:()=>u,PE:()=>g,S:()=>_,CS:()=>h}),n(18982);var o=n(27723),s=n(56427),i=n(86087),a=n(47143),c=n(40314),r=n(96476),l=n(1069),d=n(22861),m=n(39793);const _=({gatewayProvider:e,settingsHref:t,onboardingHref:n,isOffline:_,acceptIncentive:u=()=>{},gatewayHasRecommendedPaymentMethods:g,installingPlugin:p,buttonText:h=(0,o.__)("Enable","woocommerce"),incentive:y=null,setOnboardingModalOpen:w,onboardingType:v})=>{const[x,b]=(0,i.useState)(!1),{createErrorNotice:f}=(0,a.dispatch)("core/notices"),{togglePaymentGateway:j,invalidateResolutionForStoreSelector:k}=(0,a.useDispatch)(c.paymentSettingsStore),N=()=>{f((0,o.__)("An error occurred. You will be redirected to the settings page, try enabling the payment gateway there.","woocommerce"),{type:"snackbar",explicitDismiss:!0})};return(0,m.jsx)(s.Button,{variant:"primary",isBusy:x,disabled:x||!!p,onClick:s=>{if(s.preventDefault(),e.state.enabled)return;(0,l.g2)("enable_click",e,{incentive_id:y?y.promo_id:"none"});const i=window.woocommerce_admin.nonces?.gateway_toggle||"";if(!i)return N(),void(window.location.href=t);b(!0),y&&u(y.promo_id),j(e.id,window.woocommerce_admin.ajax_url,i).then((s=>{if("needs_setup"===s.data)if(e.state.account_connected)f((0,o.__)("The provider could not be enabled. Check the Manage page for details.","woocommerce"),{type:"snackbar",explicitDismiss:!0,actions:[{label:(0,o.__)("Manage","woocommerce"),url:t}]}),(0,l.g2)("enable_failed",e,{reason:"needs_setup",incentive_id:y?y.promo_id:"none"});else if("native_in_context"===v&&w)(0,l.W7)("woopayments_onboarding_modal_opened",{from:"enable_gateway_button",source:d.Fx}),w(!0);else{if(!g)return void(window.location.href=n);(0,r.getHistory)().push((0,r.getNewPath)({},"/payment-methods"))}k("getPaymentProviders"),_&&k("getOfflinePaymentGateways"),b(!1)})).catch((()=>{(0,l.g2)("enable_failed",e,{reason:"error",incentive_id:y?y.promo_id:"none"}),b(!1),N(),window.location.href=t}))},href:t,children:h})},u=({acceptIncentive:e,installingPlugin:t,buttonText:n=(0,o.__)("Activate payments","woocommerce"),incentive:a=null,setOnboardingModalOpen:c,onboardingType:r})=>{const[_,u]=(0,i.useState)(!1);return(0,m.jsx)(s.Button,{variant:"primary",isBusy:_,disabled:_||!!t,onClick:()=>{u(!0),(0,l.TH)("activate_payments_button_click",{provider_id:d.$8,suggestion_id:d.eD,incentive_id:a?a.promo_id:"none",onboarding_type:r||"unknown",provider_extension_slug:d.bw}),(0,l.wJ)().then((()=>{a&&e(a.promo_id),u(!1),"native_in_context"===r?((0,l.W7)("woopayments_onboarding_modal_opened",{from:"activate_payments_button",source:d.Fx}),c(!0)):window.location.href=(0,l.ZV)()})).catch((()=>{u(!1)}))},children:n})},g=({gatewayProvider:e,settingsHref:t,onboardingHref:n,gatewayHasRecommendedPaymentMethods:_,installingPlugin:u,buttonText:g=(0,o.__)("Complete setup","woocommerce"),setOnboardingModalOpen:p,onboardingType:h,acceptIncentive:y=()=>{},incentive:w=null})=>{const[v,x]=(0,i.useState)(!1),{select:b}=(0,a.useSelect)((e=>({select:e})),[]),f=e.state.account_connected,j=e.onboarding.state.started,k=e.onboarding.state.completed;return(0,i.useEffect)((()=>{(0,l.j4)(e.id)&&"native_in_context"===h&&!k&&b(c.woopaymentsOnboardingStore).getOnboardingData()}),[e.id,k,h,b]),(0,m.jsx)(s.Button,{variant:"primary",isBusy:v,disabled:v||!!u,onClick:()=>{if((0,l.g2)("complete_setup_click",e),x(!0),w&&y(w.promo_id),"native_in_context"===h)(0,l.W7)("woopayments_onboarding_modal_opened",{from:"complete_setup_button",source:d.Fx}),p(!0);else{if(f&&j)return f&&j&&!k?void(window.location.href=n):void(window.location.href=t);if(!_)return void(window.location.href=n);(0,r.getHistory)().push((0,r.getNewPath)({},"/payment-methods"))}x(!1)},children:g},e.id)};var p=n(33068);const h=({gatewayProvider:e,settingsHref:t,isInstallingPlugin:n,buttonText:i=(0,o.__)("Manage","woocommerce")})=>{const r=e._type===c.PaymentsProviderType.OfflinePm,d=(0,p.Zp)(),{invalidateResolutionForStoreSelector:_}=(0,a.useDispatch)(c.paymentGatewaysStore);return(0,m.jsx)(s.Button,{variant:"secondary",href:r?void 0:t,disabled:n,onClick:()=>{(0,l.g2)("provider_manage_click",e),r&&(_("getPaymentGateway"),d((0,l.Wg)(t)))},children:i})}},68345:(e,t,n)=>{n.d(t,{M:()=>m});var o=n(27723),s=n(56427),i=n(86087),a=n(47143),c=n(40314),r=n(1069),l=n(22861),d=n(39793);const m=({isOpen:e,onClose:t,isTestMode:n,isEmbeddedResetFlow:m=!1})=>{const[_,u]=(0,i.useState)(!1),{invalidateResolutionForStoreSelector:g}=(0,a.useDispatch)(c.paymentSettingsStore),{invalidateResolutionForStoreSelector:p}=(0,a.useDispatch)(c.woopaymentsOnboardingStore),{createNotice:h}=(0,a.useDispatch)("core/notices");let y=n?(0,o.sprintf)((0,o.__)("When you reset your test account, all payment data — including your %s account details, test transactions, and payouts history — will be lost. Your order history will remain. This action cannot be undone, but you can create a new test account at any time.","woocommerce"),"WooPayments"):(0,o.sprintf)((0,o.__)("When you reset your account, all payment data — including your %s account details, test transactions, and payouts history — will be lost. Your order history will remain. This action cannot be undone, but you can create a new test account at any time.","woocommerce"),"WooPayments");return m&&(y=(0,o.sprintf)((0,o.__)("You need to reset your test account to continue onboarding with %1$s. This will create a new test account and reset any existing %2$s account details and test transactions.","woocommerce"),"WooPayments","WooPayments")),(0,d.jsx)(d.Fragment,{children:e&&(0,d.jsxs)(s.Modal,{title:(0,o.__)("Reset your test account","woocommerce"),className:"woocommerce-woopayments-modal",isDismissible:!0,onRequestClose:t,children:[(0,d.jsxs)("div",{className:"woocommerce-woopayments-modal__content",children:[(0,d.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,d.jsx)("div",{children:(0,d.jsx)("span",{children:y})})}),(0,d.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,d.jsx)("h3",{children:(0,o.__)("Are you sure you'd like to continue?","woocommerce")})})]}),(0,d.jsx)("div",{className:"woocommerce-woopayments-modal__actions",children:(0,d.jsx)(s.Button,{className:m?"":"danger",variant:m?"primary":"secondary",isBusy:_,disabled:_,onClick:()=>{(0,r.TH)("provider_reset_onboarding_confirmation_click",{provider_id:l.$8,suggestion_id:l.eD,provider_extension_slug:l.bw}),u(!0),(0,r.pF)().then((()=>{(0,r.TH)("provider_reset_onboarding_success",{provider_id:l.$8,suggestion_id:l.eD,provider_extension_slug:l.bw}),g("getPaymentProviders"),p("getOnboardingData")})).catch((()=>{(0,r.TH)("provider_reset_onboarding_failed",{provider_id:l.$8,suggestion_id:l.eD,provider_extension_slug:l.bw,reason:"error"}),h("error",(0,o.__)("Failed to reset your WooPayments account.","woocommerce"),{isDismissible:!0})})).finally((()=>{u(!1),t()}))},children:(0,o.__)("Yes, reset account","woocommerce")})})]})})}},75854:(e,t,n)=>{n.d(t,{CX:()=>m,Ci:()=>_}),n(51609);var o=n(27723),s=n(10432),i=n(98404),a=n(91289),c=n(33623),r=n(59530),l=n(8148),d=n(39793);const m="test_account",_=[{id:"payment_methods",order:1,type:"backend",label:(0,o.__)("Choose your payment methods","woocommerce"),content:(0,d.jsx)(a.A,{})},{id:"wpcom_connection",order:2,type:"backend",label:(0,o.sprintf)((0,o.__)("Connect with %s","woocommerce"),"WordPress.com"),content:(0,d.jsx)(s.A,{}),dependencies:["payment_methods"]},{id:"activate_payments",order:3,type:"frontend",label:(0,o.__)("Activate payments","woocommerce"),subSteps:[{id:"test_or_live_account",order:1,type:"frontend",label:(0,o.__)("Test or live account","woocommerce"),dependencies:["wpcom_connection"],content:(0,d.jsx)(r.A,{})},{id:m,order:2,type:"backend",label:(0,o.__)("Ready to test payments","woocommerce"),dependencies:["test_or_live_account"],content:(0,d.jsx)(c.A,{})},{id:"business_verification",order:3,type:"backend",label:(0,o.__)("Activate payments","woocommerce"),dependencies:["test_or_live_account"],content:(0,d.jsx)(i.A,{})}]},{id:"finish",order:4,type:"frontend",label:(0,o.__)("Submit for verification","woocommerce"),dependencies:["business_verification"],content:(0,d.jsx)(l.A,{})}];(0,o.__)("Choose your payment methods","woocommerce"),a.A,(0,o.sprintf)((0,o.__)("Connect with %s","woocommerce"),"WordPress.com"),s.A,(0,o.__)("Activate payments","woocommerce"),i.A},41161:(e,t,n)=>{n.r(t),n.d(t,{SettingsPaymentsMain:()=>ve,default:()=>xe});var o=n(51609),s=n.n(o),i=n(27723),a=n(40314),c=n(47143),r=n(86087),l=n(1455),d=n.n(l),m=n(96476),_=n(15703);function u(e){const{createNotice:t}=(0,c.dispatch)("core/notices");e.error_data&&e.errors&&Object.keys(e.errors).length?Object.keys(e.errors).forEach((n=>{t("error",e.errors[n].join(" "))})):e.message&&t(e.code?"error":"success",e.message)}var g=n(72553),p=n(56427),h=n(18537),y=n(39793);const w=()=>(0,y.jsxs)("div",{className:"other-payment-gateways__content__grid-item",children:[(0,y.jsx)("div",{className:"grid-item-placeholder__img"}),(0,y.jsxs)("div",{className:"other-payment-gateways__content__grid-item__content grid-item-placeholder__content",children:[(0,y.jsx)("span",{className:"grid-item-placeholder__title"}),(0,y.jsx)("span",{className:"grid-item-placeholder__description"}),(0,y.jsx)("div",{className:"grid-item-placeholder__actions"})]})]});var v=n(98846),x=n(56109),b=n(1069);const f=({variant:e,suggestionId:t})=>{const[n,o]=(0,r.useState)(!1),s=(0,r.useRef)(null),a=e=>{const n=e.target.closest(".woocommerce-official-extension-badge__container");s.current&&n!==s.current||(o((e=>!e)),(0,b.TH)("official_badge_click",{suggestion_id:t}))};return(0,y.jsx)(v.Pill,{className:"woocommerce-official-extension-badge",children:(0,y.jsxs)("span",{className:"woocommerce-official-extension-badge__container",tabIndex:0,role:"button",ref:s,onClick:a,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||a(e)},children:[(0,y.jsx)("img",{src:x.GZ+"images/icons/official-extension.svg",alt:(0,i.__)("Official WooCommerce extension badge","woocommerce")}),"expanded"===e&&(0,y.jsx)("span",{children:(0,i.__)("Official","woocommerce")}),n&&(0,y.jsx)(p.Popover,{className:"woocommerce-official-extension-badge-popover",placement:"top-start",offset:4,variant:"unstyled",focusOnMount:!0,noArrow:!0,shift:!0,onFocusOutside:()=>{o(!1)},children:(0,y.jsx)("div",{className:"components-popover__content-container",children:(0,y.jsx)("p",{children:(0,r.createInterpolateElement)((0,i.__)("This is an Official WooCommerce payment extension. <learnMoreLink />","woocommerce"),{learnMoreLink:(0,y.jsx)(v.Link,{href:"https://woocommerce.com/learn-more-about-official-partner-badging/",target:"_blank",rel:"noreferrer",type:"external",onClick:()=>{(0,b.TH)("official_badge_learn_more_click",{suggestion_id:t})},children:(0,i.__)("Learn more","woocommerce")})})})})})]})})};var j=n(24148),k=n(73290);const N=({status:e,message:t,popoverContent:n})=>{const[o,s]=(0,r.useState)(!1),a=(0,r.useRef)(null),c=e=>{const t=e.target.closest(".woocommerce-status-badge__icon-container");a.current&&t!==a.current||s((e=>!e))};return(0,y.jsxs)(v.Pill,{className:`woocommerce-status-badge ${(()=>{switch(e){case"active":case"has_incentive":return"woocommerce-status-badge--success";case"needs_setup":case"test_mode":case"test_account":return"woocommerce-status-badge--warning";case"recommended":case"inactive":return"woocommerce-status-badge--info";default:return""}})()}`,children:[t||(()=>{switch(e){case"active":return(0,i.__)("Active","woocommerce");case"inactive":return(0,i.__)("Inactive","woocommerce");case"needs_setup":return(0,i.__)("Action needed","woocommerce");case"test_mode":return(0,i.__)("Test mode","woocommerce");case"test_account":return(0,i.__)("Test account","woocommerce");case"recommended":return(0,i.__)("Recommended","woocommerce");default:return""}})(),n&&(0,y.jsxs)("span",{className:"woocommerce-status-badge__icon-container",tabIndex:0,role:"button",ref:a,onClick:c,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||c(e)},children:[(0,y.jsx)(j.A,{className:"woocommerce-status-badge-icon",size:16,icon:k.A}),o&&(0,y.jsx)(p.Popover,{className:"woocommerce-status-badge-popover",placement:"top-start",offset:4,variant:"unstyled",focusOnMount:!0,noArrow:!0,shift:!0,onFocusOutside:()=>{s(!1)},children:(0,y.jsx)("div",{className:"components-popover__content-container",children:n})})]})]})},P=({incentive:e})=>(0,y.jsx)(N,{status:"has_incentive",message:e.badge,popoverContent:(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)("p",{className:"woocommerce-incentive-popover__title",children:e.title}),(0,y.jsx)("p",{className:"woocommerce-incentive-popover__terms",children:(0,r.createInterpolateElement)((0,i.__)("See <termsLink /> for details.","woocommerce"),{termsLink:(0,y.jsx)(v.Link,{href:e.tc_url,target:"_blank",rel:"noreferrer",type:"external",children:(0,i.__)("Terms and Conditions","woocommerce")})})})]})}),S=({suggestions:e,suggestionCategories:t,installingPlugin:n,setUpPlugin:o,isFetching:s,morePaymentOptionsLink:a})=>{const c=new URLSearchParams(window.location.search),l="expanded"===c.get("other_pes_section"),[d,m]=(0,r.useState)(l),[_,u]=(0,r.useState)(""),v=(0,r.useRef)({}),x=(e,t)=>{var n;const o=e.target.closest(".other-payment-gateways__content__title__icon-container"),s=null!==(n=v.current[t])&&void 0!==n?n:null;s&&o!==s||u(t===_?"":t)},j=()=>{u("")},k=()=>{const e=!d;(0,b.TH)("other_payment_options_section_click",{action:e?"expand":"collapse"}),m(e),c.set("other_pes_section",e?"expanded":"collapsed"),window.history.replaceState({},document.title,window.location.pathname+"?"+c.toString())},N=(0,r.useMemo)((()=>t.map((t=>({category:t,suggestions:e.filter((e=>e._type===t.id))})))),[e,t]),S=(0,r.useMemo)((()=>s?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)("div",{className:"other-payment-gateways__header__title-image-placeholder"}),(0,y.jsx)("div",{className:"other-payment-gateways__header__title-image-placeholder"}),(0,y.jsx)("div",{className:"other-payment-gateways__header__title-image-placeholder"})]}):N.map((({suggestions:e})=>0===e.length?null:e.map((e=>(0,y.jsx)("img",{src:e.icon,alt:e.title+" small logo",width:"24",height:"24",className:"other-payment-gateways__header__title-image"},e.id)))))),[N,s]),C=(0,r.useMemo)((()=>s?(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(w,{}),(0,y.jsx)(w,{}),(0,y.jsx)(w,{})]}):N.map((({category:e,suggestions:t})=>0===t.length?null:(0,y.jsxs)("div",{className:"other-payment-gateways__content__category-container",children:[(0,y.jsxs)("div",{className:"other-payment-gateways__content__title",children:[(0,y.jsx)("h3",{className:"other-payment-gateways__content__title__h3",children:(0,h.decodeEntities)(e.title)}),(0,y.jsxs)("span",{className:"other-payment-gateways__content__title__icon-container",onClick:t=>x(t,e.id),onKeyDown:t=>{"Enter"!==t.key&&" "!==t.key||x(t,e.id)},tabIndex:0,role:"button",ref:t=>{v.current[e.id]=t},children:[(0,y.jsx)(g.A,{icon:"info-outline",className:"other-payment-gateways__content__title__icon"}),e.id===_&&(0,y.jsx)(p.Popover,{className:"other-payment-gateways__content__title-popover",placement:"top-start",offset:4,variant:"unstyled",focusOnMount:!0,noArrow:!0,shift:!0,onFocusOutside:j,children:(0,y.jsx)("div",{className:"components-popover__content-container",children:(0,y.jsx)("p",{children:(0,h.decodeEntities)(e.description)})})})]})]}),(0,y.jsx)("div",{className:"other-payment-gateways__content__grid",children:t.map((e=>(0,y.jsxs)("div",{className:"other-payment-gateways__content__grid-item",children:[(0,y.jsx)("img",{className:"other-payment-gateways__content__grid-item-image",src:e.icon,alt:(0,h.decodeEntities)(e.title)+" logo"}),(0,y.jsxs)("div",{className:"other-payment-gateways__content__grid-item__content",children:[(0,y.jsxs)("span",{className:"other-payment-gateways__content__grid-item__content__title",children:[e.title,e?._incentive&&(0,y.jsx)(P,{incentive:e._incentive}),(0,y.jsx)(f,{variant:"expanded",suggestionId:e.id})]}),(0,y.jsx)("span",{className:"other-payment-gateways__content__grid-item__content__description",children:(0,h.decodeEntities)(e.description)}),(0,y.jsx)("div",{className:"other-payment-gateways__content__grid-item__content__actions",children:(0,y.jsx)(p.Button,{variant:"link",onClick:()=>{var t;return o(e,null,"not_installed"===e.plugin.status&&null!==(t=e._links?.attach?.href)&&void 0!==t?t:null,"wc_settings_payments__other_payment_options")},isBusy:n===e.id,disabled:!!n,children:n===e.id?(0,i.__)("Installing","woocommerce"):(0,i.__)("Install","woocommerce")})})]})]},e.id)))})]},e.id)))),[N,n,o,s,_]);return(0,y.jsxs)("div",{className:"other-payment-gateways"+(d?" is-expanded":""),children:[(0,y.jsxs)("div",{className:"other-payment-gateways__header",onClick:k,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||k()},role:"button",tabIndex:0,"aria-expanded":d,children:[(0,y.jsxs)("div",{className:"other-payment-gateways__header__title",children:[(0,y.jsx)("span",{children:(0,i.__)("Other payment options","woocommerce")}),!d&&(0,y.jsx)(y.Fragment,{children:S})]}),(0,y.jsx)(g.A,{className:"other-payment-gateways__header__arrow",icon:d?"chevron-up":"chevron-down"})]}),d&&(0,y.jsxs)("div",{className:"other-payment-gateways__content",children:[C,(0,y.jsx)("div",{className:"other-payment-gateways__content__external-icon",children:a})]})]})};var C=n(4921),O=n(46608),T=n(36849),I=n(21913),A=n(29491),M=n(90700),E=n(72744);const D=(e,t)=>{const n=t,{changes:o,type:s,props:i}=n,{items:a}=i,{selectedItem:c}=e;switch(s){case I.WM.stateChangeTypes.ItemClick:return{...o,isOpen:!0,highlightedIndex:e.highlightedIndex};case I.WM.stateChangeTypes.ToggleButtonKeyDownArrowDown:return{selectedItem:a[c?Math.min(a.indexOf(c)+1,a.length-1):0],isOpen:!0};case I.WM.stateChangeTypes.ToggleButtonKeyDownArrowUp:return{selectedItem:a[c?Math.max(a.indexOf(c)-1,0):a.length-1],isOpen:!0};default:return o}},H=e=>e.normalize("NFD").replace(/[\u0300-\u036f]/g,""),B=({name:e,className:t,label:n,describedBy:s,options:a,onChange:c,value:l,placeholder:d,children:m})=>{var _;const[u,g]=(0,o.useState)(""),h=(0,A.useThrottle)((0,r.useCallback)(((e,t)=>new Set(t.filter((t=>{var n;return`${H(null!==(n=t.name)&&void 0!==n?n:"")}`.toLowerCase().includes(H(e.toLowerCase()))})))),[]),200),w=""!==u?null!==(_=h(u,a))&&void 0!==_?_:new Set:new Set(a),{getToggleButtonProps:v,getMenuProps:b,getItemProps:f,isOpen:k,highlightedIndex:N,selectedItem:P,closeMenu:S}=(0,I.WM)({initialSelectedItem:l,items:[...w],stateReducer:D}),O=((e,t)=>{const n=t.find((t=>t.key===e));return n?.name?n.name:""})(l.key,a),T=P?P.key:"",B=(0,o.useRef)(null),F=(0,o.useRef)(null),L=(0,r.useCallback)((e=>{const t=B.current,n=t?.querySelector(`[data-index="${e}"]`);n&&n.scrollIntoView({block:"nearest"})}),[B]),R=""!==u,W=b({className:"components-country-select-control__menu","aria-hidden":!k,ref:B}),U=(0,r.useCallback)((e=>{e.stopPropagation(),c(T),S()}),[c,T,S]),G=(0,r.useCallback)((e=>{e.stopPropagation(),"Enter"===e.key&&c(T)}),[c,T]),$=(0,r.useCallback)((e=>{e.preventDefault(),""!==u&&g(""),null!==P&&setTimeout((()=>{L(a.indexOf(P))}),10)}),[u,P]);return(0,r.useEffect)((()=>{if(k&&null!==P){const e=Array.from(w).indexOf(P);L(e)}}),[k]),(0,y.jsxs)("div",{className:(0,C.A)("woopayments components-country-select-control",t),children:[(0,y.jsxs)(p.Button,{...v({"aria-label":n,"aria-labelledby":void 0,"aria-describedby":s||(O?(0,i.sprintf)((0,i.__)("Currently selected: %s","woocommerce"),O):(0,i.__)("No selection","woocommerce")),className:(0,C.A)("components-country-select-control__button",{placeholder:!O}),name:e,onKeyDown:G}),children:[(0,y.jsxs)("span",{className:"components-country-select-control__button-value",children:[(0,y.jsx)("span",{className:"components-country-select-control__label",children:n}),O||d]}),(0,y.jsx)(j.A,{icon:M.A,className:"components-custom-select-control__button-icon"})]}),(0,y.jsx)("div",{...W,children:k&&(0,y.jsxs)(y.Fragment,{children:[(0,y.jsxs)("div",{className:"components-country-select-control__search wc-settings-prevent-change-event",children:[(0,y.jsx)("input",{className:"components-country-select-control__search--input",ref:F,type:"text",value:u,onChange:({target:e})=>g(e.value),tabIndex:-1,placeholder:(0,i.__)("Search","woocommerce")}),(0,y.jsx)("button",{className:"components-country-select-control__search--input-suffix",onClick:$,children:(Z=R,Z?(0,y.jsx)("img",{src:x.GZ+"images/icons/clear.svg",alt:(0,i.__)("Clear search","woocommerce")}):(0,y.jsx)("img",{src:x.GZ+"images/icons/search.svg",alt:(0,i.__)("Search","woocommerce")}))})]}),(0,y.jsx)("div",{className:"components-country-select-control__list",children:[...w].map(((e,t)=>(0,o.createElement)("div",{...f({item:e,index:t,key:e.key,className:(0,C.A)(e.className,"components-country-select-control__item",{"is-highlighted":t===N}),"data-index":t,style:e.style}),key:e.key},e.key===T&&(0,y.jsx)(j.A,{icon:E.A,className:"components-country-select-control__item-icon"}),m?m(e):e.name)))}),(0,y.jsx)("div",{className:"components-country-select-control__apply",children:(0,y.jsx)("button",{className:"components-button is-primary",onClick:U,children:(0,i.__)("Apply","woocommerce")})})]})})]});var Z};var F=n(51881),L=n(33068),R=n(15698),W=n(85816),U=n(12974);const G=({provider:e,pluginFile:t,isSuggestion:n,onToggle:o,links:s=[],canResetAccount:l=!1,setResetAccountModalVisible:d=()=>{},isEnabled:m=!1})=>{const{deactivatePlugin:_}=(0,c.useDispatch)(a.pluginsStore),[u,g]=(0,r.useState)(!1),[h,w]=(0,r.useState)(!1),[v,x]=(0,r.useState)(!1),{invalidateResolutionForStoreSelector:f,togglePaymentGateway:j,hidePaymentExtensionSuggestion:k}=(0,c.useDispatch)(a.paymentSettingsStore),{createErrorNotice:N,createSuccessNotice:P}=(0,c.useDispatch)("core/notices"),S={pricing:(0,i.__)("See pricing & fees","woocommerce"),about:(0,i.__)("Learn more","woocommerce"),terms:(0,i.__)("See Terms of Service","woocommerce"),support:(0,i.__)("Get support","woocommerce"),documentation:(0,i.__)("View documentation","woocommerce")},C=s.filter((e=>{switch(e._type){case"pricing":return!0;case"terms":case"about":return!m;case"documentation":case"support":return m;default:return!1}}));return(0,y.jsxs)(y.Fragment,{children:[C.map((t=>{const n=S[t._type];return n?(0,y.jsx)("div",{className:"woocommerce-ellipsis-menu__content__item",children:(0,y.jsx)(p.Button,{target:"_blank",href:t.url,onClick:()=>{(0,b.g2)("context_link_click",e,{link_type:t._type,link_url:t.url})},children:n})},t._type):null})),!!C.length&&(0,y.jsx)(p.CardDivider,{}),n&&(0,y.jsx)("div",{className:"woocommerce-ellipsis-menu__content__item",children:(0,y.jsx)(p.Button,{onClick:()=>{(0,b.g2)("context_link_click",e,{link_type:"hide_suggestion"}),(()=>{const t=e._links?.hide?.href;t?(x(!0),k(t).then((()=>{f("getPaymentProviders"),x(!1),o()})).catch((()=>{N((0,i.__)("Failed to hide the payments extension suggestion.","woocommerce")),x(!1),o()}))):N((0,i.__)("Failed to hide the payments extension suggestion.","woocommerce"))})()},isBusy:v,disabled:v,children:(0,i.__)("Hide suggestion","woocommerce")})},"hide-suggestion"),l&&(0,y.jsx)("div",{className:"woocommerce-ellipsis-menu__content__item",children:(0,y.jsx)(p.Button,{onClick:()=>{(0,b.g2)("context_link_click",e,{link_type:"reset_onboarding"}),d(!0),o()},className:"components-button__danger",children:(0,i.__)("Reset account","woocommerce")})},"reset-account"),!n&&!m&&(0,y.jsx)("div",{className:"woocommerce-ellipsis-menu__content__item",children:(0,y.jsx)(p.Button,{className:"components-button__danger",onClick:()=>{(0,b.g2)("context_link_click",e,{link_type:"deactivate_extension"}),g(!0),_(t).then((()=>{P((0,i.__)("The provider plugin was successfully deactivated.","woocommerce")),f("getPaymentProviders"),g(!1),o()})).catch((()=>{(0,b.g2)("extension_deactivation_failed",e,{reason:"error"}),N((0,i.__)("Failed to deactivate the provider plugin.","woocommerce")),g(!1),o()}))},isBusy:u,disabled:!t||u,children:(0,i.__)("Deactivate","woocommerce")})},"deactivate"),!n&&m&&(0,y.jsx)("div",{className:"woocommerce-ellipsis-menu__content__item",children:(0,y.jsx)(p.Button,{className:"components-button__danger",onClick:()=>{(0,b.g2)("context_link_click",e,{link_type:"disable"}),(()=>{const t=window.woocommerce_admin.nonces?.gateway_toggle||"";if(!t)return(0,b.g2)("disable_failed",e,{reason:"missing_nonce"}),void N((0,i.__)("Failed to disable the payments provider.","woocommerce"));w(!0),j(e.id,window.woocommerce_admin.ajax_url,t).then((()=>{f("getPaymentProviders"),w(!1),o()})).catch((()=>{(0,b.g2)("disable_failed",e,{reason:"error"}),N((0,i.__)("Failed to disable the payments provider.","woocommerce")),w(!1),o()}))})()},isBusy:h,disabled:h,children:(0,i.__)("Disable","woocommerce")})},"disable")]})};var $=n(68345),Z=n(22861);const q=({isOpen:e,devMode:t,onClose:n})=>{const[o,s]=(0,r.useState)(!1),[a,c]=(0,r.useState)(!1);return(0,y.jsx)(y.Fragment,{children:e&&(0,y.jsxs)(p.Modal,{title:(0,i.__)("You're ready to test payments!","woocommerce"),className:"woocommerce-woopayments-modal",isDismissible:!0,onRequestClose:n,children:[(0,y.jsxs)("div",{className:"woocommerce-woopayments-modal__content",children:[(0,y.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,y.jsx)("div",{className:"woocommerce-woopayments-modal__content__item__description",children:(0,y.jsx)("p",{children:(0,T.A)({mixedString:(0,i.__)("We've created a test account for you so that you can begin testing payments on your store. {{break/}}Not sure what to test? Take a look at {{link}}how to test payments{{/link}}.","woocommerce"),components:{link:(0,y.jsx)(v.Link,{href:"https://woocommerce.com/document/woopayments/testing-and-troubleshooting/sandbox-mode/",target:"_blank",rel:"noreferrer",type:"external"}),break:(0,y.jsx)("br",{})}})})})}),(0,y.jsx)("div",{className:"woocommerce-woopayments-modal__content__item",children:(0,y.jsx)("h2",{children:(0,i.__)("What's next:","woocommerce")})}),(0,y.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex",children:[(0,y.jsx)("img",{src:x.GZ+"images/icons/store.svg",alt:"",role:"presentation"}),(0,y.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex__description",children:[(0,y.jsx)("h3",{children:(0,i.__)("Continue your store setup","woocommerce")}),(0,y.jsx)("div",{children:(0,i.__)("Finish completing the tasks required to launch your store.","woocommerce")})]})]}),!t&&(0,y.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex",children:[(0,y.jsx)("img",{src:x.GZ+"images/icons/dollar.svg",alt:"",role:"presentation"}),(0,y.jsxs)("div",{className:"woocommerce-woopayments-modal__content__item-flex__description",children:[(0,y.jsx)("h3",{children:(0,i.__)("Activate payments","woocommerce")}),(0,y.jsx)("div",{children:(0,y.jsx)("p",{children:(0,T.A)({mixedString:(0,i.__)("Provide some additional details about your business so you can begin accepting real payments. {{link}}Learn more{{/link}}","woocommerce"),components:{link:(0,y.jsx)(v.Link,{href:"https://woocommerce.com/document/woopayments/startup-guide/#sign-up-process",target:"_blank",rel:"noreferrer",type:"external"})}})})})]})]})]}),(0,y.jsxs)("div",{className:"woocommerce-woopayments-modal__actions",children:[(0,y.jsx)(p.Button,{variant:"primary",isBusy:a,disabled:a,onClick:()=>{(0,b.TH)("continue_store_setup_click",{provider_id:Z.$8,suggestion_id:Z.eD,provider_extension_slug:Z.bw}),c(!0),window.location.href=(0,_.getAdminLink)("admin.php?page=wc-admin")},children:(0,i.__)("Continue store setup","woocommerce")}),!t&&(0,y.jsx)(p.Button,{variant:"secondary",isBusy:o,disabled:o,onClick:()=>{(0,b.TH)("switch_to_live_account_click",{provider_id:Z.$8,suggestion_id:Z.eD,provider_extension_slug:Z.bw}),s(!0),window.location.href=(0,b.ZV)()},children:(0,i.__)("Activate payments","woocommerce")})]})]})})},z=({provider:e,label:t})=>{const[n,o]=(0,r.useState)(!1),s=(0,b.j4)(e.id)&&"gateway"===e._type&&e.state?.account_connected&&(e.onboarding?.state?.test_mode||!e.onboarding?.state?.completed);return(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(v.EllipsisMenu,{label:t,renderContent:({onToggle:t})=>(0,y.jsx)(G,{provider:e,pluginFile:e.plugin.file,isSuggestion:"suggestion"===e._type,links:e.links,onToggle:t,isEnabled:e.state?.enabled,canResetAccount:s,setResetAccountModalVisible:o}),focusOnMount:!0}),(0,y.jsx)($.M,{isOpen:n,onClose:()=>o(!1),isTestMode:e.onboarding?.state?.test_mode})]})},K=({suggestion:e,installingPlugin:t,setUpPlugin:n,pluginInstalled:o,acceptIncentive:s,shouldHighlightIncentive:a=!1,...c})=>{const r=(0,b.O2)(e)?e._incentive:null;let l=(0,i.__)("Install","woocommerce");return o?l=(0,i.__)("Enable","woocommerce"):t===e.id&&(l=(0,i.__)("Installing","woocommerce")),(0,y.jsx)("div",{id:e.id,className:"transitions-disabled woocommerce-list__item woocommerce-list__item-enter-done "+((0,b.O2)(e)&&a?"has-incentive":""),...c,children:(0,y.jsxs)("div",{className:"woocommerce-list__item-inner",children:[(0,y.jsxs)("div",{className:"woocommerce-list__item-before",children:[(0,y.jsx)(R.Gh,{}),e.icon&&(0,y.jsx)("img",{className:"woocommerce-list__item-image",src:e.icon,alt:e.title+" logo"})]}),(0,y.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,y.jsxs)("span",{className:"woocommerce-list__item-title",children:[e.title," ",!(0,b.O2)(e)&&(0,b.j4)(e.id)&&(0,y.jsx)(N,{status:"recommended"}),r&&(0,y.jsx)(P,{incentive:r}),(0,y.jsx)(f,{variant:"expanded",suggestionId:e.id})]}),(0,y.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,U.Ay)((0,h.decodeEntities)(e.description))}),(0,b.j4)(e.id)&&(0,y.jsx)(W.WooPaymentsMethodsLogos,{maxElements:10,tabletWidthBreakpoint:1080,mobileWidthBreakpoint:768,isWooPayEligible:(0,b.bU)(e)})]}),(0,y.jsx)("div",{className:"woocommerce-list__item-buttons",children:(0,y.jsx)("div",{className:"woocommerce-list__item-buttons__actions",children:(0,y.jsx)(p.Button,{variant:"primary",onClick:()=>{var t,i;o&&(0,b.g2)("enable_click",e,{incentive_id:r?r.promo_id:"none"}),r&&s(r.promo_id),n(e,null!==(t=e.onboarding?._links?.onboard?.href)&&void 0!==t?t:null,o?null:null!==(i=e._links?.attach?.href)&&void 0!==i?i:null,"wc_settings_payments__main_suggestion")},isBusy:t===e.id,disabled:!!t,children:l})})}),(0,y.jsx)("div",{className:"woocommerce-list__item-after",children:(0,y.jsx)("div",{className:"woocommerce-list__item-after__actions",children:(0,y.jsx)(z,{label:(0,i.__)("Payment provider actions","woocommerce"),provider:e})})})]})})};var V=n(75753);const Y=({buttonText:e=(0,i.__)("Reactivate payments","woocommerce"),settingsHref:t})=>{const[n,o]=(0,r.useState)(!1),{createSuccessNotice:s,createErrorNotice:l}=(0,c.dispatch)("core/notices"),{invalidateResolutionForStoreSelector:m}=(0,c.useDispatch)(a.paymentSettingsStore);return(0,y.jsx)(p.Button,{variant:"primary",isBusy:n,disabled:n,onClick:e=>{e.preventDefault(),o(!0),(0,b.TH)("reactivate_payments_button_click",{provider_id:Z.$8,provider_extension_slug:Z.bw,suggestion_id:Z.eD}),d()({path:"/wc/v3/payments/settings",method:"POST",data:{is_test_mode_enabled:!1}}).then((()=>{s((0,i.sprintf)((0,i.__)("%s is now processing live payments (real payment methods and charges).","woocommerce"),"WooPayments"),{type:"snackbar",explicitDismiss:!1}),m("getPaymentProviders"),o(!1)})).catch((()=>{o(!1),(0,b.TH)("reactivate_payments_error",{provider_id:Z.$8,provider_extension_slug:Z.bw,suggestion_id:Z.eD}),l((0,i.sprintf)((0,i.__)("An error occurred. You will be redirected to the %s settings page to manage payments processing mode from there.","woocommerce"),"WooPayments"),{type:"snackbar",explicitDismiss:!0}),window.location.href=t}))},href:t,children:e})},J=({gateway:e,installingPlugin:t,acceptIncentive:n,shouldHighlightIncentive:o,setIsOnboardingModalOpen:s,...a})=>{var c;const r=(0,b.j4)(e.id),l=(0,b.O2)(e)?e._incentive:null,d=(null!==(c=e.onboarding.recommended_payment_methods)&&void 0!==c?c:[]).length>0,m=!e.state.account_connected||e.state.account_connected&&!e.onboarding.state.started||e.state.account_connected&&e.onboarding.state.started&&!e.onboarding.state.completed;return(0,y.jsx)("div",{id:e.id,className:`transitions-disabled woocommerce-list__item woocommerce-list__item-enter-done woocommerce-item__payment-gateway ${r?"woocommerce-item__woocommerce-payments":""} ${(0,b.O2)(e)&&o?"has-incentive":""}`,...a,children:(0,y.jsxs)("div",{className:"woocommerce-list__item-inner",children:[(0,y.jsxs)("div",{className:"woocommerce-list__item-before",children:[(0,y.jsx)(R.Gh,{}),e.icon&&(0,y.jsx)("img",{className:"woocommerce-list__item-image",src:e.icon,alt:e.title+" logo"})]}),(0,y.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,y.jsxs)("span",{className:"woocommerce-list__item-title",children:[e.title,l?(0,y.jsx)(P,{incentive:l}):(0,y.jsx)(N,{status:(()=>{if(m||!e.state.enabled&&e.state.needs_setup)return"needs_setup";if(e.state.enabled){if(e.state.account_connected){if(e.onboarding.state.test_mode)return"test_account";if(e.state.test_mode)return"test_mode"}return"active"}return"inactive"})()}),e._suggestion_id&&(0,y.jsx)(f,{variant:"expanded",suggestionId:e._suggestion_id}),e.supports?.includes("subscriptions")&&(0,y.jsx)(p.Tooltip,{placement:"top",text:(0,i.__)("Supports recurring payments","woocommerce"),children:(0,y.jsx)("img",{className:"woocommerce-list__item-recurring-payments-icon",src:x.GZ+"images/icons/recurring-payments.svg",alt:(0,i.__)("Icon to indicate support for recurring payments","woocommerce")})})]}),(0,y.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,U.Ay)((0,h.decodeEntities)(e.description))}),r&&(0,y.jsx)(W.WooPaymentsMethodsLogos,{maxElements:10,tabletWidthBreakpoint:1080,mobileWidthBreakpoint:768,isWooPayEligible:(0,b.bU)(e)})]}),(0,y.jsx)("div",{className:"woocommerce-list__item-buttons",children:(0,y.jsxs)("div",{className:"woocommerce-list__item-buttons__actions",children:[!e.state.enabled&&!m&&(0,y.jsx)(V.S,{gatewayProvider:e,settingsHref:e.management._links.settings.href,onboardingHref:e.onboarding._links.onboard.href,isOffline:!1,gatewayHasRecommendedPaymentMethods:d,installingPlugin:t,incentive:l,acceptIncentive:n,setOnboardingModalOpen:s,onboardingType:e.onboarding.type}),!m&&(0,y.jsx)(V.CS,{gatewayProvider:e,settingsHref:e.management._links.settings.href,isInstallingPlugin:!!t}),m&&(0,y.jsx)(V.PE,{gatewayProvider:e,settingsHref:e.management._links.settings.href,onboardingHref:e.onboarding._links.onboard.href,gatewayHasRecommendedPaymentMethods:d,installingPlugin:t,setOnboardingModalOpen:s,onboardingType:e.onboarding.type,incentive:l,acceptIncentive:n}),(0,b.j4)(e.id)&&!e.state.dev_mode&&e.state.account_connected&&e.onboarding.state.completed&&e.onboarding.state.test_mode&&(0,y.jsx)(V.LO,{acceptIncentive:n,installingPlugin:t,incentive:l,setOnboardingModalOpen:s,onboardingType:e.onboarding.type}),(0,b.j4)(e.id)&&!e.state.dev_mode&&e.state.account_connected&&e.onboarding.state.completed&&!e.onboarding.state.test_mode&&e.state.test_mode&&(0,y.jsx)(Y,{settingsHref:e.management._links.settings.href})]})}),(0,y.jsx)("div",{className:"woocommerce-list__item-after",children:(0,y.jsx)("div",{className:"woocommerce-list__item-after__actions",children:(0,y.jsx)(z,{label:(0,i.__)("Payment Provider Options","woocommerce"),provider:e})})})]})})},Q=({providers:e,installedPluginSlugs:t,installingPlugin:n,setUpPlugin:o,acceptIncentive:s,shouldHighlightIncentive:i,updateOrdering:c,setIsOnboardingModalOpen:r})=>{const l=(0,L.Zp)();return(0,y.jsx)(R.q6,{items:e,className:"settings-payment-gateways__list",setItems:c,children:e.map((e=>{switch(e._type){case a.PaymentsProviderType.Suggestion:const c=e,d=t.includes(e.plugin.slug);return(0,y.jsx)(R.Uq,{id:c.id,children:K({suggestion:c,installingPlugin:n,setUpPlugin:o,pluginInstalled:d,acceptIncentive:s,shouldHighlightIncentive:i})},c.id);case a.PaymentsProviderType.Gateway:const m=e;return(0,y.jsx)(R.Uq,{id:e.id,children:J({gateway:m,installingPlugin:n,acceptIncentive:s,shouldHighlightIncentive:i,setIsOnboardingModalOpen:r})},e.id);case a.PaymentsProviderType.OfflinePmsGroup:const _=e;return(0,y.jsx)(R.Uq,{id:_.id,children:(0,y.jsx)("div",{id:_.id,className:"transitions-disabled woocommerce-list__item clickable-list-item enter-done",onClick:()=>{l((0,b.Wg)(_.management._links.settings.href))},children:(0,y.jsxs)("div",{className:"woocommerce-list__item-inner",children:[(0,y.jsxs)("div",{className:"woocommerce-list__item-before",children:[(0,y.jsx)(R.Gh,{}),(0,y.jsx)("img",{src:_.icon,alt:_.title+" logo"})]}),(0,y.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,y.jsx)("span",{className:"woocommerce-list__item-title",children:_.title}),(0,y.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:{__html:_.description}})]}),(0,y.jsx)("div",{className:"woocommerce-list__item-after centered no-buttons",children:(0,y.jsx)("div",{className:"woocommerce-list__item-after__actions",children:(0,y.jsx)("a",{className:"woocommerce-list__item-after__actions__arrow",href:_.management._links.settings.href,children:(0,y.jsx)(g.A,{icon:"chevron-right"})})})})]})})},_.id);default:return null}}))})},X=({providers:e,installedPluginSlugs:t,installingPlugin:n,setUpPlugin:o,acceptIncentive:s,shouldHighlightIncentive:l,updateOrdering:m,isFetching:u,businessRegistrationCountry:g,setBusinessRegistrationCountry:w,setIsOnboardingModalOpen:x})=>{var f;const{invalidateResolution:j}=(0,c.useDispatch)(a.paymentSettingsStore),{invalidateResolution:k}=(0,c.useDispatch)(a.woopaymentsOnboardingStore),[N,P]=(0,r.useState)(!1),S=(0,r.useRef)(null),I=(window.wcSettings?.admin?.preloadSettings?.general?.woocommerce_default_country||"US").split(":")[0],A=(0,r.useMemo)((()=>Object.entries(window.wcSettings.countries||[]).map((([e,t])=>({key:e,name:(0,h.decodeEntities)(t),types:[]}))).sort(((e,t)=>e.name.localeCompare(t.name)))),[]),M=I!==g,E=(0,C.A)("settings-payment-gateways__header-select-container",{"has-alert":M}),D=e=>{const t=e.target.closest(".settings-payment-gateways__header-select-container--indicator");S.current&&t!==S.current||((0,b.TH)("business_location_indicator_click",{store_country:I,business_country:g||"unknown"}),P((e=>!e)))};return(0,y.jsxs)("div",{className:"settings-payment-gateways",children:[(0,y.jsxs)("div",{className:"settings-payment-gateways__header",children:[(0,y.jsx)("div",{className:"settings-payment-gateways__header-title",children:(0,i.__)("Payment providers","woocommerce")}),(0,y.jsxs)("div",{className:E,children:[(0,y.jsx)(B,{className:"woocommerce-select-control__country",label:(0,i.__)("Business location:","woocommerce"),placeholder:"",value:null!==(f=A.find((e=>e.key===g)))&&void 0!==f?f:{key:"US",name:"United States (US)"},options:A,onChange:e=>{d()({path:a.WC_ADMIN_NAMESPACE+"/settings/payments/country",method:"POST",data:{location:e}}).then((()=>{w(e),window.wcSettings.admin.woocommerce_payments_nox_profile&&(window.wcSettings.admin.woocommerce_payments_nox_profile.business_country_code=e),j("getPaymentProviders",[e]),k("getOnboardingData",[])}))}}),M&&(0,y.jsxs)("div",{className:"settings-payment-gateways__header-select-container--indicator",tabIndex:0,role:"button",ref:S,onClick:D,onKeyDown:e=>{"Enter"!==e.key&&" "!==e.key||D(e)},children:[(0,y.jsx)("div",{className:"settings-payment-gateways__header-select-container--indicator-icon",children:(0,y.jsx)(O.A,{})}),N&&(0,y.jsx)(p.Popover,{className:"settings-payment-gateways__header-select-container--indicator-popover",placement:"top-end",offset:4,variant:"unstyled",focusOnMount:!0,noArrow:!0,shift:!0,onFocusOutside:()=>{P(!1)},children:(0,y.jsx)("div",{className:"components-popover__content-container",children:(0,y.jsx)("p",{children:(0,T.A)({mixedString:(0,i.__)("Your business location does not match your store location. {{link}}Edit store location.{{/link}}","woocommerce"),components:{link:(0,y.jsx)(v.Link,{href:(0,_.getAdminLink)("admin.php?page=wc-settings&tab=general"),target:"_blank",type:"external",onClick:()=>{(0,b.TH)("business_location_popover_edit_store_location_click",{store_country:I,business_country:g||"unknown"})}})}})})})})]})]})]}),u?(0,y.jsx)(F.i,{rows:5}):(0,y.jsx)(Q,{providers:e,installedPluginSlugs:t,installingPlugin:n,setUpPlugin:o,acceptIncentive:s,shouldHighlightIncentive:l,updateOrdering:m,setIsOnboardingModalOpen:x})]})},ee=({incentive:e,provider:t,onboardingUrl:n,onDismiss:s,onAccept:a,setUpPlugin:c})=>{const[l,d]=(0,r.useState)(!1),[m,_]=(0,r.useState)(!1),[u,g]=(0,r.useState)(!1),h="wc_settings_payments__banner";return(0,o.useEffect)((()=>{var n;(0,b.TH)("incentive_show",{incentive_id:e.promo_id,provider_id:t.id,suggestion_id:null!==(n=t._suggestion_id)&&void 0!==n?n:"unknown",display_context:h})}),[e,t]),l||(0,b.$8)(e,h)||m?null:(0,y.jsx)(p.Card,{className:"woocommerce-incentive-banner",isRounded:!0,children:(0,y.jsxs)("div",{className:"woocommerce-incentive-banner__content",children:[(0,y.jsx)("div",{className:"woocommerce-incentive-banner__image",children:(0,y.jsx)("img",{src:x.GZ+"images/settings-payments/incentives-illustration.svg",alt:(0,i.__)("Incentive illustration","woocommerce")})}),(0,y.jsxs)(p.CardBody,{className:"woocommerce-incentive-banner__body",children:[(0,y.jsx)(N,{status:"has_incentive",message:(0,i.__)("Limited time offer","woocommerce")}),(0,y.jsxs)("div",{className:"woocommerce-incentive-banner__copy",children:[(0,y.jsx)("h2",{children:e.title}),(0,y.jsx)("p",{children:e.description})]}),(0,y.jsx)("div",{className:"woocommerce-incentive-banner__terms",children:(0,r.createInterpolateElement)((0,i.__)("See <termsLink /> for details.","woocommerce"),{termsLink:(0,y.jsx)(v.Link,{href:e.tc_url,target:"_blank",rel:"noreferrer",type:"external",children:(0,i.__)("Terms and Conditions","woocommerce")})})}),(0,y.jsxs)("div",{className:"woocommerce-incentive-banner__actions",children:[(0,y.jsx)(p.Button,{variant:"primary",isBusy:l,disabled:l,onClick:()=>{var o,i;(0,b.TH)("incentive_accept",{incentive_id:e.promo_id,provider_id:t.id,suggestion_id:null!==(o=t._suggestion_id)&&void 0!==o?o:"unknown",display_context:h}),g(!0),a(e.promo_id),s(e._links.dismiss.href,h,!0),d(!0),c(t,n,"not_installed"===t.plugin.status&&null!==(i=t._links?.attach?.href)&&void 0!==i?i:null,"wc_settings_payments__incentive_banner"),g(!1)},children:e.cta_label}),(0,y.jsx)(p.Button,{variant:"tertiary",isBusy:u,disabled:u,onClick:()=>{g(!0),s(e._links.dismiss.href,h),g(!1),_(!0)},children:(0,i.__)("Dismiss","woocommerce")})]})]})]})})},te=({incentive:e,provider:t,onboardingUrl:n,onAccept:s,onDismiss:a,setUpPlugin:c})=>{const[l,d]=(0,r.useState)(!1),[m,_]=(0,r.useState)(!0),u="wc_settings_payments__modal",g=(0,b.$8)(e,u);(0,o.useEffect)((()=>{var n;(0,b.TH)("incentive_show",{incentive_id:e.promo_id,provider_id:t.id,suggestion_id:null!==(n=t._suggestion_id)&&void 0!==n?n:"unknown",display_context:u})}),[e,t]);const h=()=>{_(!1)};return g?null:(0,y.jsx)(y.Fragment,{children:m&&(0,y.jsx)(p.Modal,{title:"",className:"woocommerce-incentive-modal",onRequestClose:()=>{a(e._links.dismiss.href,u),h()},children:(0,y.jsx)(p.Card,{className:"woocommerce-incentive-modal__card",children:(0,y.jsxs)("div",{className:"woocommerce-incentive-modal__content",children:[(0,y.jsx)(p.CardMedia,{className:"woocommerce-incentive-modal__media",children:(0,y.jsx)("img",{src:x.GZ+"images/settings-payments/incentives-illustration.svg",alt:(0,i.__)("Incentive illustration","woocommerce")})}),(0,y.jsxs)(p.CardBody,{className:"woocommerce-incentive-modal__body",children:[(0,y.jsx)("div",{children:(0,y.jsx)(N,{status:"has_incentive",message:(0,i.__)("Limited time offer","woocommerce")})}),(0,y.jsx)("h2",{children:e.title}),(0,y.jsx)("p",{children:e.description}),(0,y.jsx)("p",{className:"woocommerce-incentive-modal__terms",children:(0,r.createInterpolateElement)((0,i.__)("See <termsLink /> for details.","woocommerce"),{termsLink:(0,y.jsx)(v.Link,{href:e.tc_url,target:"_blank",rel:"noreferrer",type:"external",children:(0,i.__)("Terms and Conditions","woocommerce")})})}),(0,y.jsx)("div",{className:"woocommerce-incentive-model__actions",children:(0,y.jsx)(p.Button,{variant:"primary",isBusy:l,disabled:l,onClick:()=>{var o,i;(0,b.TH)("incentive_accept",{incentive_id:e.promo_id,provider_id:t.id,suggestion_id:null!==(o=t._suggestion_id)&&void 0!==o?o:"unknown",display_context:u}),d(!0),s(e.promo_id),a(e._links.dismiss.href,u,!0),h(),c(t,n,"not_installed"===t.plugin.status&&null!==(i=t._links?.attach?.href)&&void 0!==i?i:null,"wc_settings_payments__incentive_modal"),d(!1)},children:e.cta_label})})]})]})})})})};var ne=n(93832);function oe({setIsOpen:e,children:t}){return(0,y.jsx)(p.Modal,{className:"settings-payments-onboarding-modal",isFullScreen:!0,__experimentalHideHeader:!0,onRequestClose:()=>e(!1),shouldCloseOnClickOutside:!1,children:t})}var se=n(58016),ie=n(99096),ae=n(75854),ce=n(66087),re=n(20195),le=n(10979),de=n.n(le);const me=(0,r.forwardRef)((function({className:e,children:t,spokenMessage:n=t,politeness:o="polite",actions:s=[],onRemove:a=ce.noop,icon:c=null,explicitDismiss:l=!1,onDismiss:d=null,__unstableHTML:m=!1},_){function u(e){e&&e.preventDefault&&e.preventDefault(),d(),a()}d=d||ce.noop,function(e,t){const n="string"==typeof e?e:(0,r.renderToString)(e);(0,r.useEffect)((()=>{n&&(0,re.speak)(n,t)}),[n,t])}(n,o),(0,r.useEffect)((()=>{const e=setTimeout((()=>{l||(d(),a())}),1e4);return()=>clearTimeout(e)}),[l,d,a]);const g=(0,C.A)(e,"components-snackbar",{"components-snackbar-explicit-dismiss":!!l});s&&s.length>1&&(!0===globalThis.SCRIPT_DEBUG&&de()("Snackbar can only have 1 action, use Notice if your message require many messages"),s=[s[0]]);const h=(0,C.A)("components-snackbar__content",{"components-snackbar__content-with-icon":!!c});return!0===m&&(t=(0,y.jsx)(r.RawHTML,{children:t})),(0,y.jsx)("div",{ref:_,className:g,onClick:l?ce.noop:u,tabIndex:"0",role:l?"":"button",onKeyPress:l?ce.noop:u,"aria-label":l?"":(0,i.__)("Dismiss this notice","woocommerce"),children:(0,y.jsxs)("div",{className:h,children:[c&&(0,y.jsx)("div",{className:"components-snackbar__icon",children:c}),t,s.map((({label:e,onClick:t,url:n},o)=>(0,y.jsx)(p.Button,{href:n,isTertiary:!0,onClick:e=>function(e,t){e.stopPropagation(),a(),t&&t(e)}(e,t),className:"components-snackbar__action",children:e},o))),l&&(0,y.jsx)("span",{role:"button","aria-label":"Dismiss this notice",tabIndex:"0",className:"components-snackbar__dismiss-button",onClick:u,onKeyPress:u,children:"✕"})]})})})),_e=({children:e,duration:t=4e3,className:n})=>{const[o,s]=(0,r.useState)(!1),[i,a]=(0,r.useState)(!1);(0,r.useEffect)((()=>{const e=setTimeout((()=>{s(!0);const e=setTimeout((()=>{a(!0)}),t);return()=>clearTimeout(e)}),100);return()=>{clearTimeout(e)}}),[]);const c=["woopayments_onboarding_modal_snackbar_wrapper",n,o?"is-visible":"",i?"is-exiting":""].filter(Boolean).join(" ");return(0,y.jsx)("div",{className:c,children:(0,y.jsx)(me,{className:n+"__snackbar",children:e})})},ue=()=>{const{snackbar:e}=(0,ie.w)();return e.show?(0,y.jsx)(_e,{className:e.className||"",duration:e.duration,children:e.message}):null};function ge({isOpen:e,setIsOpen:t,providerData:n}){const o=(0,L.zy)(),a=(0,m.getHistory)(),r="/woopayments/onboarding",{createErrorNotice:l}=(0,c.dispatch)("core/notices"),d=(0,ne.getQueryArg)(window.location.href,"wpcom_connection_return")||!1,_=n?.onboarding?.state?.wpcom_has_working_connection||!1,{sessionEntryPoint:u}=(0,ie.w)();s().useEffect((()=>{const n=(0,m.getQuery)(),o=n.path&&n.path.includes(r);if(!o||e||!_&&d||((0,b.W7)("woopayments_onboarding_modal_opened",{source:u}),t(!0)),e&&!o){const e=(0,m.getNewPath)({path:r},r,{page:"wc-settings",tab:"checkout"});a.push(e)}!_&&d&&((0,b.W7)("woopayments_onboarding_wpcom_connection_cancelled",{source:u}),l((0,i.__)("Setup was cancelled!","woocommerce"),{type:"snackbar",explicitDismiss:!1}))}),[o,e,t,d,_,l,a]);const g=()=>{(0,b.W7)("woopayments_onboarding_modal_closed",{source:u});const e=(0,m.getNewPath)({},"/wp-admin/admin.php",{page:"wc-settings",tab:"checkout"});a.push(e),t(!1)};return e?(0,y.jsx)(oe,{setIsOpen:g,children:(0,y.jsxs)(ie.X,{closeModal:g,onboardingSteps:ae.Ci,children:[(0,y.jsx)(se.A,{}),(0,y.jsx)(ue,{})]})}):null}var pe=n(14908),he=n(83306);const ye=({textProps:e,message:t,eventName:n="",eventProperties:o={},targetUrl:s,linkType:a="wc-admin",target:c,onClickCallback:r})=>{const l=t.match(/{{Link}}(.*?){{\/Link}}/),d=l?l[1]:"",m="external"===a&&"_blank"===c;return(0,y.jsx)(pe.Text,{...e,children:(0,T.A)({mixedString:t,components:{Link:(0,y.jsx)(v.Link,{onClick:()=>{if(r?r():(0,he.recordEvent)(n,o),"external"!==a)return window.location.href=s,!1},href:s,type:a,target:m?"_blank":void 0,"aria-label":m?`${d} (${(0,i.__)("opens in a new tab","woocommerce")})`:void 0})}})})};var we=n(51684);const ve=()=>{var e,t;const[n,s]=(0,r.useState)(null),[l,g]=(0,r.useState)(null),{installAndActivatePlugins:p}=(0,c.useDispatch)(a.pluginsStore),{updateProviderOrdering:h,attachPaymentExtensionSuggestion:w}=(0,c.useDispatch)(a.paymentSettingsStore),[v,x]=(0,r.useState)(null),[f,j]=(0,r.useState)(!1),[k,N]=(0,r.useState)(window.wcSettings?.admin?.woocommerce_payments_nox_profile?.business_country_code||null),[P,C]=(0,r.useState)(!1);(0,r.useEffect)((()=>{(0,b.TH)("pageview");const e=new URLSearchParams(window.location.search);"true"===e.get("test_drive_error")&&x((0,i.sprintf)((0,i.__)("%s: An error occurred while setting up your sandbox account — please try again.","woocommerce"),"WooPayments")),"1"===e.get("wcpay-connect-jetpack-error")&&x((0,i.sprintf)((0,i.__)("%s: There was a problem connecting your WordPress.com account — please try again.","woocommerce"),"WooPayments")),"true"===e.get("wcpay-sandbox-success")&&j(!0)}),[]);const O=(0,c.useSelect)((e=>e(a.pluginsStore).getInstalledPlugins()),[]),{invalidateResolutionForStoreSelector:T}=(0,c.useDispatch)(a.paymentSettingsStore),{providers:I,offlinePaymentGateways:A,suggestions:M,suggestionCategories:E,isFetching:D}=(0,c.useSelect)((e=>{const t=e(a.paymentSettingsStore);return{providers:t.getPaymentProviders(k),offlinePaymentGateways:t.getOfflinePaymentGateways(k),suggestions:t.getSuggestions(k),suggestionCategories:t.getSuggestionCategories(k),isFetching:t.isFetching()}}),[k]),H=(0,o.useCallback)(((e,t,n=!1)=>{d()({url:e,method:"POST",data:{context:t,do_not_track:n}})}),[]),B=(0,o.useCallback)((e=>{d()({path:`/wc-analytics/admin/notes/experimental-activate-promo/${e}`,method:"POST"})}),[]);(0,r.useEffect)((()=>{g(null)}),[I]);const F=I.find((e=>"_incentive"in e)),L=F?F._incentive:null;let R=!1,W=!1,U=!1;if(F&&L)if((0,b.sq)(L))if((0,b.$8)(L,"wc_settings_payments__modal")){if(!(0,b.$8)(L,"wc_settings_payments__banner")){const e=new Date;e.setDate(e.getDate()-30),(0,b.uH)(L,"wc_settings_payments__modal",e.getTime())?W=!0:U=!0}}else R=!0;else(0,b.uU)(L)&&((0,b.$8)(L,"wc_settings_payments__banner")?U=!0:W=!0);const G=(0,o.useRef)(!1);(0,r.useEffect)((()=>{if(D||!I.length||!M.length||G.current)return;G.current=!0;const e={woocommerce_payments_displayed:I.some((e=>(0,b.j4)(e.id)))};M.forEach((t=>{e[t.id.replace(/-/g,"_")+"_displayed"]=!0})),I.filter((e=>"suggestion"===e._type)).forEach((t=>{t._suggestion_id?e[t._suggestion_id.replace(/-/g,"_")+"_displayed"]=!0:t.plugin&&t.plugin.slug&&(e[t.plugin.slug.replace(/-/g,"_")+"_displayed"]=!0)})),(0,b.TH)("recommendations_pageview",e)}),[M,I,D]);const $=(0,o.useCallback)(((e,t,o,i="wc_settings_payments__main")=>{var r;n||(e?.onboarding?._links?.preload?.href&&d()({url:e?.onboarding?._links?.preload.href,method:"POST",data:{location:k}}),!t&&(0,b.j4)(e.id)&&(t=(0,b.ge)()),s(e.id),(0,b.TH)("recommendations_setup",{extension_selected:e.plugin.slug,extension_action:"not_installed"===e.plugin.status?"install":"activate",provider_id:e.id,suggestion_id:null!==(r=e?._suggestion_id)&&void 0!==r?r:"unknown",provider_extension_slug:e.plugin.slug,from:i,source:i}),p([e.plugin.slug]).then((async n=>{var r;o&&w(o),u(n),T("getPaymentProviders"),"not_installed"===e.plugin.status&&(0,b.TH)("provider_installed",{provider_id:e.id,suggestion_id:null!==(r=e?._suggestion_id)&&void 0!==r?r:"unknown",provider_extension_slug:e.plugin.slug,from:i}),s(null);const l=(await(0,c.resolveSelect)(a.paymentSettingsStore).getPaymentProviders(k)).find((t=>t.id===e.id||t?._suggestion_id===e.id||t.plugin.slug===e.plugin.slug));if("native_in_context"===l?.onboarding?.type)(0,b.W7)("woopayments_onboarding_modal_opened",{from:i,source:Z.Fx}),C(!0);else{var d;if((null!==(d=l?.onboarding?.recommended_payment_methods)&&void 0!==d?d:[]).length>0)return void(0,m.getHistory)().push((0,m.getNewPath)({},"/payment-methods"));t&&(window.location.href=t)}})).catch((t=>{var n;let o="provider_extension_installation_failed";"not_installed"!==e.plugin.status&&(o="provider_extension_activation_failed"),(0,b.TH)(o,{provider_id:e.id,suggestion_id:null!==(n=e?._suggestion_id)&&void 0!==n?n:"unknown",provider_extension_slug:e.plugin.slug,from:i,source:Z.Fx,reason:"error"}),u(t),s(null)})))}),[n,p,T,k]),z=(0,y.jsx)(ye,{message:(0,i.__)("Visit {{Link}}the WooCommerce Marketplace{{/Link}} to find additional payment options.","woocommerce"),onClickCallback:()=>{const e=I.map((e=>e.plugin&&e.plugin.slug?e.plugin.slug.replace(/-/g,"_"):e._suggestion_id?e._suggestion_id.replace(/-/g,"_"):e.id));A.forEach((t=>{e.push(t.id)})),M.forEach((t=>{t.plugin&&t.plugin.slug?e.push(t.plugin.slug.replace(/-/g,"_")):e.push(t.id.replace(/-/g,"_"))}));const t=[...new Set(e)];(0,b.TH)("recommendations_other_options",{available_payment_methods:t.join(", ")})},targetUrl:(0,we.isFeatureEnabled)("marketplace")?(0,_.getAdminLink)("admin.php?page=wc-admin&tab=extensions&path=/extensions&category=payment-gateways"):"https://woocommerce.com/product-category/woocommerce-extensions/payment-gateways/",linkType:(0,we.isFeatureEnabled)("marketplace")?"wc-admin":"external"});return(0,y.jsxs)(y.Fragment,{children:[R&&F&&L&&(0,y.jsx)(te,{incentive:L,provider:F,onboardingUrl:null!==(e=F.onboarding?._links?.onboard?.href)&&void 0!==e?e:null,onDismiss:H,onAccept:B,setUpPlugin:$}),v&&(0,y.jsxs)("div",{className:"notice notice-error is-dismissible wcpay-settings-notice",children:[(0,y.jsx)("p",{children:v}),(0,y.jsx)("button",{type:"button",className:"notice-dismiss",onClick:()=>{x(null)}})]}),W&&F&&L&&(0,y.jsx)(ee,{incentive:L,provider:F,onboardingUrl:null!==(t=F.onboarding?._links?.onboard?.href)&&void 0!==t?t:null,onDismiss:H,onAccept:B,setUpPlugin:$}),(0,y.jsxs)("div",{className:"settings-payments-main__container",children:[(0,y.jsx)(X,{providers:l||I,installedPluginSlugs:O,installingPlugin:n,setUpPlugin:$,acceptIncentive:B,shouldHighlightIncentive:U,updateOrdering:function(e){const t=e.map((e=>e._order)).sort(((e,t)=>e-t)),n={};e.forEach(((e,o)=>{n[e.id]=t[o]})),h(n),g(e)},isFetching:D,businessRegistrationCountry:k,setBusinessRegistrationCountry:N,setIsOnboardingModalOpen:C}),!D&&0===M.length&&(0,y.jsx)("div",{className:"more-payment-options",children:z}),(D||M.length>0)&&(0,y.jsx)(S,{suggestions:M,suggestionCategories:E,installingPlugin:n,setUpPlugin:$,isFetching:D,morePaymentOptionsLink:z})]}),((0,b.ZT)(I)||(0,b.Pt)(I))&&(0,y.jsx)(ge,{isOpen:P,setIsOpen:C,providerData:(0,b.RY)(I)||{}}),(0,y.jsx)(q,{isOpen:f&&(0,b.Pt)(I),devMode:(0,b.MQ)(I),onClose:()=>j(!1)})]})},xe=ve},8181:(e,t,n)=>{t.A=function(e){var t=e.size,n=void 0===t?24:t,o=e.onClick,c=(e.icon,e.className),r=function(e,t){if(null==e)return{};var n,o,s=function(e,t){if(null==e)return{};var n,o,s={},i=Object.keys(e);for(o=0;o<i.length;o++)n=i[o],0<=t.indexOf(n)||(s[n]=e[n]);return s}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],0<=t.indexOf(n)||Object.prototype.propertyIsEnumerable.call(e,n)&&(s[n]=e[n])}return s}(e,i),l=["gridicon","gridicons-cross-small",c,!1,!1,!1].filter(Boolean).join(" ");return s.default.createElement("svg",a({className:l,height:n,width:n,onClick:o},r,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),s.default.createElement("g",null,s.default.createElement("path",{d:"M17.705 7.705l-1.41-1.41L12 10.59 7.705 6.295l-1.41 1.41L10.59 12l-4.295 4.295 1.41 1.41L12 13.41l4.295 4.295 1.41-1.41L13.41 12l4.295-4.295z"})))};var o,s=(o=n(51609))&&o.__esModule?o:{default:o},i=["size","onClick","icon","className"];function a(){return a=Object.assign?Object.assign.bind():function(e){for(var t,n=1;n<arguments.length;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},a.apply(this,arguments)}},46608:(e,t,n)=>{t.A=function(e){var t=e.size,n=void 0===t?24:t,o=e.onClick,c=(e.icon,e.className),r=function(e,t){if(null==e)return{};var n,o,s=function(e,t){if(null==e)return{};var n,o,s={},i=Object.keys(e);for(o=0;o<i.length;o++)n=i[o],0<=t.indexOf(n)||(s[n]=e[n]);return s}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)n=i[o],0<=t.indexOf(n)||Object.prototype.propertyIsEnumerable.call(e,n)&&(s[n]=e[n])}return s}(e,i),l=["gridicon","gridicons-info-outline",c,!!function(e){return 0==e%18}(n)&&"needs-offset",!1,!1].filter(Boolean).join(" ");return s.default.createElement("svg",a({className:l,height:n,width:n,onClick:o},r,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"}),s.default.createElement("g",null,s.default.createElement("path",{d:"M13 9h-2V7h2v2zm0 2h-2v6h2v-6zm-1-7c-4.411 0-8 3.589-8 8s3.589 8 8 8 8-3.589 8-8-3.589-8-8-8m0-2c5.523 0 10 4.477 10 10s-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2z"})))};var o,s=(o=n(51609))&&o.__esModule?o:{default:o},i=["size","onClick","icon","className"];function a(){return a=Object.assign?Object.assign.bind():function(e){for(var t,n=1;n<arguments.length;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},a.apply(this,arguments)}}}]);