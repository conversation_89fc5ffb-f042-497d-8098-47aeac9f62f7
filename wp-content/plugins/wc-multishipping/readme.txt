=== Mondial Relay & Chronopost plugin for WooCommerce - WCMultiShipping ===
Contributors: woomultishipping
Donate link:
Tags: WooCommerce, Mondial Relay, Chronopost
Requires at least: 4.7
Tested up to: 6.8
Stable tag: 2.5.8
Requires PHP: 5.7
License: GPLv3
License URI: https://www.gnu.org/licenses/gpl-3.0.html

Add Mondial Relay & Chronopost to Woocommerce
Create your labels and send your shipments easily.

== Description ==

WcMultiShipping is a plugin that integrates Mondial Relay and Chronopost shipping services into WooCommerce, streamlining your shipping process, automating label generation, and allowing customers to choose their preferred delivery method during checkout.

### Key Features:

- **Seamless Mondial Relay Integration**: Effortlessly add Mondial Relay shipping methods to your WooCommerce store, allowing customers to select pickup points or home delivery options during checkout.
- **Automatic Label Generation**: Generate shipping labels automatically for Mondial Relay and Chronopost orders directly from your WooCommerce admin panel, reducing the time spent on manual tasks (Pro version).
- **Flexible Shipping Rates**: Customize shipping costs based on cart total, order weight, or specific shipping zones, ensuring that your customers always get the best rates.
- **Advanced Configuration Options**: Access detailed settings for each shipping method, including default insurance amounts, preparation times, and handling fees.
- **Google Maps Integration**: Display Mondial Relay pickup points using Google Maps during checkout, giving your customers a clear and easy way to select their preferred location.

### Why Choose WcMultiShipping?

- **Enhanced Customer Experience**: Offering multiple delivery options, including the widely recognized Mondial Relay service, improves customer satisfaction and increases conversion rates.
- **Time-Saving Automation**: With automatic label generation and seamless integration into WooCommerce, you can focus on growing your business while we handle the logistics.
- **Optimized for WooCommerce**: WcMultiShipping is built to work flawlessly with WooCommerce, ensuring a smooth experience for both store owners and customers.

### Available Mondial Relay Shipping Methods:

- **Mondial Relay - Point Relais**: Customers can choose to have their orders delivered to a nearby pickup point.
- **Mondial Relay - Colis Drive**: Perfect for customers who prefer to pick up their orders from a drive-through location.
- **Mondial Relay - Home Delivery (1 or 2 Deliverers)**: Offers the convenience of home delivery with the option for one or two deliverers, ideal for heavier packages.
- **Mondial Relay - Home Delivery < 30kg**: Specialized for packages weighing under 30kg, ensuring fast and efficient delivery.

For a detailed guide on configuring Mondial Relay with WooCommerce, visit our comprehensive setup page: [Mondial Relay WooCommerce Setup](https://www.wcmultishipping.com/mondial-relay-woocommerce/)

### Available Chronopost Shipping Methods:

- **Chronopost 10**: Guaranteed delivery by 10 AM the next day.
- **Chronopost 13**: Delivery by 1 PM the next day.
- **Chronopost 18**: End of day delivery option.
- **Chronopost Classic**: Standard delivery with no time commitment.
- **Chronopost Express**: Fast, reliable express delivery service.
- **Chronopost Relais**: Convenient delivery to local pickup points.
- **Chronopost Relais Domicile**: Combined service of home delivery and pickup point options.
- **Chronopost Relais Europe**: Cross-border delivery within Europe.
- **Chronopost Same Day**: Urgent same-day delivery service.

For step-by-step instructions on setting up Chronopost, please visit: [Chronopost WooCommerce Setup](https://www.wcmultishipping.com/plugin-chronopost-woocommerce/)

### Installation

1. Download and install the WcMultiShipping plugin via the "Plugins" section of your WordPress admin panel.
2. Activate the plugin through the "Plugins" menu.
3. Configure the Mondial Relay and Chronopost shipping methods in the WooCommerce settings.
4. Customize your shipping rates and conditions based on your business needs.
5. Manage your shipping labels directly from the WooCommerce admin panel.

### Frequently Asked Questions

= How do I save time using WcMultiShipping? =
WcMultiShipping automates the label generation process and integrates directly with your WooCommerce store, allowing you to manage everything from one place.

= Is the plugin easy to use? =
Absolutely! The plugin is designed for ease of use, with a straightforward setup process and a user-friendly interface.

= Can I set custom shipping rates for Mondial Relay? =
Yes, you can define shipping rates based on cart total, order weight, and specific shipping zones.

= Does the plugin support automatic label generation for Mondial Relay? =
Yes, the Pro version of the plugin allows for automatic label generation, saving you significant time and effort.

= How can I configure Mondial Relay for WooCommerce? =
We provide a detailed setup guide on our website. You can follow the steps here: [Mondial Relay WooCommerce Setup](https://www.wcmultishipping.com/mondial-relay-woocommerce/)

== Screenshots ==

1. Chronopost order listing
2. Chronopost WooCommerce shipping methods
3. Chronopost shipping methods configuration

== Changelog ==

= 2.5.6 - June 2025 =
MR Label fix 
Pickup points location fix


= 2.5.5 - February 2025 =
Chronopost Shipping Methods Per Country update
Fix w/ assigning order to Chronopost / MR from admin page
Compatibilty with new WordPress Select For Country in checkout
Order status tracking fix + Chronopost link to doc 



= 2.5.1 - September 2024 =
Front-end issue fixed
Inward label issue fixed

= 2.5.0 - July 2024 =
WordPress HPOS compatibilty
CRON fix + tracking status update 
Multiple Chronopost fixes
New WordPress checkout compatibilty

= 2.4.6 - November 2023 =
Mondial Relay Pickup point issue fixed.

= 2.4.5 - November 2023 =
Chronopost2Shop sending method
WooCommerce compatibilty fix
WooCommerce HPOS compatibilty
Phone number regex improvement
Modal improvement w/ WooCommerce new checkout
Chronopost Label Format selection


= 2.3.8 - November 2023 =
Security fixes (Logs could be checked from the front-end)

= 2.3.7 - November 2023 =
Missing CSS file fix

= 2.3.6 - November 2023 =
Google Maps issue fixed

= 2.3.5 - November 2023 =
CSS file inclusion fix
Chronopost improvement
WooCommerce block integration


= 2.3.3 - September 2023 =
Chronopost : Recipient address 2

= 2.3.2 - September 2023 =
Pickup point modal fix

= 2.3.1 - September 2023 =
WooCommerce x Gutenberg fix

= 2.3.0 - August 2023 =
Packaging weight fix (kg vs g)
UPS shipping point fix

= 2.2.0 - July 2023 =
Chronopost Adress Fix
Download as PDF when there's only one label
UPS integration fix
WooCommerce block

= 2.1.8 - 07 February 2023 =
Chronopost tracking improvements

= 2.1.7 - 07 February 2023 =
Pick up points selection pop-up design improvements

= 2.1.6 - 01 February 2023 =
Pick up points selection pop-up design improvements
Chronopost status update fix

= 2.1.5 - 24 December 2022 =
Adding Netherlands to Mondial Relay countries list
OpenStreetMaps click issue with Chrome is now fixed
Improvements regarding weight calculation

= 2.1.2 - 24 December 2022 =
Fix issued with Zip code starting with "0"

= 2.1.1 - 17 December 2022 =
Order status are now available on front-end
Fixed issue with wrong characters (Mondial relay addresses)

= 1.9.5 - 29 March 2022 =
Chronopost Quickcost fix
Multiple maps displayed issue fixed

= 1.8.7 - 29 March 2022 =
Fix with float value for parcel size

= 1.8.7 - 29 March 2022 =
OSM alert pop up fix

= 1.8.5 - 16 March 2022 =
Chronopost new delivery methods + shipping label generation fix

= 1.8.0 - 24 October 2021 =
OpenStreetMap as default map provider + ChronoFresh

= 0.1.0 - 24 October 2021 =
Some advices and some help have been added into the interfaces

= 0.1.0 - 17 May 2021 =
OpenStreetMap integration

= 0.0.8 - 19 April 2021 =
Pickup selection from the backend

= 0.0.7 - 23 March 2021 =
Mondial Relay Widget integration

= 0.0.6 - 23 March 2021 =
Parcel Tracking Emails

= 0.0.2 - 23 March 2021 =
Mondial Relay support.

= 0.0.1 - 10 February 2021 =
First release.

== Upgrade Notice ==