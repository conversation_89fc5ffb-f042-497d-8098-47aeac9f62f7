"use strict"; jQuery(function (e) { e("#wms_parcels_number_input").on("blur change", function () { !function (a) { var n = e("#wms_meta_box_parcel_information"); e("#wms_meta_box .wms_meta_box_options .wms_metabox_parcel_info").slice(a).remove(); for (var t = e("#wms_meta_box .wms_meta_box_options .wms_metabox_parcel_info").length; t < a; t++) { var r = n.clone(!0); r.removeAttr("id"), r.find("#wms_parcels_number").remove(); var o = r.find(".title").first().html().replace("1", t + 1); r.find(".title").first().html(o), r.prepend(e("<div>", { style: "border_top=1px black solid; margin-top:15px;", colspan: 2 })), r.find("input").each(function () { var a = e(this).attr("name").replace(/\[0\]/, "[" + t + "]"); e(this).attr("name", a) }), r.insertBefore(e("#wms_meta_box_generate_outward_label_button")) } }(e(this).val()) }), e("#wms_generate_outward_label_button").off("click").on("click", function () { var a = window.location.search, n = new URLSearchParams(a), t = e("#wms_nonce_metabox").val(), r = { action: "wms_generate_label", wms_order_id: n.get("id"), wms_nonce: t, wms_action: "wms_generate_outward_labels" }; return e("#wms_meta_box :input").serializeArray().forEach(function (e) { r[e.name] = e.value }), e.get(wmsajaxurl, r).then(function (e) { location.reload() }).catch(function (e) { console.error("Ajax request failed: ", e), alert("An error occurred while generating the label.") }), !1 }), e("#wms_generate_inward_label_button").off("click").on("click", function () { var a = window.location.search, n = new URLSearchParams(a), t = e("#wms_nonce_metabox").val(), r = { action: "wms_generate_label", wms_order_id: n.get("id"), wms_nonce: t, wms_action: "wms_generate_inward_labels" }; return e("#wms_meta_box :input").serializeArray().forEach(function (e) { r[e.name] = e.value }), e.get(wmsajaxurl, r).then(function (e) { console.log(e) }).catch(function (e) { console.error("Ajax request failed: ", e), alert("An error occurred while generating the label.") }), !1 }) });