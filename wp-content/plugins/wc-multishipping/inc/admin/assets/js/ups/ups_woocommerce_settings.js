"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}window.addEventListener("DOMContentLoaded",function(){document.getElementById("wms_ups_account_test_credentials").addEventListener("click",function(){var e=this.closest("tr").querySelector(".wms_text_result");e.innerHTML="",e.classList.add("spinner");var n,s,o,r=(n=document.getElementById("wms_ups_api_access_key"),s=document.getElementById("wms_ups_account_number"),o=document.getElementById("wms_ups_password"),""!==n.value&&""!==s.value&&""!==o.value&&{api_key:n.value,account_number:s.value,password:o.value});if(!r)return e.classList.remove("spinner"),alert(t("Merci de bien définir vos informations de compte avant de tester.","wc-multishipping")),!1;r.action="wms_ups_test_credentials",jQuery.post(wmsajaxurl,r,function(t){e.classList.remove("spinner"),e.innerHTML=t.message,e.style.color=t.error?"red":"green"})}),document.getElementById("wms_ups_log_export").addEventListener("click",function(){var e=this.closest("tr").querySelector(".wms_text_result");e.innerHTML="",e.classList.add("spinner"),jQuery.post(wmsajaxurl,{action:"wms_ups_log_export"},function(t){"object"===_typeof(t)?(e.classList.remove("spinner"),e.innerHTML=t.message):(e.classList.remove("spinner"),e.innerHTML="",window.location="".concat(wmsajaxurl,"?action=wms_ups_log_export"))})});var e=wp.i18n,t=e.__;e._x,e._n,e._nx});