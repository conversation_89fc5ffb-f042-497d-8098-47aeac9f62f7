"use strict";jQuery(function(e){e("#wms_parcels_number_input").on("blur change",function(){!function(t){var o=e("#wms_meta_box_parcel_information");e("#wms_meta_box .wms_meta_box_options .wms_metabox_parcel_info").slice(t).remove();for(var n=e("#wms_meta_box .wms_meta_box_options .wms_metabox_parcel_info").length;n<t;n++){var _=o.clone(!0);_.removeAttr("id"),_.find("#wms_parcels_number").remove();var a=_.find(".title").first().html().replace("1",n+1);_.find(".title").first().html(a),_.prepend(e("<div>",{style:"border_top=1px black solid; margin-top:15px;",colspan:2})),_.find("input").each(function(){var t=e(this).attr("name").replace(/\[0\]/,"["+n+"]");e(this).attr("name",t)}),_.insertBefore(e("#wms_meta_box_generate_outward_label_button"))}}(e(this).val())}),e("#wms_generate_outward_label_button").off("click").on("click",function(){e("#wms_do_action").val("wms_generate_outward_labels")}),e("#wms_generate_inward_label_button").off("click").on("click",function(){e("#wms_do_action").val("wms_generate_inward_labels")})});