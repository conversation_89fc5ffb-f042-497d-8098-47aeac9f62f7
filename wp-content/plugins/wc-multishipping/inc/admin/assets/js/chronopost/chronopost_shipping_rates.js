"use strict";jQuery(function(e){function t(){var t=e(".wms__shipping_rates__shipping_class__select");t.selectWoo(),t.on("select2:select",function(t){var n=t.params.data.id,i=e(this).val();"all"==n?e(this).val(["all"]).trigger("change"):-1!==e.inArray("all",i)&&(i.splice(i.indexOf("all"),1),e(this).val(i).trigger("change"))}),t.on("select2:unselect",function(t){null===e(this).val()&&e(this).val(["all"]).trigger("change")})}function n(e){var t=document.getElementById("woocommerce_wms_10_value_add_quickcost").closest("tr"),n=document.getElementById("woocommerce_wms_10_value_quickcost_type").closest("tr"),i=e.checked?"table-row":"none";t.style.display=i,n.style.display=i}function i(t){"weight"==t.value?e(".condition_unit").html("("+woo_unit+")"):"cart_amount"==t.value&&e(".condition_unit").html("(€)")}var s;e("#wms_shipping_rates_add").click(function(){var n=function(t,n){return e('<tr>\n                    <td class="check-column"><input type="checkbox" /></td>\n                    <td style="text-align: center">\n                        <input type="number" class="input-number regular-input" step="0.01" min="0" required name="shipping_rates['.concat(t,'][min]"/>\n                    </td>\n                    <td style="text-align: center">\n                        <input type="number" class="input-number regular-input" step="0.01" min="0" required name="shipping_rates[').concat(t,'][max]"/>\n                    </td>\n                    <td style="text-align: center">\n                        <select multiple="multiple" class="wms__shipping_rates__shipping_class__select" style="width: auto; max-width: 10rem" required name="shipping_rates[').concat(t,'][shipping_class][]">\n                            ').concat(n,'\n                        </select>\n                    </td>\n                    <td style="text-align: center">\n                        <input type="number" class="input-number regular-input" step="0.01" min="0" required name="shipping_rates[').concat(t,'][price]"/>\n                    </td>\n                </tr>'))}(e(".table_rates:first tr").length,wms_global_var.shipping_classes_options);e(this).closest("table").children("tbody").append(n),n.prev().hasClass("alternate")||n.addClass("alternate"),t()}),e("#wms_shipping_rates_remove").click(function(){confirm(wms_global_var.confirm_deletion_txt)&&(e("table.shippingrows tbody input:checked").closest("tr").remove(),e("table.shippingrows input:checked").prop("checked",!1))}),e("input[type=radio][name=pricing_condition]").change(function(){i(this)}),e(document).ready(function(){i(e("input[name=pricing_condition]:checked")[0])}),null!==(s=document.getElementById("woocommerce_wms_10_quickcost"))&&(n(s),document.getElementById("woocommerce_wms_10_quickcost").addEventListener("change",function(){n(this)})),t()});