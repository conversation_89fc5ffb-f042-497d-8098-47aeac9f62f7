"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}window.addEventListener("DOMContentLoaded",function(){var e,t;null===(e=document.getElementById("wms_mondial_relay_account_test_credentials"))||void 0===e||e.addEventListener("click",function(){var e=this.closest("tr").querySelector(".wms_text_result");e.innerHTML="",e.classList.add("spinner");var t,n,r=(t=document.getElementById("wms_mondial_relay_private_key"),n=document.getElementById("wms_mondial_relay_customer_code"),""!==t.value&&""!==n.value&&{private_key:t.value,code_enseigne:n.value});if(!r)return e.classList.remove("spinner"),alert(o("Merci de bien définir vos informations de compte avant de tester.","wc-multishipping")),!1;r.action="wms_mondial_relay_test_credentials",fetch(wmsSettings.ajaxurl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams(r)}).then(function(e){return e.json()}).then(function(t){e.classList.remove("spinner"),e.innerHTML=t.message,e.style.color=t.error?"red":"green"})}),null===(t=document.getElementById("wms_mondial_relay_log_export"))||void 0===t||t.addEventListener("click",function(){var e=this.closest("tr").querySelector(".wms_text_result");e.innerHTML="",e.classList.add("spinner"),fetch(wmsSettings.ajaxurl,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({action:"wms_mondial_relay_log_export"})}).then(function(e){return e.json()}).then(function(t){"object"===_typeof(t)?(e.classList.remove("spinner"),e.innerHTML=t.message):(e.classList.remove("spinner"),e.innerHTML="",window.location="".concat(wmsSettings.ajaxurl,"?action=wms_mondial_relay_log_export"))})});var n=wp.i18n,o=n.__;n._x,n._n,n._nx});