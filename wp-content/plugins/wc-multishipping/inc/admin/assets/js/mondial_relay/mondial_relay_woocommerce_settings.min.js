"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}window.addEventListener("DOMContentLoaded",function(){document.getElementById("wms_mondial_relay_account_test_credentials").addEventListener("click",function(){var e=this.closest("tr").querySelector(".wms_text_result");e.innerHTML="",e.classList.add("spinner");var n,o,r=(n=document.getElementById("wms_mondial_relay_private_key"),o=document.getElementById("wms_mondial_relay_customer_code"),""!==n.value&&""!==o.value&&{private_key:n.value,code_enseigne:o.value});if(!r)return e.classList.remove("spinner"),alert(t("Merci de bien définir vos informations de compte avant de tester.","wc-multishipping")),!1;r.action="wms_mondial_relay_test_credentials",jQuery.post(ajaxurl,r,function(t){e.classList.remove("spinner"),e.innerHTML=t.message,e.style.color=t.error?"red":"green"})}),document.getElementById("wms_mondial_relay_log_export").addEventListener("click",function(){var e=this.closest("tr").querySelector(".wms_text_result");e.innerHTML="",e.classList.add("spinner"),jQuery.post(ajaxurl,{action:"wms_mondial_relay_log_export"},function(t){"object"===_typeof(t)?(e.classList.remove("spinner"),e.innerHTML=t.message):(e.classList.remove("spinner"),e.innerHTML="",window.location="".concat(ajaxurl,"?action=wms_mondial_relay_log_export"))})});var e=wp.i18n,t=e.__;e._x,e._n,e._nx});