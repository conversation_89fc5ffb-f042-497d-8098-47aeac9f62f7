"use strict";function _createForOfIteratorHelper(e,t){var i;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(i=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,a=!0,c=!1;return{s:function(){i=e[Symbol.iterator]()},n:function(){var e=i.next();return a=e.done,e},e:function(e){c=!0,r=e},f:function(){try{a||null==i.return||i.return()}finally{if(c)throw r}}}}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}jQuery(function(e){var t,i;p(),window.set_wms_google_maps_pickup_modal=p;var n,o=wp.i18n,r=o.__,a=(o._x,o._n,o._nx,[]),c="",s="";function p(){var t=document.getElementsByClassName("wms_pickup_open_modal_google_maps");if(0!==t.length){var i,o=_createForOfIteratorHelper(t);try{for(o.s();!(i=o.n()).done;){i.value.addEventListener("click",function(){e(this).WCBackboneModal({template:this.getAttribute("wms-pickup-modal-id")}),c=document.getElementById(this.getAttribute("wms-pickup-modal-id")),n=c.querySelector(".wc-backbone-modal-loader"),e("#wms_shipping_provider").val(this.getAttribute("wms-shipping-provider")),u()})}}catch(e){o.e(e)}finally{o.f()}}}function u(){var e={zoom:15,mapTypeId:google.maps.MapTypeId.ROADMAP,center:{lat:48.866667,lng:2.333333},disableDefaultUI:!0};s=new google.maps.Map(c.querySelector(".wms_pickup_modal_map"),e);c.querySelector(".wms_pickup_modal_address_city_input").value="Paris";c.querySelector(".wms_pickup_modal_address_zipcode_input").value="75001";c.querySelector(".wms_pickup_modal_address_country_select select").value="FR",l(),c.querySelector(".wms_pickup_modal_address_search").addEventListener("click",function(){a.map(function(e){e.setMap(null)}),a=[],l()})}function l(){var o=function(){var e=c.querySelector(".wms_pickup_modal_address_country_select select").value;return""===e&&(e="FR"),{country:e,zipcode:c.querySelector(".wms_pickup_modal_address_zipcode_input").value,city:c.querySelector(".wms_pickup_modal_address_city_input").value}}(),p=o.country,u=o.zipcode,l=o.city;if(""===u||""===l)return alert(r("Merci de définir une adresse valide","wc-multishipping")),!1;n.style.display="block";var m=e("#wms_nonce").val(),y={action:"wms_get_pickup_point",shipping_provider:e("#wms_shipping_provider").val(),country:p,zipcode:u,city:l,wms_nonce:m};return e.get(ajaxurl,y).then(function(e){if(n.style.display="none",e.error)return o=e.error_message,c.querySelector(".wms_pickup_modal_listing").innerHTML='<div style="color: #ff0000">'.concat(o,"</div>"),!1;var o,p,u=new google.maps.LatLngBounds;(i=c.querySelector(".wms_pickup_modal_listing")).innerHTML="",e.data.map(function(e){p={lat:parseFloat(e.latitude),lng:parseFloat(e.longitude)};var n,o,c=new google.maps.Marker({position:p,map:s,title:e.name,visible:!0,icon:"https://maps.google.com/mapfiles/ms/icons/red-dot.png"}),l=function(e){var t={0:r("Lundi","wc-multishippping"),1:r("Mardi","wc-multishippping"),2:r("Mercredi","wc-multishippping"),3:r("Jeudi","wc-multishippping"),4:r("Vendredi","wc-multishippping"),5:r("Samedi","wc-multishippping"),6:r("Dimanche","wc-multishippping")},n='<table class="wms_pickup_open_time">\n                                <tbody>';e.opening_time.map(function(e,i){n+="<tr><td>".concat(t[i],"</td><td> ").concat(e,"</td></tr>")}),n+="</tbody></table>";var o='<div class="wms_pickup_modal_listing_one" data-pickup-name="'.concat(e.name,'">\n                            <div class="wms_pickup_name" data-pickup-name="').concat(e.nom,'">').concat(e.name,'</div>\n                            <div class="wms_pickup_address1" data-pickup-address1="').concat(e.address,'">').concat(e.address,'</div>\n                            <div>\n                                <span class="wms_pickup_zipcode" data-pickup-zipcode="').concat(e.zip_code,'">').concat(e.zip_code,'</span>\n                                <span class="wms_pickup_city" data-pickup-city="').concat(e.city,'">').concat(e.city,'</span>\n                            </div>\n                            <div class="wms_pickup_country" data-pickup-country="').concat(e.country,'">').concat(e.country,"</div> \n                            ").concat(n,'\n                            <button class="button wms_pickup_modal_listing_one_button_ship" data-pickup-id="').concat(e.id,'">\n                                ').concat(r("Envoyer à cette adresse","wc-multishipping"),"\n                            </button>\n                        </div>");return i.innerHTML+=o,new google.maps.InfoWindow({content:o.replace("wms_pickup_modal_listing_one_button_ship","wms_pickup_modal_infowindow_one_button_ship")})}(e);o=l,(n=c).addListener("click",function(){t&&t.close(),o.open(s,n),t=o,d(),google.maps.event.addListener(t,"domready",function(){var e=document.getElementsByClassName("wms_pickup_modal_infowindow_one_button_ship");null!=e&&_(e)});var e=document.querySelector('[data-pickup-name="'.concat(this.title,'"]'));e.scrollIntoView(),e.classList.add("wms_is_selected")}),a.push(c),u.extend(p)}),s.fitBounds(u),function(){var e,t=_createForOfIteratorHelper(document.getElementsByClassName("wms_pickup_modal_listing_one"));try{for(t.s();!(e=t.n()).done;){var i=e.value;i.addEventListener("click",function(){d(),this.classList.add("wms_is_selected")})}}catch(e){t.e(e)}finally{t.f()}}(),_()})}function d(){var e,t=_createForOfIteratorHelper(document.getElementsByClassName("wms_pickup_modal_listing_one"));try{for(t.s();!(e=t.n()).done;){e.value.classList.remove("wms_is_selected")}}catch(e){t.e(e)}finally{t.f()}}function _(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;null==t&&(t=document.getElementsByClassName("wms_pickup_modal_listing_one_button_ship"));var i,n=_createForOfIteratorHelper(t);try{for(n.s();!(i=n.n()).done;){i.value.addEventListener("click",function(){document.querySelector('[id="wms_pickup_point"]').value=this.getAttribute("data-pickup-id");var t=this.closest(".wms_pickup_modal_listing_one"),i=t.getAttribute("data-pickup-name"),n=(this.getAttribute("data-pickup-id"),t.querySelector(".wms_pickup_address1").getAttribute("data-pickup-address1")),o=t.querySelector(".wms_pickup_zipcode").getAttribute("data-pickup-zipcode"),a=t.querySelector(".wms_pickup_city").getAttribute("data-pickup-city"),s=t.querySelector(".wms_pickup_country").getAttribute("data-pickup-country"),p=[i,n,a+" "+o,s],u=e("#wms_pickup_selected");confirm(sprintf(r("Merci de confirmer votre choix: %s","wc-multishipping"),"\n\n"+p.join("\n")))&&(u.html("<strong>"+r("Votre colis sera envoyé à cette adresse:","wc-multishipping")+"</strong><div>"+p.join("</div><div>")+"</div>"),e("#wms_pickup_info").val(JSON.stringify({pickup_name:i,pickup_address:n,pickup_city:a,pickup_zipcode:o,pickup_country:s})),c.querySelector(".modal-close").click())})}}catch(e){n.e(e)}finally{n.f()}}});