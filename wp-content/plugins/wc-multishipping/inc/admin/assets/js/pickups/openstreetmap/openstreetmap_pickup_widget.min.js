"use strict";function _createForOfIteratorHelper(e,t){var i;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(i=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,r=function(){};return{s:r,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,c=!0,o=!1;return{s:function(){i=e[Symbol.iterator]()},n:function(){var e=i.next();return c=e.done,e},e:function(e){o=!0,a=e},f:function(){try{c||null==i.return||i.return()}finally{if(o)throw a}}}}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}jQuery(function(e){var t;p(),window.set_wms_openstreetmap_pickup_modal=p;var i,n=wp.i18n,r=n.__,a=(n._x,n._n,n._nx,[]),c="",o=null;function p(){var t=document.getElementsByClassName("wms_pickup_open_modal_openstreetmap");if(0!==t.length){var n,r=_createForOfIteratorHelper(t);try{for(r.s();!(n=r.n()).done;){n.value.addEventListener("click",function(){e(this).WCBackboneModal({template:this.getAttribute("wms-pickup-modal-id")}),c=document.getElementById(this.getAttribute("wms-pickup-modal-id")),i=c.querySelector(".wc-backbone-modal-loader"),e("#wms_shipping_provider").val(this.getAttribute("wms-shipping-provider")),s()})}}catch(e){r.e(e)}finally{r.f()}}}function s(){o=L.map("wms_pickup_modal_map_openstreemap").setView([48.866667,2.333333],14),L.tileLayer("https://{s}.tile.openstreetmap.fr/osmfr/{z}/{x}/{y}.png",{attribution:'&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',minZoom:1,maxZoom:20}).addTo(o);c.querySelector(".wms_pickup_modal_address_city_input").value="Paris";c.querySelector(".wms_pickup_modal_address_zipcode_input").value="75001";c.querySelector(".wms_pickup_modal_address_country_select select").value="FR",u(),c.querySelector(".wms_pickup_modal_address_search").addEventListener("click",function(){!function(){for(var e=0;e<a.length;e++)o.removeLayer(a[e])}(),u()})}function u(){var n=function(){var e=c.querySelector(".wms_pickup_modal_address_country_select select").value;return""===e&&(e="FR"),{country:e,zipcode:c.querySelector(".wms_pickup_modal_address_zipcode_input").value,city:c.querySelector(".wms_pickup_modal_address_city_input").value}}(),p=n.country,s=n.zipcode,u=n.city;if(""===s||""===u)return alert(r("Merci de définir une adresse valide","wc-multishipping")),!1;i.style.display="block";var m=e("#wms_nonce").val(),y={action:"wms_get_pickup_point",shipping_provider:e("#wms_shipping_provider").val(),country:p,zipcode:s,city:u,wms_nonce:m};return i.style.display="none",e.get(ajaxurl,y).then(function(e){if(e.error)return i=e.error_message,c.querySelector(".wms_pickup_modal_listing").innerHTML='<div style="color: #ff0000">'.concat(i,"</div>"),!1;var i;(t=c.querySelector(".wms_pickup_modal_listing")).innerHTML="",e.data.map(function(i){e.data[0]==i&&o.setView([parseFloat(i.latitude),parseFloat(i.longitude)],13);var n=L.marker([parseFloat(i.latitude),parseFloat(i.longitude)],{title:i.name}).addTo(o),c=function(e){var i={0:r("Lundi","wc-multishippping"),1:r("Mardi","wc-multishippping"),2:r("Mercredi","wc-multishippping"),3:r("Jeudi","wc-multishippping"),4:r("Vendredi","wc-multishippping"),5:r("Samedi","wc-multishippping"),6:r("Dimanche","wc-multishippping")},n='<table class="wms_pickup_open_time">\n                                <tbody>';e.opening_time.map(function(e,t){n+="<tr><td>".concat(i[t],"</td><td> ").concat(e,"</td></tr>")}),n+="</tbody></table>";var a='<div class="wms_pickup_modal_listing_one" data-pickup-name="'.concat(e.name,'">\n                            <div class="wms_pickup_name" data-pickup-name="').concat(e.nom,'">').concat(e.name,'</div>\n                            <div class="wms_pickup_address1" data-pickup-address1="').concat(e.address,'">').concat(e.address,'</div>\n                            <div>\n                                <span class="wms_pickup_zipcode" data-pickup-zipcode="').concat(e.zip_code,'">').concat(e.zip_code,'</span>\n                                <span class="wms_pickup_city" data-pickup-city="').concat(e.city,'">').concat(e.city,'</span>\n                            </div>\n                            <div class="wms_pickup_country" data-pickup-country="').concat(e.country,'">').concat(e.country,"</div> \n                            ").concat(n,'\n                            <button class="button wms_pickup_modal_listing_one_button_ship" data-pickup-id="').concat(e.id,'">\n                                ').concat(r("Envoyer à cette adresse","wc-multishipping"),"\n                            </button>\n                        </div>");return t.innerHTML+=a,a.replace("wms_pickup_modal_listing_one_button_ship","wms_pickup_modal_infowindow_one_button_ship")}(i);n.bindPopup("<b>"+i.name+"</b><br>"+c).on("click",d),a.push(n)}),function(){var e,t=_createForOfIteratorHelper(document.getElementsByClassName("wms_pickup_modal_listing_one"));try{for(t.s();!(e=t.n()).done;){var i=e.value;i.addEventListener("click",function(){l(),this.classList.add("wms_is_selected")})}}catch(e){t.e(e)}finally{t.f()}}(),_()})}function l(){var e,t=_createForOfIteratorHelper(document.getElementsByClassName("wms_pickup_modal_listing_one"));try{for(t.s();!(e=t.n()).done;){e.value.classList.remove("wms_is_selected")}}catch(e){t.e(e)}finally{t.f()}}function d(t){l();var i=document.querySelector('.wms_pickup_modal_listing [data-pickup-name="'.concat(t.target.options.title,'"]'));i.scrollIntoView(),i.classList.add("wms_is_selected");var n,r=_createForOfIteratorHelper(document.getElementsByClassName("wms_pickup_modal_infowindow_one_button_ship"));try{for(r.s();!(n=r.n()).done;){var a=n.value;_(e(a))}}catch(e){r.e(e)}finally{r.f()}}function _(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;null==t&&(t=document.getElementsByClassName("wms_pickup_modal_listing_one_button_ship"));var i,n=_createForOfIteratorHelper(t);try{for(n.s();!(i=n.n()).done;){i.value.addEventListener("click",function(){var t=document.querySelector('[id="wms_pickup_point"]');t.value=this.getAttribute("data-pickup-id");var i=new Event("change");t.dispatchEvent(i);var n=this.closest(".wms_pickup_modal_listing_one"),a=n.getAttribute("data-pickup-name"),o=(this.getAttribute("data-pickup-id"),n.querySelector(".wms_pickup_address1").getAttribute("data-pickup-address1")),p=n.querySelector(".wms_pickup_zipcode").getAttribute("data-pickup-zipcode"),s=n.querySelector(".wms_pickup_city").getAttribute("data-pickup-city"),u=n.querySelector(".wms_pickup_country").getAttribute("data-pickup-country"),l=[a,o,s+" "+p,u],d=e("#wms_pickup_selected");confirm(sprintf(r("Merci de confirmer votre choix: %s","wc-multishipping"),"\n\n"+l.join("\n")))&&(d.html("<strong>"+r("Votre colis sera envoyé à cette adresse:","wc-multishipping")+"</strong><div>"+l.join("</div><div>")+"</div>"),e("#wms_pickup_info").val(JSON.stringify({pickup_name:a,pickup_address:o,pickup_city:s,pickup_zipcode:p,pickup_country:u})),c.querySelector(".modal-close").click())})}}catch(e){n.e(e)}finally{n.f()}}});