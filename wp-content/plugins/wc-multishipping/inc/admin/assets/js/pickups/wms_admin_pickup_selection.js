"use strict";jQuery(function(e){function s(){e(".wms_order_select_shipping_method_button").off("click").on("click",function(){e(this).closest(".wms_order_assign_shipping_methods_area").find(".wms_order_available_shipping_methods").toggle()}),e(".wms_order_confirm_selection").off("click").on("click",function(){var s=e(this).closest(".wms_order_available_shipping_methods"),i=s.find('input[name="wms_shipping_method_to_select"]:checked').val(),_=s.find(".wms_order_error_message");e("#wms_order_item_id").val(e(this).closest("tr.shipping").attr("data-order_item_id")),void 0!==i?e(this).closest("form").submit():_.find(".wms_order_pickup_point_error_message").show()}),e('input[name="wms_shipping_method_to_select"]').on("change",function(){var s=e(this).closest(".wms_order_available_shipping_methods"),i=s.find(".wms_order_select_relay"),_=s.find(".wms_order_pickup_point_error_message");_.find(".wms_order_affect_error_message_method").hide(),_.find(".wms_order_pickup_point_error_message").hide(),1===e(this).data().isRelay?i.show():i.hide()})}s(),window.wms_admin_select_pickup=s});