"use strict";function _createForOfIteratorHelper(e,t){var i="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!i){if(Array.isArray(e)||(i=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){i&&(e=i);var n=0,a=function(){};return{s:a,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,c=!0,r=!1;return{s:function(){i=i.call(e)},n:function(){var e=i.next();return c=e.done,e},e:function(e){r=!0,o=e},f:function(){try{c||null==i.return||i.return()}finally{if(r)throw o}}}}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var i=Object.prototype.toString.call(e).slice(8,-1);return"Object"===i&&e.constructor&&(i=e.constructor.name),"Map"===i||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=new Array(t);i<t;i++)n[i]=e[i];return n}jQuery(function(e){var t,i;p(),window.set_wms_google_maps_pickup_modal=p;var n,a=wp.i18n,o=a.__,c=(a._x,a._n,a._nx,[]),r="",s="";function p(){var t=document.getElementsByClassName("wms_pickup_open_modal_google_maps");if(0!==t.length){var i,a=_createForOfIteratorHelper(t);try{for(a.s();!(i=a.n()).done;){i.value.addEventListener("click",function(){e(this).WCBackboneModal({template:this.getAttribute("wms-pickup-modal-id")}),r=document.getElementById(this.getAttribute("wms-pickup-modal-id")),n=r.querySelector(".wc-backbone-modal-loader"),e("#wms_shipping_provider").val(this.getAttribute("wms-shipping-provider")),u()})}}catch(e){a.e(e)}finally{a.f()}}}function u(){var e={zoom:15,mapTypeId:google.maps.MapTypeId.ROADMAP,center:{lat:48.866667,lng:2.333333},disableDefaultUI:!0};s=new google.maps.Map(r.querySelector(".wms_pickup_modal_map"),e);r.querySelector(".wms_pickup_modal_address_city_input").value="Paris";r.querySelector(".wms_pickup_modal_address_zipcode_input").value="75001";r.querySelector(".wms_pickup_modal_address_country_select select").value="FR",l(),r.querySelector(".wms_pickup_modal_address_search").addEventListener("click",function(){c.map(function(e){e.setMap(null)}),c=[],l()})}function l(){var a=function(){var e=r.querySelector(".wms_pickup_modal_address_country_select select").value;return""===e&&(e="FR"),{country:e,zipcode:r.querySelector(".wms_pickup_modal_address_zipcode_input").value,city:r.querySelector(".wms_pickup_modal_address_city_input").value}}(),p=a.country,u=a.zipcode,l=a.city;n.style.display="block";var m=e("#wms_nonce").val(),y={action:"wms_get_pickup_point",shipping_provider:e("#wms_shipping_provider").val(),country:p,zipcode:u,city:l,wms_nonce:m};return e.get(wmsajaxurl,y).then(function(e){if(n.style.display="none",e.error)return a=e.error_message,r.querySelector(".wms_pickup_modal_listing").innerHTML='<div style="color: #ff0000">'.concat(a,"</div>"),!1;var a,p,u=new google.maps.LatLngBounds;(i=r.querySelector(".wms_pickup_modal_listing")).innerHTML="",e.data.map(function(e){p={lat:parseFloat(e.latitude),lng:parseFloat(e.longitude)};var n,a,r=new google.maps.Marker({position:p,map:s,title:e.name,visible:!0,icon:"https://maps.google.com/mapfiles/ms/icons/red-dot.png"}),l=function(e){var t={0:o("Lundi","wc-multishippping"),1:o("Mardi","wc-multishippping"),2:o("Mercredi","wc-multishippping"),3:o("Jeudi","wc-multishippping"),4:o("Vendredi","wc-multishippping"),5:o("Samedi","wc-multishippping"),6:o("Dimanche","wc-multishippping")},n='<table class="wms_pickup_open_time">\n                                <tbody>';e.opening_time.map(function(e,i){n+="<tr><td>".concat(t[i],"</td><td> ").concat(e,"</td></tr>")}),n+="</tbody></table>";var a='<div class="wms_pickup_modal_listing_one" data-pickup-name="'.concat(e.name,'">\n                            <div class="wms_pickup_name" data-pickup-name="').concat(e.nom,'">').concat(e.name,'</div>\n                            <div class="wms_pickup_address1" data-pickup-address1="').concat(e.address,'">').concat(e.address,'</div>\n                            <div class="wms_pickup_address2">\n                                <span class="wms_pickup_zipcode" data-pickup-zipcode="').concat(e.zip_code,'">').concat(e.zip_code,'</span>\n                                <span class="wms_pickup_city" data-pickup-city="').concat(e.city,'">').concat(e.city,'</span>\n                            </div>\n                            <div class="wms_pickup_country" data-pickup-country="').concat(e.country,'">').concat(e.country,"</div> \n                            ").concat(n,'\n                            <button class="button wms_pickup_modal_listing_one_button_ship" data-pickup-id="').concat(e.id,'">\n                                ').concat(o("Envoyer à cette adresse","wc-multishipping"),"\n                            </button>\n                        </div>");return i.innerHTML+=a,new google.maps.InfoWindow({content:a.replace("wms_pickup_modal_listing_one_button_ship","wms_pickup_modal_infowindow_one_button_ship")})}(e);a=l,(n=r).addListener("click",function(){t&&t.close(),a.open(s,n),t=a,d(),google.maps.event.addListener(t,"domready",function(){var e=document.getElementsByClassName("wms_pickup_modal_infowindow_one_button_ship");null!=e&&_(e)});var e=document.querySelector('[data-pickup-name="'.concat(this.title,'"]'));e.scrollIntoView(),e.classList.add("wms_is_selected")}),c.push(r),u.extend(p)}),s.fitBounds(u),function(){var e,t=_createForOfIteratorHelper(document.getElementsByClassName("wms_pickup_modal_listing_one"));try{for(t.s();!(e=t.n()).done;){var i=e.value;i.addEventListener("click",function(){d(),this.classList.add("wms_is_selected")})}}catch(e){t.e(e)}finally{t.f()}}(),_()})}function d(){var e,t=_createForOfIteratorHelper(document.getElementsByClassName("wms_pickup_modal_listing_one"));try{for(t.s();!(e=t.n()).done;){e.value.classList.remove("wms_is_selected")}}catch(e){t.e(e)}finally{t.f()}}function _(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;null==t&&(t=document.getElementsByClassName("wms_pickup_modal_listing_one_button_ship"));var i,n=_createForOfIteratorHelper(t);try{for(n.s();!(i=n.n()).done;){i.value.addEventListener("click",function(){document.querySelector('[id="wms_pickup_point"]').value=this.getAttribute("data-pickup-id");var t=this.closest(".wms_pickup_modal_listing_one"),i=t.getAttribute("data-pickup-name"),n=(this.getAttribute("data-pickup-id"),t.querySelector(".wms_pickup_address1").getAttribute("data-pickup-address1")),a=t.querySelector(".wms_pickup_zipcode").getAttribute("data-pickup-zipcode"),c=t.querySelector(".wms_pickup_city").getAttribute("data-pickup-city"),s=t.querySelector(".wms_pickup_country").getAttribute("data-pickup-country"),p=[i,n,c+" "+a,s],u=e("#wms_pickup_selected");confirm(sprintf(o("Merci de confirmer votre choix: %s","wc-multishipping"),"\n\n"+p.join("\n")))&&(u.html("<strong>"+o("Votre colis sera envoyé à cette adresse:","wc-multishipping")+"</strong><div>"+p.join("</div><div>")+"</div>"),e("#wms_pickup_info").val(JSON.stringify({pickup_name:i,pickup_address:n,pickup_city:c,pickup_zipcode:a,pickup_country:s})),r.querySelector(".modal-close").click())})}}catch(e){n.e(e)}finally{n.f()}}});