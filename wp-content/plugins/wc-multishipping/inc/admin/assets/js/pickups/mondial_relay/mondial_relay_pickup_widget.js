"use strict";function _createForOfIteratorHelper(e,r){var t="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=_unsupportedIterableToArray(e))||r&&e&&"number"==typeof e.length){t&&(e=t);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,a=!0,c=!1;return{s:function(){t=t.call(e)},n:function(){var e=t.next();return a=e.done,e},e:function(e){c=!0,o=e},f:function(){try{a||null==t.return||t.return()}finally{if(c)throw o}}}}function _unsupportedIterableToArray(e,r){if(e){if("string"==typeof e)return _arrayLikeToArray(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?_arrayLikeToArray(e,r):void 0}}function _arrayLikeToArray(e,r){(null==r||r>e.length)&&(r=e.length);for(var t=0,n=new Array(r);t<r;t++)n[t]=e[t];return n}jQuery(function(e){i(),window.set_wms_mondial_relay_widget_pickup_modal=i;var r=wp.i18n,t=r.__,n=(r._x,r._n,r._nx,"");function i(){var r=document.getElementsByClassName("wms_pickup_open_modal_mondial_relay");if(0!==r.length){var t,i=_createForOfIteratorHelper(r);try{for(i.s();!(t=i.n()).done;){t.value.addEventListener("click",function(){e(this).WCBackboneModal({template:this.getAttribute("wms-pickup-modal-id")}),n=document.getElementById(this.getAttribute("wms-pickup-modal-id")),n.querySelector(".wc-backbone-modal-loader"),e("#wms_shipping_provider").val(this.getAttribute("wms-shipping-provider")),o()})}}catch(e){i.e(e)}finally{i.f()}}}function o(){var r,i,o,a,c,l;e(".wms_pickup_modal_map").MR_ParcelShopPicker({Target:"#wms_pickup_point",TargetDisplayInfoPR:"#wms_pickup_selected",Brand:"CC21X9MZ",Country:"FR",PostCode:"75001",Responsive:!0,NbResults:19,OnParcelShopSelected:function(e){r=e.ID,i=e.Nom,o=e.Adresse1,a=e.CP,c=e.Ville,l=e.Pays}}),e("#wms_select_point").on("click",function(){if(null!=i){var u=[i,o,c,a,l],s=e("#wms_pickup_selected");if(confirm(sprintf(t("Merci de confirmer votre choix: %s","wc-multishipping"),"\n\n"+u.join("\n"))))document.querySelector('[id="wms_pickup_point"]').value=r,s.html("<strong>"+t("Votre colis sera envoyé à cette adresse:","wc-multishipping")+"</strong><div>"+u.join("</div><div>")+"</div>"),e("#wms_pickup_info").val(JSON.stringify({pickup_name:i,pickup_address:o,pickup_city:c,pickup_zipcode:a,pickup_country:l})),n.querySelector(".modal-close").click()}else n.querySelector(".modal-close").click()})}});