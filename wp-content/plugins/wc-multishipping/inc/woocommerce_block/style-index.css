/*!********************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[8].use[1]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[8].use[2]!./src/js/style.scss ***!
  \********************************************************************************************************************************************************************/
/**
*  Converts a hex value into the rgb equivalent.
*
* @param {string} hex - the hexadecimal value to convert
* @return {string} comma separated rgb values
*/
/**
 * Colors
 */
.wcmultishipping-example-component {
  font-size: 20px;
  color: #999999;
}

/*# sourceMappingURL=style-index.css.map*/