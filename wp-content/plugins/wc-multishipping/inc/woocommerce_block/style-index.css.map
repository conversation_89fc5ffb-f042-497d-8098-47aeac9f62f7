{"version": 3, "file": "./style-index.css", "mappings": ";;;AAAA;;;;;CAAA;ACEA;;EAAA;ACFkB;EACjB;EACA,cCCe;ADShB,C", "sources": ["webpack://wcmultishipping/./node_modules/@wordpress/base-styles/_functions.scss", "webpack://wcmultishipping/./node_modules/@wordpress/base-styles/_colors.scss", "webpack://wcmultishipping/./src/js/style.scss", "webpack://wcmultishipping/./src/css/_colors.scss"], "sourcesContent": ["/**\n*  Converts a hex value into the rgb equivalent.\n*\n* @param {string} hex - the hexadecimal value to convert\n* @return {string} comma separated rgb values\n*/\n@function hex-to-rgb($hex) {\n\t@return red($hex), green($hex), blue($hex);\n}\n", "@import \"./functions\";\n\n/**\n * Colors\n */\n\n// WordPress grays.\n$black: #000;\t\t\t// Use only when you truly need pure black. For UI, use $gray-900.\n$gray-900: #1e1e1e;\n$gray-800: #2f2f2f;\n$gray-700: #757575;\t\t// Meets 4.6:1 text contrast against white.\n$gray-600: #949494;\t\t// Meets 3:1 UI or large text contrast against white.\n$gray-400: #ccc;\n$gray-300: #ddd;\t\t// Used for most borders.\n$gray-200: #e0e0e0;\t\t// Used sparingly for light borders.\n$gray-100: #f0f0f0;\t\t// Used for light gray backgrounds.\n$white: #fff;\n\n// Opacities & additional colors.\n$dark-theme-focus: $white;\t// Focus color when the theme is dark.\n$dark-gray-placeholder: rgba($gray-900, 0.62);\n$medium-gray-placeholder: rgba($gray-900, 0.55);\n$light-gray-placeholder: rgba($white, 0.65);\n\n// Alert colors.\n$alert-yellow: #f0b849;\n$alert-red: #cc1818;\n$alert-green: #4ab866;\n", "@import \"colors\"; .wcmultishipping-example-component {\n font-size: 20px;\n color: $example_color;\n}\n", "@import \"node_modules/@wordpress/base-styles/colors\";\n\n// Bright colors\n$example_color: #999999;"], "names": [], "sourceRoot": ""}