{"version": 3, "file": "index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACkD;AACD;AAC8B;AAC/E;AACA;AACA;AACsB;AAEtB,MAAMG,qBAAqB,GAAGF,iEAAU,CAAC,sBAAsB,CAAC;AAGhE,MAAMG,iBAAiB,GAAGA,CAAC;EAC1BC,IAAI;EACJC;AACD,CAAC,KAAK;EAEL,IAAIC,wBAAwB;IAAEC,uBAAuB,GAAGC,SAAS;EAEjE,IAAGA,SAAS,IAAIJ,IAAI,EAAEK,aAAa,CAAC,CAAC,CAAC,EAAE;;EAExC;EACA,KAAI,IAAIC,mBAAmB,IAAIN,IAAI,EAAEK,aAAa,CAAC,CAAC,CAAC,EAAEE,cAAc,EAAC;IACrE,IAAGD,mBAAmB,CAACE,QAAQ,KAAK,IAAI,EAAC;MACxCN,wBAAwB,GAAGI,mBAAmB,CAACG,SAAS;IACzD;EACD;EAEA,IAAGL,SAAS,IAAIF,wBAAwB,IACpC,CAAC,CAAC,IAAIA,wBAAwB,CAACQ,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAIR,wBAAwB,CAACQ,OAAO,CAAC,cAAc,CAAE,EAAC;IAC/GP,uBAAuB,GAAG,MAAM;EACjC,CAAC,MAAI;IACJA,uBAAuB,GAAG,OAAO;IACjCQ,mBAAmB,CAAC,CAAC;EACtB;EAGA,OAAOC,iEAAA;IAAKC,EAAE,EAAC,sBAAsB;IAACC,KAAK,EAAE;MAACC,OAAO,EAAEZ;IAAuB;EAAE,GAC/ES,iEAAA;IAAQE,KAAK,EAAE;MACdE,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE,CAAC;MACTC,WAAW,EAAE,OAAO;MACpBC,WAAW,EAAE,OAAO;MACpBC,SAAS,EAAE,EAAE;MACbC,YAAY,EAAE;IACf,CAAE;IACAC,SAAS,EAAC,6BAA6B;IACvCC,mBAAmB,EAAEzB,qBAAqB,CAAC,qBAAqB,CAAE;IAClE0B,sBAAsB,EAAE1B,qBAAqB,CAAC,wBAAwB,CAAE;IACxE2B,YAAY,EAAE3B,qBAAqB,CAAC,cAAc;EAAE,GAEpDA,qBAAqB,CAAC,oBAAoB,CACpC,CAAC,EACTc,iEAAA;IAAKC,EAAE,EAAC;EAAgB,CAAM,CAAC,EAC/BD,iEAAA;IAAKC,EAAE,EAAC;EAA0B,GACjCD,iEAAA,iBAASd,qBAAqB,CAAC,uBAAuB,CAAU,CAAC,EACjEc,iEAAA;IAAKC,EAAE,EAAC;EAAqB,GAAEf,qBAAqB,CAAC,2BAA2B,CAAO,CACnF,CAAC,EACNc,iEAAA;IAAOc,IAAI,EAAC,QAAQ;IAACb,EAAE,EAAC,WAAW;IAACc,KAAK,EAAE7B,qBAAqB,CAAC,OAAO;EAAE,CAAC,CAAC,EAC5Ec,iEAAA;IAAOc,IAAI,EAAC,QAAQ;IAACb,EAAE,EAAC;EAAuB,CAAC,CAAC,EACjDD,iEAAA;IAAOc,IAAI,EAAC,QAAQ;IAACb,EAAE,EAAC;EAAkB,CAAC,CAAC,EAC5CD,iEAAA;IAAOc,IAAI,EAAC,QAAQ;IAACb,EAAE,EAAC;EAAiB,CAAC,CACtC,CAAC;AACP,CAAC;AAED,MAAMe,MAAM,GAAGA,CAAA,KAAM;EACpB,OACChB,iEAAA,CAAAiB,wDAAA,QACCjB,iEAAA,CAACf,2FAAiC,QACjCe,iEAAA,CAACb,iBAAiB,MAAC,CACe,CAClC,CAAC;AAEL,CAAC;AAGDJ,kEAAc,CAAC,iBAAiB,EAAE;EACjCiC,MAAM;EACNE,KAAK,EAAE;AACR,CAAC,CAAC;AAGF,SAASnB,mBAAmBA,CAACT,wBAAwB,EAAC;EACrD,IAAI6B,WAAW,GAAGC,QAAQ,CAACC,sBAAsB,CAAC,6BAA6B,CAAC;EAChF,IAAGF,WAAW,CAACG,MAAM,KAAK,CAAC,EAAE;EAE7B,KAAI,IAAIC,UAAU,IAAIJ,WAAW,EAAC;IAEjC,IAAG3B,SAAS,IAAIF,wBAAwB,EAAE;IAG1C,IAAIkC,6BAA6B,GAAG,EAAE;IACtC,IAAG,CAAC,CAAC,IAAIlC,wBAAwB,CAACQ,OAAO,CAAC,eAAe,CAAC,EAAC;MAC1D2B,MAAM,CAAC,wBAAwB,CAAC,CAACC,GAAG,CAAC,eAAe,CAAC;MACrDF,6BAA6B,GAAGD,UAAU,CAACI,YAAY,CAAC,wBAAwB,CAAC;IAClF,CAAC,MAAK,IAAG,CAAC,CAAC,IAAIrC,wBAAwB,CAACQ,OAAO,CAAC,YAAY,CAAC,EAAC;MAC7D2B,MAAM,CAAC,wBAAwB,CAAC,CAACC,GAAG,CAAC,YAAY,CAAC;MAClDF,6BAA6B,GAAGD,UAAU,CAACI,YAAY,CAAC,qBAAqB,CAAC;IAC/E,CAAC,MAAK,IAAG,CAAC,CAAC,IAAIrC,wBAAwB,CAACQ,OAAO,CAAC,KAAK,CAAC,EAAC;MACtD2B,MAAM,CAAC,wBAAwB,CAAC,CAACC,GAAG,CAAC,KAAK,CAAC;MAC3CF,6BAA6B,GAAGD,UAAU,CAACI,YAAY,CAAC,cAAc,CAAC;IACxE;IAGAF,MAAM,CAACF,UAAU,CAAC,CAACK,UAAU,CAAC,kBAAkB,CAAC;IACjDH,MAAM,CAACF,UAAU,CAAC,CAACM,WAAW,CAAC,CAAC;IAChCJ,MAAM,CAACF,UAAU,CAAC,CAACO,QAAQ,CAACN,6BAA6B,CAAC,CAACM,QAAQ,CAAC,6BAA6B,CAAC;IAClGP,UAAU,CAACQ,WAAW,CAACR,UAAU,CAACS,SAAS,CAAC,IAAI,CAAC,CAAC;IAElD,IAAG,CAAC,CAAC,IAAIR,6BAA6B,CAAC1B,OAAO,CAAC,QAAQ,CAAC,EAAEmC,gCAAgC,CAAC,6BAA6B,EAC/FT,6BAA6B,CAAC,CAAC,KACnD,IAAG,CAAC,CAAC,IAAIA,6BAA6B,CAAC1B,OAAO,CAAC,eAAe,CAAC,EAAEoC,kCAAkC,CAAC,6BAA6B,EAC3GV,6BAA6B,CAAC,CAAC,KACrD,IAAG,CAAC,CAAC,IAAIA,6BAA6B,CAAC1B,OAAO,CAAC,eAAe,CAAC,EAAEqC,kCAAkC,CAAC,6BAA6B,EAC3GX,6BAA6B,CAAC;EAC1D;AACD;;;;;;;;;;;ACtHA;;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;UAEA;UACA;;;;;WCzBA;WACA;WACA;WACA;WACA,+BAA+B,wCAAwC;WACvE;WACA;WACA;WACA;WACA,iBAAiB,qBAAqB;WACtC;WACA;WACA;WACA;WACA,kBAAkB,qBAAqB;WACvC,oHAAoH,iDAAiD;WACrK;WACA,KAAK;WACL;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;;;;WC7BA;WACA;WACA;WACA,eAAe,4BAA4B;WAC3C,eAAe;WACf,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA,8CAA8C;;;;;WCA9C;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;WCNA;;WAEA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA,8CAA8C;;WAE9C;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,iCAAiC,mCAAmC;WACpE;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,MAAM,qBAAqB;WAC3B;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;WACA;WACA;;;;;UEnDA;UACA;UACA;UACA,2FAA2F,kDAAkD;UAC7I", "sources": ["webpack://wcmultishipping/./src/js/index.js", "webpack://wcmultishipping/./src/js/style.scss?2749", "webpack://wcmultishipping/external window [\"wc\",\"blocksCheckout\"]", "webpack://wcmultishipping/external window [\"wc\",\"wcSettings\"]", "webpack://wcmultishipping/external window [\"wp\",\"element\"]", "webpack://wcmultishipping/external window [\"wp\",\"plugins\"]", "webpack://wcmultishipping/webpack/bootstrap", "webpack://wcmultishipping/webpack/runtime/chunk loaded", "webpack://wcmultishipping/webpack/runtime/compat get default export", "webpack://wcmultishipping/webpack/runtime/define property getters", "webpack://wcmultishipping/webpack/runtime/hasOwnProperty shorthand", "webpack://wcmultishipping/webpack/runtime/make namespace object", "webpack://wcmultishipping/webpack/runtime/jsonp chunk loading", "webpack://wcmultishipping/webpack/before-startup", "webpack://wcmultishipping/webpack/startup", "webpack://wcmultishipping/webpack/after-startup"], "sourcesContent": ["/**\n * External dependencies\n */\nimport {registerPlugin} from '@wordpress/plugins';\nimport {getSetting} from '@woocommerce/settings';\nimport {ExperimentalOrderShippingPackages} from '@woocommerce/blocks-checkout';\n/**\n * Internal dependencies\n */\nimport './style.scss';\n\nconst dataFromBlockSettings = getSetting('wcmultishipping_data');\n\n\nconst MyCustomComponent = ({\n\tcart,\n\textensions\n}) => {\n\n\tlet selected_shipping_method, display_wms_block_value = undefined;\n\n\tif(undefined == cart?.shippingRates[0]) return;\n\n\t//Check all shipping methods and pick the selected one\n\tfor(let one_shipping_method of cart?.shippingRates[0]?.shipping_rates){\n\t\tif(one_shipping_method.selected === true){\n\t\t\tselected_shipping_method = one_shipping_method.method_id;\n\t\t}\n\t}\n\n\tif(undefined == selected_shipping_method ||\n\t   (-1 == selected_shipping_method.indexOf(\"relais\") && -1 == selected_shipping_method.indexOf(\"2shop\") && -1 == selected_shipping_method.indexOf(\"access_point\"))){\n\t\tdisplay_wms_block_value = 'none';\n\t}else{\n\t\tdisplay_wms_block_value = 'block';\n\t\tset_wms_popup_class();\n\t}\n\n\n\treturn <div id=\"wms_pickup_woo_block\" style={{display: display_wms_block_value}}>\n\t\t<button style={{\n\t\t\tpadding: 4,\n\t\t\tborder: 2,\n\t\t\tborderColor: 'black',\n\t\t\tborderStyle: 'solid',\n\t\t\tmarginTop: 20,\n\t\t\tmarginBottom: 20\n\t\t}}\n\t\t\t\tclassName=\"wms_pickup_selection_button\"\n\t\t\t\tchronopost_modal_id={dataFromBlockSettings['chronopost_modal_id']}\n\t\t\t\tmondial_relay_modal_id={dataFromBlockSettings['mondial_relay_modal_id']}\n\t\t\t\tups_modal_id={dataFromBlockSettings['ups_modal_id']}\n\t\t>\n\t\t\t{dataFromBlockSettings['choose-pickup-text']}\n\t\t</button>\n\t\t<div id=\"wms_ajax_error\"></div>\n\t\t<div id=\"wms_selected_pickup_desc\">\n\t\t\t<strong>{dataFromBlockSettings['package-shipping-text']}</strong>\n\t\t\t<div id=\"wms_pickup_selected\">{dataFromBlockSettings['please-select-pickup-text']}</div>\n\t\t</div>\n\t\t<input type=\"hidden\" id=\"wms_nonce\" value={dataFromBlockSettings['nonce']}/>\n\t\t<input type=\"hidden\" id=\"wms_shipping_provider\"/>\n\t\t<input type=\"hidden\" id=\"wms_pickup_point\"/>\n\t\t<input type=\"hidden\" id=\"wms_pickup_info\"/>\n\t</div>;\n};\n\nconst render = () => {\n\treturn (\n\t\t<>\n\t\t\t<ExperimentalOrderShippingPackages>\n\t\t\t\t<MyCustomComponent/>\n\t\t\t</ExperimentalOrderShippingPackages>\n\t\t</>\n\t);\n};\n\n\nregisterPlugin('wcmultishipping', {\n\trender,\n\tscope: 'woocommerce-checkout'\n});\n\n\nfunction set_wms_popup_class(selected_shipping_method){\n\tlet wms_buttons = document.getElementsByClassName('wms_pickup_selection_button');\n\tif(wms_buttons.length === 0) return;\n\n\tfor(let wms_button of wms_buttons){\n\n\t\tif(undefined == selected_shipping_method) return;\n\n\n\t\tlet shipping_provider_modal_class = '';\n\t\tif(-1 != selected_shipping_method.indexOf(\"mondial_relay\")){\n\t\t\tjQuery('#wms_shipping_provider').val('mondial_relay');\n\t\t\tshipping_provider_modal_class = wms_button.getAttribute('mondial_relay_modal_id');\n\t\t}else if(-1 != selected_shipping_method.indexOf(\"chronopost\")){\n\t\t\tjQuery('#wms_shipping_provider').val('chronopost');\n\t\t\tshipping_provider_modal_class = wms_button.getAttribute('chronopost_modal_id');\n\t\t}else if(-1 != selected_shipping_method.indexOf(\"ups\")){\n\t\t\tjQuery('#wms_shipping_provider').val('ups');\n\t\t\tshipping_provider_modal_class = wms_button.getAttribute('ups_modal_id');\n\t\t}\n\n\n\t\tjQuery(wms_button).removeAttr(\"wms-backbone-set\");\n\t\tjQuery(wms_button).removeClass();\n\t\tjQuery(wms_button).addClass(shipping_provider_modal_class).addClass('wms_pickup_selection_button');\n\t\twms_button.replaceWith(wms_button.cloneNode(true));\n\n\t\tif(-1 != shipping_provider_modal_class.indexOf(\"google\")) set_wms_google_maps_pickup_modal('wms_pickup_selection_button',\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t   shipping_provider_modal_class);\n\t\telse if(-1 != shipping_provider_modal_class.indexOf(\"openstreetmap\")) set_wms_openstreetmap_pickup_modal('wms_pickup_selection_button',\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t shipping_provider_modal_class);\n\t\telse if(-1 != shipping_provider_modal_class.indexOf(\"mondial_relay\")) set_wms_mondial_relay_pickup_modal('wms_pickup_selection_button',\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t shipping_provider_modal_class);\n\t}\n}\n\n", "// extracted by mini-css-extract-plugin\nexport {};", "module.exports = window[\"wc\"][\"blocksCheckout\"];", "module.exports = window[\"wc\"][\"wcSettings\"];", "module.exports = window[\"wp\"][\"element\"];", "module.exports = window[\"wp\"][\"plugins\"];", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t\"index\": 0,\n\t\"./style-index\": 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkwcmultishipping\"] = self[\"webpackChunkwcmultishipping\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [\"./style-index\"], function() { return __webpack_require__(\"./src/js/index.js\"); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n", ""], "names": ["registerPlugin", "getSetting", "ExperimentalOrderShippingPackages", "dataFromBlockSettings", "MyCustomComponent", "cart", "extensions", "selected_shipping_method", "display_wms_block_value", "undefined", "shippingRates", "one_shipping_method", "shipping_rates", "selected", "method_id", "indexOf", "set_wms_popup_class", "createElement", "id", "style", "display", "padding", "border", "borderColor", "borderStyle", "marginTop", "marginBottom", "className", "chronopost_modal_id", "mondial_relay_modal_id", "ups_modal_id", "type", "value", "render", "Fragment", "scope", "wms_buttons", "document", "getElementsByClassName", "length", "wms_button", "shipping_provider_modal_class", "j<PERSON><PERSON><PERSON>", "val", "getAttribute", "removeAttr", "removeClass", "addClass", "replaceWith", "cloneNode", "set_wms_google_maps_pickup_modal", "set_wms_openstreetmap_pickup_modal", "set_wms_mondial_relay_pickup_modal"], "sourceRoot": ""}