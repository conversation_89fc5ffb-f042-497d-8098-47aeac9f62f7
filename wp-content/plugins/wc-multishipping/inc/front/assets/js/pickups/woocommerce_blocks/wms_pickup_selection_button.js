"use strict";function _createForOfIteratorHelper(e,t){var o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!o){if(Array.isArray(e)||(o=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){o&&(e=o);var n=0,r=function(){};return{s:r,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:r}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,s=!1;return{s:function(){o=o.call(e)},n:function(){var e=o.next();return i=e.done,e},e:function(e){s=!0,a=e},f:function(){try{i||null==o.return||o.return()}finally{if(s)throw a}}}}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var o=Object.prototype.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,n=new Array(t);o<t;o++)n[o]=e[o];return n}function set_wms_popup_class(){var e=document.getElementsByClassName("wms_pickup_selection_button");if(0!==e.length){var t,o=_createForOfIteratorHelper(e);try{for(o.s();!(t=o.n()).done;){var n,r=t.value,a=null===(n=document.getElementsByClassName("wc-block-components-shipping-rates-control__package")[0])||void 0===n||null===(n=n.getElementsByClassName("wc-block-components-radio-control__option-checked")[0])||void 0===n||null===(n=n.firstChild)||void 0===n?void 0:n.value;if(null==a)return;var i="";if(-1!=a.indexOf("mondial_relay"))jQuery("#wms_shipping_provider").val("mondial_relay"),i=r.getAttribute("mondial_relay_modal_id");else if(-1!=a.indexOf("chronopost"))jQuery("#wms_shipping_provider").val("chronopost"),i=r.getAttribute("chronopost_modal_id");else{if(-1==a.indexOf("ups"))return;jQuery("#wms_shipping_provider").val("ups"),i=r.getAttribute("ups_modal_id")}jQuery(r).removeAttr("wms-backbone-set"),jQuery(r).removeClass(),jQuery(r).addClass(i).addClass("wms_pickup_selection_button"),r.replaceWith(r.cloneNode(!0)),-1!=i.indexOf("google")?set_wms_google_maps_pickup_modal("wms_pickup_selection_button",i):-1!=i.indexOf("openstreetmap")?set_wms_openstreetmap_pickup_modal("wms_pickup_selection_button",i):-1!=i.indexOf("mondial_relay")&&set_wms_mondial_relay_pickup_modal("wms_pickup_selection_button",i)}}catch(e){o.e(e)}finally{o.f()}}}jQuery(function(e){e(document.body).on("updated_shipping_method",function(){set_wms_popup_class()}).on("updated_wc_div",function(){set_wms_popup_class()}).on("updated_checkout",function(){set_wms_popup_class()}),e(document).ready(function(){set_wms_popup_class()}),e(document.body).on("change",".wc-block-components-shipping-rates-control__package",function(){set_wms_popup_class()})});