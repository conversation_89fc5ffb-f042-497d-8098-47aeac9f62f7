"use strict";function _createForOfIteratorHelper(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var o=0,i=function(){};return{s:i,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,r=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return r=e.done,e},e:function(e){c=!0,a=e},f:function(){try{r||null==n.return||n.return()}finally{if(c)throw a}}}}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}var wms_is_info_popup_opened,listing_container,modal,loader,wms_map_google;function set_wms_google_maps_pickup_modal(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"wms_pickup_open_modal_google_maps",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=document.getElementsByClassName(e);if(0!==n.length){var o,i=_createForOfIteratorHelper(n);try{for(i.s();!(o=i.n()).done;){var a=o.value;null==a.getAttribute("wms-backbone-set")&&(a.addEventListener("click",function(e){e.preventDefault(),jQuery(this).WCBackboneModal({template:0<t.length?t:this.getAttribute("wms-pickup-modal-id")}),modal=document.getElementById(0<t.length?t:this.getAttribute("wms-pickup-modal-id")),loader=modal.querySelector(".wc-backbone-modal-loader"),init_gmap()}),a.setAttribute("wms-backbone-set",!0))}}catch(e){i.e(e)}finally{i.f()}}}jQuery(function(e){e(document.body).on("updated_shipping_method",function(){set_wms_google_maps_pickup_modal()}).on("updated_wc_div",function(){set_wms_google_maps_pickup_modal()}).on("updated_checkout",function(){set_wms_google_maps_pickup_modal()}),set_wms_google_maps_pickup_modal()});var __=wp.i18n.__;function init_gmap(){var e,t,n,o,i,a,r,c,s={zoom:15,mapTypeId:google.maps.MapTypeId.ROADMAP,center:{lat:48.866667,lng:2.333333},disableDefaultUI:!0};wms_map_google=new google.maps.Map(modal.querySelector("#wms_pickup_modal_map_googlemaps"),s);var p=(null===(e=document.getElementById("ship-to-different-address-checkbox"))||void 0===e?void 0:e.checked)||!1,_="Paris",l=document.getElementById("shipping_city"),u=document.getElementById("billing_city"),d=document.getElementById("shipping-city"),m=document.getElementById("billing-city");l&&p?_=l:u?_=u:d?_=d:m&&(_=m),modal.querySelector(".wms_pickup_modal_address_city_input").value=null!==(t=_.value)&&void 0!==t?t:_;var g="75001",y=document.getElementById("shipping_postcode"),v=document.getElementById("billing_postcode"),f=document.getElementById("shipping-postcode"),k=document.getElementById("billing-postcode");y&&p?g=y:v?g=v:f?g=f:k&&(g=k),modal.querySelector(".wms_pickup_modal_address_zipcode_input").value=null!==(n=g.value)&&void 0!==n?n:g;var w="FR",h=document.getElementById("shipping_country"),b=document.getElementById("billing_country"),I=null!==(o=null===(i=document.getElementById("shipping-country"))||void 0===i?void 0:i.querySelector("input"))&&void 0!==o?o:document.getElementById("shipping-country"),S=null!==(a=null===(r=document.getElementById("billing-country"))||void 0===r?void 0:r.querySelector("input"))&&void 0!==a?a:document.getElementById("billing-country");if(h&&p)w=h;else if(b)w=b;else if(I){var E,j=_createForOfIteratorHelper(modal.querySelector(".wms_pickup_modal_address_country_select select").options);try{for(j.s();!(E=j.n()).done;){if((q=E.value).text===I.value||q.value===I.value){w=q;break}}}catch(e){j.e(e)}finally{j.f()}}else if(S){var B,A=_createForOfIteratorHelper(modal.querySelector(".wms_pickup_modal_address_country_select select").options);try{for(A.s();!(B=A.n()).done;){var q;if((q=B.value).text===S.value||q.value===S.value){w=q;break}}}catch(e){A.e(e)}finally{A.f()}}modal.querySelector(".wms_pickup_modal_address_country_select select").value=null!==(c=w.value)&&void 0!==c?c:w,get_pickup_point_ajax_gmaps(),set_button_reload_points_gmaps()}function get_address_modal_gmaps(){var e=modal.querySelector(".wms_pickup_modal_address_country_select select").value;return""===e&&(e="FR"),{country:e,zipcode:modal.querySelector(".wms_pickup_modal_address_zipcode_input").value,city:modal.querySelector(".wms_pickup_modal_address_city_input").value}}function get_pickup_point_ajax_gmaps(){var e=get_address_modal_gmaps(),t=e.country,n=e.zipcode,o=e.city;loader.style.display="block";var i=jQuery("#wms_nonce").val(),a={action:"wms_get_pickup_point",shipping_provider:jQuery("#wms_shipping_provider").val(),country:t,zipcode:n,city:o,wms_nonce:i};return jQuery.get(WMS.ajaxurl,a).then(function(e){if(loader.style.display="none",e.error)return set_error_message(e.error_message),!1;var t,n=new google.maps.LatLngBounds;(listing_container=modal.querySelector(".wms_pickup_modal_listing")).innerHTML="",e.data.map(function(e){t={lat:parseFloat(e.latitude),lng:parseFloat(e.longitude)};var o=new google.maps.Marker({position:t,map:wms_map_google,title:e.name,visible:!0,icon:"https://maps.google.com/mapfiles/ms/icons/red-dot.png"});set_marker_onclick_actions_gmaps(o,wms_generate_map_popup_gmaps(e)),markers.push(o),n.extend(t)}),wms_map_google.fitBounds(n),set_select_point_gmaps(),set_ship_here_button_onclick_action_gmaps();var o,i=_createForOfIteratorHelper(document.getElementsByClassName("wms_pickup_modal_listing_one_button_ship"));try{for(i.s();!(o=i.n()).done;)o.value}catch(e){i.e(e)}finally{i.f()}})}function wms_generate_map_popup_gmaps(e){var t={0:__("Lundi","wc-multishippping"),1:__("Mardi","wc-multishippping"),2:__("Mercredi","wc-multishippping"),3:__("Jeudi","wc-multishippping"),4:__("Vendredi","wc-multishippping"),5:__("Samedi","wc-multishippping"),6:__("Dimanche","wc-multishippping")},n='<table class="wms_pickup_open_time">\n                                <tbody>';e.opening_time.map(function(e,o){n+="<tr><td>".concat(t[o],"</td><td> ").concat(e,"</td></tr>")}),n+="</tbody></table>";var o='<div class="wms_pickup_modal_listing_one" data-pickup-name="'.concat(e.name,'">\n                            <div class="wms_pickup_name" data-pickup-name="').concat(e.nom,'">').concat(e.name,'</div>\n                            <div class="wms_pickup_address1" data-pickup-address1="').concat(e.address,'">').concat(e.address,'</div>\n                            <div class="wms_pickup_address2">\n                                <span class="wms_pickup_zipcode" data-pickup-zipcode="').concat(e.zip_code,'">').concat(e.zip_code,'</span>\n                                <span class="wms_pickup_city" data-pickup-city="').concat(e.city,'">').concat(e.city,'</span>\n                            </div>\n                            <div class="wms_pickup_country" data-pickup-country="').concat(e.country,'">').concat(e.country,"</div> \n                            ").concat(n,'\n                            <button class="button wms_pickup_modal_listing_one_button_ship" data-pickup-id="').concat(e.id,'">\n                                ').concat(__("Envoyer à cette adresse","wc-multishipping"),"\n                            </button>\n                        </div>");return listing_container.innerHTML+=o,new google.maps.InfoWindow({content:o.replace("wms_pickup_modal_listing_one_button_ship","wms_pickup_modal_infowindow_one_button_ship")})}function set_button_reload_points_gmaps(){modal.querySelector(".wms_pickup_modal_address_search").addEventListener("click",function(){clear_markers_on_map_gmaps(),get_pickup_point_ajax_gmaps()})}function clear_markers_on_map_gmaps(){markers.map(function(e){e.setMap(null)}),markers=[]}function set_error_message(e){modal.querySelector(".wms_pickup_modal_listing").innerHTML='<div style="color: #ff0000">'.concat(e,"</div>")}function set_select_point_gmaps(){var e,t=_createForOfIteratorHelper(document.getElementsByClassName("wms_pickup_modal_listing_one"));try{for(t.s();!(e=t.n()).done;){e.value.addEventListener("click",function(){unselect_points_gmaps(),this.classList.add("wms_is_selected")})}}catch(e){t.e(e)}finally{t.f()}}function unselect_points_gmaps(){var e,t=_createForOfIteratorHelper(document.getElementsByClassName("wms_pickup_modal_listing_one"));try{for(t.s();!(e=t.n()).done;){e.value.classList.remove("wms_is_selected")}}catch(e){t.e(e)}finally{t.f()}}function set_marker_onclick_actions_gmaps(e,t){e.addListener("click",function(){wms_is_info_popup_opened&&wms_is_info_popup_opened.close(),t.open(wms_map_google,e),wms_is_info_popup_opened=t,unselect_points_gmaps(),google.maps.event.addListener(wms_is_info_popup_opened,"domready",function(){var e=document.getElementsByClassName("wms_pickup_modal_infowindow_one_button_ship");null!=e&&set_ship_here_button_onclick_action_gmaps(e)});var n=document.querySelector('[data-pickup-name="'.concat(this.title,'"]'));n.scrollIntoView(),n.classList.add("wms_is_selected")})}function set_ship_here_button_onclick_action_gmaps(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;null==e&&(e=document.getElementsByClassName("wms_pickup_modal_listing_one_button_ship"));var t,n=_createForOfIteratorHelper(e);try{for(n.s();!(t=n.n()).done;){t.value.addEventListener("click",function(){var e=document.querySelector('[id="wms_pickup_point"]');e.value=this.getAttribute("data-pickup-id");var t=new Event("change");e.dispatchEvent(t);var n=this.closest(".wms_pickup_modal_listing_one"),o=n.getAttribute("data-pickup-name"),i=this.getAttribute("data-pickup-id"),a=n.querySelector(".wms_pickup_address1").getAttribute("data-pickup-address1"),r=n.querySelector(".wms_pickup_zipcode").getAttribute("data-pickup-zipcode"),c=n.querySelector(".wms_pickup_city").getAttribute("data-pickup-city"),s=n.querySelector(".wms_pickup_country").getAttribute("data-pickup-country"),p=[o,a,c+" "+r,s],_=jQuery("#wms_ajax_error"),l=jQuery("#wms_pickup_selected"),u=jQuery("#wms_shipping_provider").val(),d=jQuery("#wms_nonce").val();confirm(sprintf(__("Merci de confirmer votre choix: %s","wc-multishipping"),"\n\n"+p.join("\n")))&&jQuery.ajax({url:WMS.ajaxurl,type:"POST",dataType:"json",data:{action:"wms_select_pickup_point",pickup_id:i,pickup_name:o,pickup_address:a,pickup_zipcode:r,pickup_city:c,pickup_country:s,pickup_provider:u,wms_nonce:d},beforeSend:function(){_.hide()},success:function(e){if(!1===e.error){l.html("<div>"+p.join("</div><div>")+"</div>"),jQuery("#wms_pickup_info").innerText=JSON.stringify(p),modal.querySelector(".modal-close").click();var t,n=_createForOfIteratorHelper(jQuery(".wc-block-components-shipping-address"));try{for(n.s();!(t=n.n()).done;){var o=t.value;-1!=jQuery(o).html().indexOf("Livraison à")&&jQuery(o).html("Livraison à : "+p.join("\n"))}}catch(e){n.e(e)}finally{n.f()}jQuery("body").trigger("update_checkout")}else _.html(e.error_message),_.show()}})})}}catch(e){n.e(e)}finally{n.f()}}