"use strict";function _createForOfIteratorHelper(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,l=!0,a=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return l=e.done,e},e:function(e){a=!0,i=e},f:function(){try{l||null==n.return||n.return()}finally{if(a)throw i}}}}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(e,t):void 0}}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var modal,loader;jQuery(function(e){e(document.body).on("updated_shipping_method",function(){set_wms_mondial_relay_pickup_modal()}).on("updated_wc_div",function(){set_wms_mondial_relay_pickup_modal()}).on("updated_checkout",function(){set_wms_mondial_relay_pickup_modal()})});var __=wp.i18n.__;function set_wms_mondial_relay_pickup_modal(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"wms_pickup_open_modal_mondial_relay",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=document.getElementsByClassName(e);if(0!==n.length){var r,o=_createForOfIteratorHelper(n);try{for(o.s();!(r=o.n()).done;){var i=r.value;null==i.getAttribute("wms-backbone-set")&&(i.addEventListener("click",function(e){e.preventDefault(),jQuery(this).WCBackboneModal({template:0<t.length?t:this.getAttribute("wms-pickup-modal-id")}),modal=document.getElementById(0<t.length?t:this.getAttribute("wms-pickup-modal-id")),loader=modal.querySelector(".wc-backbone-modal-loader"),init_mondial_relay_map()}),i.setAttribute("wms-backbone-set",!0))}}catch(e){o.e(e)}finally{o.f()}}}function init_mondial_relay_map(){var e,t,n,r,o,i,l,a,u,c,d,p,s,m=(null===(e=document.getElementById("ship-to-different-address-checkbox"))||void 0===e?void 0:e.checked)||!1,_="75001",y=document.getElementById("shipping_postcode"),f=document.getElementById("billing_postcode"),v=document.getElementById("shipping-postcode"),g=document.getElementById("billing-postcode");y&&m?_=y:f?_=f:v?_=v:g&&(_=g),_=null!==(t=_.value)&&void 0!==t?t:_;var h="FR",k=document.getElementById("shipping_country"),b=document.getElementById("billing_country"),w=null!==(n=null===(r=document.getElementById("shipping-country"))||void 0===r?void 0:r.querySelector("input"))&&void 0!==n?n:document.getElementById("shipping-country"),j=null!==(o=null===(i=document.getElementById("billing-country"))||void 0===i?void 0:i.querySelector("input"))&&void 0!==o?o:document.getElementById("billing-country");k?h=k:b?h=b:w?h=w.value:j&&(h=j.value),h=null!==(l=h.value)&&void 0!==l?l:h,jQuery(".wms_pickup_modal_map").MR_ParcelShopPicker({Target:"#wms_pickup_point",Brand:"CC21X9MZ",Country:h,PostCode:_,Responsive:!0,NbResults:19,OnParcelShopSelected:function(e){a=e.ID,u=e.Nom,c=e.Adresse1,d=e.CP,p=e.Ville,s=e.Pays}}),jQuery("#wms_select_point").on("click",function(){if(null!=u){var e=[u,c,p+" "+d,s],t=jQuery("#wms_ajax_error"),n=jQuery("#wms_pickup_selected"),r=jQuery("#wms_shipping_provider").val(),o=jQuery("#wms_nonce").val();confirm(sprintf(__("Merci de confirmer votre choix: %s","wc-multishipping"),"\n\n"+e.join("\n")))&&jQuery.ajax({url:WMS.ajaxurl,type:"POST",dataType:"json",data:{action:"wms_select_pickup_point",pickup_id:a,pickup_name:u,pickup_address:c,pickup_zipcode:d,pickup_city:p,pickup_country:s,pickup_provider:r,wms_nonce:o},beforeSend:function(){t.hide()},success:function(r){if(!1===r.error){n.html("<div>"+e.join("</div><div>")+"</div>"),jQuery("#wms_pickup_info").innerText=JSON.stringify(e),modal.querySelector(".modal-close").click();var o,i=_createForOfIteratorHelper(jQuery(".wc-block-components-shipping-address"));try{for(i.s();!(o=i.n()).done;){var l=o.value;-1!=jQuery(l).html().indexOf("Livraison à")&&jQuery(l).html("Livraison à : "+e.join("\n"))}}catch(e){i.e(e)}finally{i.f()}jQuery("body").trigger("update_checkout")}else t.html(r.error_message),t.show()}})}else modal.querySelector(".modal-close").click()})}jQuery(function(e){set_wms_mondial_relay_pickup_modal()});