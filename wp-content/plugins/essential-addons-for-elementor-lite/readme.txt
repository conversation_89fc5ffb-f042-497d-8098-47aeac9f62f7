=== Essential Addons for Elementor - Popular Elementor Templates & Widgets ===
Contributors: wpdev<PERSON><PERSON>, <PERSON><PERSON>, re_enter_rupok, Asif2B<PERSON>, pri<PERSON><PERSON><PERSON>l, sum<PERSON><PERSON><PERSON><PERSON><PERSON>, rudlinkon, nhr<PERSON>b, j<PERSON><PERSON><PERSON><PERSON>, himadree12
Tags: elementor, elementor addons, elementor widgets, elementor templates, elementor woocommerce
Requires at least: 5.0
Tested up to: 6.8
Requires PHP: 7.0
Stable tag: 6.2.3
License: GPLv3
License URI: https://opensource.org/licenses/GPL-3.0

Elementor addon offering 100+ widgets and templates — Elementor Gallery, Slider, Form, Post Grid, Menu, Accordion, WooCommerce & more.

== Description ==

Enhance **[Elementor](https://wordpress.org/plugins/elementor/)** page building experience with 100+ creative elements and extensions. Add powers to your website builder using our easy-to-use Elementor widgets and ready Elementor templates, which were designed to make your next WordPress website design easier and prettier than ever before.

= Essential Addons for Elementor offers 100+ best Elementor widgets with kits, 5,500+ ready templates & best WooCommerce builder, serving 2 million+ active users. =

[Check the Demos](https://essential-addons.com/demos)

### Completely Customizable
Each widget and extension comes with a bunch of options to customize your website in every possible way. You can achieve nearly any design of your imagination.

### Light Weight & Instant Loading
No extra resources or messy codes to slow down your website. Optimized for super fast loading and instant Live editing.

### Elements Control option
Enable and disable individual elements adding to make your page load faster and smoother. You can deactivate unnecessary widgets to keep the site light.


### 6000+ Ready WordPress Templates & Blocks through **Templately**
Grab access to 3,000+ pre-made Elementor templates from the ultimate templates cloud for WordPress, Templately, that come with Essential Addons.Unlock stunning Elementor templates, blocks & sections;and design your website to stand out from the crowd.

https://youtu.be/Z73muoczARc

Each of these Elementor templates is completely customizable and responsive, and also seamlessly compatible with Essential Addons. You can personalize them effortlessly by changing font, color, etc.

Templately also offers advanced features and facilities to save your templates to the cloud. Store all your ready and customized designs with Templately & deploy on hundreds of websites with 1-click. Increase productivity and power up your whole team to build websites faster than ever before.

Ready Elementor Templates From Templately:

- [Textivy AI - AI SaaS Template](https://templately.com/pack/textivy-ai-elementor-ai-sass)
- [KetoFlow - Keto Diet Website Template](https://templately.com/pack/ketoflow-elementor-keto-diet-template)
- [IllustrateAI - AI Photo Creation Website Template](https://templately.com/pack/illustrateai-elementor-ai-photo-creation)
- [HeartBloom - Valentine Gift Store Template](https://templately.com/pack/heartbloom-elementor-valentine-gift-store)


### Expert Support
We have an extraordinary support team ready to help you. Ask your questions in the support forum, or contact us directly through live chat and contact form.

### [Elementor](https://wordpress.org/plugins/elementor/) Website Builder is required for this plugin.


### 60+ FREE ELEMENTOR ADDONS AND COUNTING

We have designed more than 50 of the most useful widgets to enhance your Elementor Page Building experience and allow you to climb the top of your design capabilities.

1. [Post Grid](https://essential-addons.com/post-grid/) - Showcase your blog posts in 3 unique styles
2. [Post Timeline](https://essential-addons.com/post-timeline/) - Create stunning timeline for your posts & pages
3. [Fancy Text](https://essential-addons.com/fancy-text/ ) - Design pages using animated texts in 8 different styles
4. [Creative Buttons](https://essential-addons.com/creative-buttons/) - Quickly add modern buttons with hover effects
5. [Countdown](https://essential-addons.com/countdown/ ) - Include a countdown timer to boost click-through rates
6. [Team Members](https://essential-addons.com/team-members/) - Feature your team members with a few clicks
7. [Testimonials](https://essential-addons.com/testimonials/) - Showcase customer reviews & increase credibility
8. [WooCommerce Product Grid](https://essential-addons.com/woo-product-grid/) - Display WooCommerce products in grid layout
9. [Contact Form 7](https://essential-addons.com/contact-form-7/) - Customize beautiful forms without any coding
10. [Gravity Forms](https://essential-addons.com/gravity-forms/) - Design forms with Elementor & Gravity Forms
11. [Ninja Forms](https://essential-addons.com/ninja-forms/) - Easily create your form style just the way you want
12. [Caldera Forms](https://essential-addons.com/caldera-forms/) - Design effective mobile-friendly forms
13. [WPForms](https://essential-addons.com/wpforms/) - Create powerful WordPress forms in minutes
14. [weForms](https://essential-addons.com/weforms/) - Get the fastest form building experience
15. [Info Box](https://essential-addons.com/info-box/) - Design beautiful info box from predefined styles
16. [Flip Box](https://essential-addons.com/flip-box/) - Use animated Flip Boxes to highlight any content
17. [Dual Color Heading](https://essential-addons.com/dual-color-headline/) - Highlight your headings with dual colors
18. [Call to Action](https://essential-addons.com/call-to-action/) - Design call-to-action buttons with a few clicks
19. [Pricing Table](https://essential-addons.com/pricing-table/) - Create Pricing Tables within minutes that converts
20. [Twitter Feed](https://essential-addons.com/twitter-feed/) - Showcase your latest tweets to your visitor
21. [Data Table](https://essential-addons.com/table/) - Insert attractive data table anywhere you want
22. [Filterable Gallery](https://essential-addons.com/filterable-gallery/) - Share images with an interactive gallery
23. [Image Accordion](https://essential-addons.com/image-accordion/) - Highlight your images with amazing hover effects
24. [Content Ticker](https://essential-addons.com/content-ticker/) - Use slider & ticker effects to display your content
25. [Tooltip](https://essential-addons.com/tooltip/) - Set tooltip for Icon, Image, Text or shortcodes
26. [Advanced Tabs](https://essential-addons.com/advanced-tabs/) - Display information neatly in nested tabs
27. [Advanced Accordion](https://essential-addons.com/advanced-accordion/) - Add beautiful nested accordions anywhere
28. [Progress Bar](https://essential-addons.com/progress-bar/) - Display progress bars with 4+ different styles
29. [Feature List](https://essential-addons.com/feature-list/) - Display feature lists with custom icons and styles
30. [Fluent Forms](https://essential-addons.com/fluent-forms/) - Design your Fluent Forms container, fields and anything you want
31. [Facebook Feed](https://essential-addons.com/facebook-feed/) - Display Facebook post feed from your page
32. [Sticky Video](https://essential-addons.com/sticky-video/) - Add videos that are sticky on scroll while playing
33. [BetterDocs Category Grid](https://essential-addons.com/betterdocs-category-grid/) - Create documentation in grid layout
34. [BetterDocs Category Box](https://essential-addons.com/betterdocs-category-box/) - Create documentation in box layout
35. [BetterDocs Search Form](https://essential-addons.com/betterdocs-search-form/) - Add a live search box for documentation
36. [Advanced Data Table](https://essential-addons.com/advanced-data-table) - Design large data tables without coding
37. [Event Calendar](https://essential-addons.com/event-calendar) - Create customized event pages
38. [Formstack](https://essential-addons.com/formstack/) - Design your Formstack forms
39. [Woo Checkout](https://essential-addons.com/woo-checkout/) -  Build attractive WooCommerce Checkout pages
[youtube](https://www.youtube.com/watch?v=l3GNAJHza5c)
40. [Typeform](https://essential-addons.com/typeform/) - Embed & design your Typeform forms
41. [Login | Register Form](https://essential-addons.com/login-register-form/) - Create amazing login/registration forms
42. [Woo Product Compare](https://essential-addons.com/woo-product-compare/) - Design stunning product compare tables
43. [Woo Product Carousel](https://essential-addons.com/woo-product-carousel/) - Add Product Carousels for WooCommerce
44. [Simple Menu](https://essential-addons.com/simple-menu/)- Design minimalist navigation menus
45. [Woo Product Gallery](https://essential-addons.com/woo-product-gallery/)- Show your products in a filterable gallery
46. [Woo Cart](https://essential-addons.com/woo-cart/)- Use WooCommerce Cart page to boost online sales
47. [Interactive Circle](https://essential-addons.com/interactive-circle/)- Display content with engaging circular layouts
48. [NFT Gallery](https://essential-addons.com/nft-gallery/)- Add and showcase NFT collections effortlessly
49. [Business Reviews](https://essential-addons.com/business-reviews/)- Display Google Customer Review to increase your online reputation
50. [SVG Draw](https://essential-addons.com/svg-draw/)- Display SVG elements in a visually striking and interactive way on your website
51. [Woo Product List](https://essential-addons.com/woo-product-list/)- Showcase your store’s products with a stunning listicle format
52. [Woo Product Price](https://essential-addons.com/woo-product-price/)- Style your product price with custom colors and typography
53. [Woo Product Rating](https://essential-addons.com/woo-product-rating/)- Show ratings to help customers make informed choices
54. [Woo Product Images](https://essential-addons.com/woo-product-images/)- Display WooCommerce product images with stunning effects
55. [Woo Add to Cart](https://essential-addons.com/woo-add-to-cart/)- Make shopping easier with an ‘Add to Cart’ button
56. [Breadcrumbs](https://essential-addons.com/breadcrumbs/)- Improve navigation with breadcrumbs for easy exploration
57. [Code Snippet](https://essential-addons.com/code-snippet)- Display beautifully formatted code on your Elementor website

Extensions (Free):

1. [Reading Progress Bar](https://essential-addons.com/reading-progress/) - Add progress bar to show reading position
[Watch YouTube Tutorial](https://www.youtube.com/watch?v=3Teo4kSWJPA)
2. [Duplicator](https://essential-addons.com/duplicator/) - Duplicate any page or post in a single click
3. [Table of Contents](https://essential-addons.com/table-of-content/) - Display Table of Contents anywhere you want
[Watch YouTube Tutorial](https://youtu.be/W6woRX7Ud7I)
4. [Custom JS](https://essential-addons.com/custom-js/) - Add custom JavaScript with a few clicks
5. [Scroll to Top](https://essential-addons.com/scroll-to-top) - Let visitors quickly navigate to the top of your page
6. [Wrapper Link](https://essential-addons.com/wrapper-link) - Add custom links into any section, column, or even flexbox
7. [Hover Interactions](https://essential-addons.com/hover-interaction/) - Create captivating effects on buttons, images, texts and more

### More elements (50+) on [Premium Version](https://wpdeveloper.com/in/upgrade-essential-addons-elementor)

1. [Post Block](https://essential-addons.com/post-block/) - Display your blog posts with a variety of styles
2. [Lightbox & Modal](https://essential-addons.com/lightbox-modal/) - Create interactive popups after trigger actions
3. [Testimonial Slider](https://essential-addons.com/testimonial-slider/) - Share customer reviews with animated sliders
4. [Image Comparison](https://essential-addons.com/image-comparison/) - Let your viewers compare between two images
5. [Interactive Promo](https://essential-addons.com/interactive-promo/ ) - Display content with attractive animations
6. [Instagram Feed](https://essential-addons.com/instagram-feed/) - Display Instagram posts beautifully
7. [Advanced Google Map](https://essential-addons.com/advanced-google-map/) - Create maps with unlimited themes
[Watch YouTube Tutorial](https://www.youtube.com/watch?v=kLx9O8ZoBv0)
8. [Static Product](https://essential-addons.com/static-product/) - Present your static product just the way you want
9. [Flip Carousel](https://essential-addons.com/flip-carousel/) - Feature your content using a unique carousel style
10. [Interactive Cards](https://essential-addons.com/interactive-cards/) - Create fascinating effects for your content
11. [Content Timeline](https://essential-addons.com/content-timeline/) - Create memory lane with animated scrolling
12. [Advanced Menu](https://essential-addons.com/advanced-menu/) - Use advanced navigation menu anywhere
13. [Twitter Feed Carousel](https://essential-addons.com/twitter-feed/) - Share Twitter posts in interactive styles
14. [Dynamic Gallery](https://essential-addons.com/dynamic-gallery/) - Add filterable gallery for any content
15. [Smart Post List](https://essential-addons.com/post-list/) - Design your blog page with modern post list
16. [Mailchimp](https://essential-addons.com/mailchimp/) - Design your Mailchimp form with ease
17. [Toggle](https://essential-addons.com/content-toggle/) - Share any content in less space with a toggle
18. [One Page Navigation](https://essential-addons.com/one-page-nav/) - Create one page websites in Elementor
19. [Price Menu](https://essential-addons.com/price-menu/) - Make a beautiful pricing menu for your brands
20. [Image Hotspots](https://essential-addons.com/image-hotspots/) - Add hotspot icons with tooltips in an image
21. [Divider](https://essential-addons.com/divider/) - Separate your section with fancy divider
22. [Counter](https://essential-addons.com/counter/) - Highlight important data using the Counter element
23. [Team Member Carousel](https://essential-addons.com/team-members-carousel/) - Display team members in a carousel
24. [Post Carousel](https://essential-addons.com/post-carousel/) - Use a carousel to display multiple posts
25. [Logo Carousel](https://essential-addons.com/logo-carousel/) - Highlight brands or product logos in a carousel
26. [Protected Content](https://essential-addons.com/protected-content/) -  Lock your content with password protection
27. [Offcanvas](https://essential-addons.com/offcanvas-content/) - Display content in offcanvas with one click
28. [Image Scroller](https://essential-addons.com/image-scroller/) - Show long height images with image scroller
29. [Woo Product Slider](https://essential-addons.com/woo-product-slider/) - Showcase your products in a stunning slider
30. [Woo Product Collections](https://essential-addons.com/woocommerce-product-collections/)- Show product collections beautifully
31. [LearnDash Course List](https://essential-addons.com/learndash-course-list/)- Share LearnDash course list on website
32. [Advanced Search](https://essential-addons.com/advanced-search/)- Add a customizable, dynamic search bar
33. [Woo Thank You](https://essential-addons.com/woo-thank-you)- Design a personalized thank you message on the WooCommerce order confirmation page
34. [Woo Cross Sells](https://essential-addons.com/woo-cross-sells/)- Customize the display of cross-sell products to boost your sales potential
35. [Woo Account Dashboard](https://essential-addons.com/woo-account-dashboard/)- Design a personalized WooCommerce Customer Account Dashboard
36. [Fancy Chart](https://essential-addons.com/fancy-chart/)- Visualize important data with interactive charts and graphs on your website
37. [Stacked Cards](https://essential-addons.com/stacked-cards/)- Showcase your website content in a distinctive and engaging way
38. [360 Degree Photo Viewer](https://essential-addons.com/360-degree-photo-viewer)- Make your website image visible from every angle with adjustable controls
39. [Multicolumn Pricing Table](https://essential-addons.com/multicolumn-pricing-table/)- Display pricing plans in multiple columns to compare features and choose the best option
40. [Figma to Elementor Converter](https://essential-addons.com/figma-to-elementor-converter/)- Turn Figma designs into fully customizable Elementor Pages


Extensions (Pro):

1. [Parallax](https://essential-addons.com/parallax-scrolling/) - Add creative parallax effects to your content
2. [Particles](https://essential-addons.com/particle-effect/) - Add animated particle effects to your content
3. [Advanced Tooltip](https://essential-addons.com/advanced-tooltip/) - Make any widget more informative with tooltip
4. [Content Protection](https://essential-addons.com/content-protection/) - Hide your content with password protection
5. [Conditional Display](https://essential-addons.com/conditional-display/)- Display your preferred content based on the logic conditions you have set
6. [Dynamic Tags](https://essential-addons.com/dynamic-tags/)- Display content dynamically on your preferred section of the website
7. [Interactive animations](https://essential-addons.com/interactive-animations/)- Create stunning animations & build a fully dynamic web page


More features and improvements are coming on regular updates. Want to unlock the advanced elements? [Upgrade to our Pro version](https://essential-addons.com/upgrade-ea-pro)

### Elementor WooCommerce Widgets From Essential Addons

With Essential Addons, we bring you seamless compatibility with WooCommerce, enhancing your ability to showcase products, categories, and filters with ease. There are 11+ beautiful **WooCommerce widgets** for your every need:

[EA Woo Product Carousel](https://essential-addons.com/docs/woo-product-carousel/): Display your store’s products interactively with this WooCommerce widget and implement various styling options and ready-made layouts to create an interactive product showcase on your WordPress website.

[EA Woo Product Gallery](https://essential-addons.com/docs/woo-product-gallery/): Captivate visitors and drive immediate purchases on your WooCommerce store using the Product Gallery element from Essential Addons. Explore incredible ready layouts, abundant customization choices, and more.

[EA Woo Product Price](https://essential-addons.com/docs/ea-woo-product-price/): Highlight the price of your WooCommerce product Price and style it using custom colors and typography.

[EA Woo Product Rating](https://essential-addons.com/docs/ea-woo-product-rating/): Display WooCommerce product rating to help customers make informed purchasing decisions and increase credibility.

[EA Woo Product Images](https://essential-addons.com/docs/ea-woo-product-images/): Showcase eye-catching WooCommerce product images with advanced customization and effects.

[EA Woo Add to Cart](https://essential-addons.com/docs/ea-woo-add-to-cart/): Simplify the shopping experience by displaying an easy-to-access ‘Add to Cart’ button for all your WooCommerce products.

[EA Woo Product Slider (PRO)](https://essential-addons.com/docs/woo-product-slider/): Present your WooCommerce products in a stunning slider format with this advanced Elementor addon. You can also easily customize the entire slider appearance with fantastic pre-designed layouts, effects, and other advanced options.

[EA Woo Product Collection (PRO)](https://essential-addons.com/docs/ea-woo-product-collections/): Exhibit your WooCommerce product collections on any section of your Elementor website to showcase items based on categories, tags, or attributes, and add engaging hover effects.

https://youtu.be/4jKW2e2QY-g

[EA Woo Product List](https://essential-addons.com/docs/ea-woo-product-list/): Easily add all your WooCommerce products in a listicle format on any Elementor page or post on your website and customize with unique layouts to attract customers instantly.


[EA Woo Product Grid](https://essential-addons.com/docs/woocommerce-product-grid/): Display your products in visually appealing grid-layouts on any website section and allow customers to view them based on category, tags, or attributes.

[EA Woo Product Compare](https://essential-addons.com/docs/woo-product-compare/): Easily compare your preferred WooCommerce products anywhere on the page with this free Elementor addon. Benefit from numerous pre-designed theme layouts and much more.


[EA Woo Cross Sells:](https://essential-addons.com/docs/ea-woo-cross-sells/): Effortlessly show related items to your customers with this premium Elementor widget, aiding them in discovering complementary products and enriching their overall shopping experience.

https://youtu.be/EQu8e1MwuKI


[EA Woo Cart](https://essential-addons.com/docs/woocommerce-cart/): Elevate customers’ purchasing experience on your WooCommerce store by effortlessly designing an appealing Cart Page with Essential Addons using no coding.

[EA Woo Checkout](https://essential-addons.com/docs/woo-checkout/): Craft visually appealing Checkout pages for your WooCommerce store to customize and style the widget effortlessly to ensure increased sales.

[EA Woo Account Dashboard (PRO)](https://essential-addons.com/docs/ea-woo-account-dashboard/): Create a user-friendly and visually appealing dashboard for your WooCommerce store, consolidating all vital tabs into one convenient location.

https://youtu.be/CEUqgOS4CN0

[EA Woo Thank You (PRO)](https://essential-addons.com/docs/ea-woo-thank-you/):Resonate with customers and ensure a positive post-purchase experience with this advanced Elementor WooCommerce widget from Essential Addons. It lets you create tailor-made thank-you messages with limitless personalization.


### 🏆  FEATURED BY 100+ RENOWNED PUBLICATIONS

**WP Mayor:** “If you use Elementor, Essential Addons is a great way to expand your library of available widgets without slowing your site down.”
**WP Pagebuilders:** “With 60+ additional widgets, Essential Addons is a great add-on to enhance your experience in building a website with WordPress+Elementor.”
**MonsterPost:** “Essential Addons for Elementor has the largest number of active users among all third-party extensions on the market. The widgets are tested in all popular web browsers to ensure full browser compatibility for all elements.”
**WPCrafter:** WordPress influencer Adam Preiser did a Speed Test & found Essential Addons for Elementor is the fastest solution out there 👇

https://youtu.be/K9BssSV-KC8

### BACKED BY A TRUSTED TEAM
Essential Addons is brought to you by [WPDeveloper](https://wpdeveloper.com/), a dedicated WordPress product company, trusted by 6 million+ happy users.

### Documentation and Support

- For documentation and tutorials go to our [Documentation](https://essential-addons.com/docs/).
- If you have any more questions, visit our support on the [Plugin's Forum](https://wordpress.org/support/plugin/essential-addons-for-elementor-lite).
- For more information about features, FAQs and documentation, check out our website at [Essential Addons](https://essential-addons.com/).

### Happy User of Essential Addons?

- Join our [Facebook Group](https://www.facebook.com/groups/essentialaddons/).
- Learn from our tutorials on [YouTube Channel](https://wpdeveloper.com/go/youtube-channel).
- Or rate us on [WordPress](https://wordpress.org/support/plugin/essential-addons-for-elementor-lite/reviews/?rate=5#new-post)

### 🔥 WHAT’S NEXT
If you like Essential Addons, then consider checking out our other WordPress Plugins:


🔔 **[NotificationX](https://wordpress.org/plugins/notificationx/)** – Best Social Proof & FOMO Marketing Solution to boost conversions & design stunning WordPress notification bars in Elementor

🗒️ **[BetterDocs](https://wordpress.org/plugins/betterdocs/)** – Best Documentation & Knowledge Base Plugin for WordPress, which also comes with ready doc templates for Elementor

⏰ **[SchedulePress](https://wordpress.org/plugins/wp-scheduled-posts/)**  – Complete solution for managing WordPress content scheduling through an editorial calendar & Social Share

🔗 **[EmbedPress](https://wordpress.org/plugins/embedpress/)** - Easiest WordPress solution for embedding videos, images, posts, audio, maps and PDF, DOC, PPT & all other types of content into your website using Elementor, Gutenberg and more.

🔎 **[easy.jobs](https://wordpress.org/plugins/easyjobs/)** -  Smart and easy recruitment and talent sourcing solution for hiring with AI-powered screening system, question sets, remote interviews, and designing branded career pages with Elementor.

== Installation ==

Note : This plugin works with Elementor. Make sure you have [Elementor](https://wordpress.org/plugins/elementor/) installed.


1. Upload the plugin folder to the `/wp-content/plugins/` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the 'Plugins' screen in WordPress
3. You can type "EA" on your element tabs within Elementor editor and all the available elements will appear.
4. Also you can see them under the category "Essential Addons for Elementor" on your element/widget list.

== Frequently Asked Questions ==

= Can I use the plugin without Elementor Page Builder? =

No. You cannot use without Elementor since it's an addon for Elementor.

= Does it work with any theme? =

Absolutely! It will work with any theme where Elementor works.

= What if I update to Premium version? =

Your existing elements/content will work with premium version. So you won't lose your developed contents.

== Screenshots ==

1. Overview of few widgets
2. Elements control panel
3. Introducing EA 5.0
4. Advanced Google Map Widget
5. Advanced Tooltip Extension
6. Advanced Tabs Widget
7. Fancy Text Widget
8. Filterable Gallery Widget
9. Feature List Widget
10. Image Hotspots Widget
11. Woo Product Grid Widget
12. Team Member Carousel Widget
13. Smart Post List Widget
14. One Page Navigation Widget
15. Protected Content Widget
16. Flip Box Widget
17. Creative Buttons Widget


== Changelog ==

= 6.2.3 - 13/08/2025 =

- Fixed: EA Simple Menu | Dropdown not working on iPhone
- Fixed: EA Filterable Gallery | Filters not updating in editor
- Fixed: Homepage not loading after updating from v6.1.8
- Fixed: Post Widgets | "Load More" button stays after full load
- Fixed: EA Woo Checkout | Shipping selection issue
- Fixed: EA Add to Cart | Spacing issue with variable products
- Fixed: Migration error | File integrity check added
- Fixed: EA Gravity Forms | List/File upload field color issue
- Fixed: EA SVG Draw | Not triggering on scroll
- Improved: EA Login/Register | Cloudflare Turnstile support
- Improved: EA Tooltip | WPML Media Translation support
- Improved: EA Flip Box | Auto height for all devices
- Improved: EA Woo Product Image | Custom hook for thumbnail
- Improved: EA Woo Product Image | Enhanced zoom effect
- Improved: EA Breadcrumbs | Home icon now clickable
- Improved: Performance | Reduced unnecessary queries
- Security Enhancement
- Few minor bug fixes & improvements

= 6.2.2 - 28/07/2025 =

- Few minor bug fixes & improvements

= 6.2.1 - 20/07/2025 =

- Improved: EA Call To Action | Added Multi-Color Title support
- Improved: EA Login/Register Form | Added reCAPTCHA support in Password Reset Form
- Fixed: EA Woo Product Carousel | Resolved add to cart button Duplication issue with Blocksy theme
- Fixed: EA Woo Product Carousel | Resolved Badge color styling issue
- Few minor bug fixes & improvements

= 6.2.0 - 03/07/2025 =

- Added: New Widget | EA Code Snippet
- Improved: Security Enhancements
- Improved: EA Event Calendar | Added label to search field for accessibility
- Fixed: EA Woo Product Gallery | Resolved cropping and misalignment issues with custom image sizes
- Few minor bug fixes & improvements


= 6.1.20 - 26/06/2025 =

- Improved: Security Enhancements
- Fixed: EA Woo Product Gallery | Compatibility issue with WPML
- Fixed: EA Filterable Gallery | Resolved layout issues
- Fixed: EA Woo Product Carousel | Addressed accessibility issues
- Fixed: EA Product Grid | Pricing now displays correctly in list layout
- Fixed: Resolved compatibility issue with GiveWP
- Few minor bug fixes & improvements

= 6.1.19 - 20/06/2025 =

- Few minor bug fixes & improvements

= 6.1.18 - 04/06/2025 =

- Fixed: EA Woo Product Gallery | category tab not working
- Few minor bug fixes & improvements

= 6.1.17 - 02/06/2025 =

- Added: EA SVG Draw | New Fill Type (Always) Controller
- Added: EA Filterable Gallery | Added Captions for Videos
- Fixed: EA Advanced Data Table | Sorting Issue for non-English Languages
- Fixed: EA Advanced Data Table | Sorting Icon Shows Console Error
- Fixed: EA Woo Product Gallery |  Quick View Popup Styling Issue
- Fixed: EA Woo Product Gallery |  "View Cart" Button Line Height Is Incorrect
- Fixed: EA Simple Menu |  Undefined Array Key "eael_simple_menu_menu" Warning
- Fixed: EA Woo Product Grid | "View More" Button Visible After Full Load in Elementor Tabs
- Fixed: EA Woo Product Grid | Issues with Missing Products
- Fixed: EA Woo Product Image | Image Scaling Issue
- Fixed: EA Login/Register Form | Validation Message Not Showing
- Fixed: EA Login/Register Form | Issue Submitting Forms With File Uploads
- Fixed: EA SVG Draw  | Fill After Draw Issue
- Fixed: EA Post Grid  | Excluding Pages Issue
- Fixed: Compatibility Issue with Newer PHP Versions.
- Improved: EA Fancy Text | Animation Delay Until Section Scrolls Into View.
- Improved: EA Filterable Gallery | Revamped Controls & Layouts
- Improved : Removed Unused Class “use” Declaration
- Improved: EA Post Grid, Team Member, Testimonial, Woo Product Grid, Woo Product Gallery | Controls Reorganized in Elementor Panel
- Few minor bug fixes & improvements

= 6.1.15 - 25/05/2025 =

- Improved: EA Filterable Gallery | Revamped Controls & Layouts
- Improved: EA Dual Color Heading | Added Multiple Heading Option
- Fixed: EA Login/Register Form | Google Social Login assigning "Subscriber" instead of "Editor" role after Signup
- Few minor bug fixes & improvements

= 6.1.14 - 21/05/2025 =

- Few minor bug fixes & improvements

= 6.1.13 - 19/05/2025 =

- Improved: Security Enhancement
- Few minor bug fixes & improvements

= 6.1.12 - 13/05/2025 =

- Fixed: 404 Page not found page not working when built with Elementor template
- Fixed: EA Flip Box | Height Inconsistency and Scroll Not Working on iPhone
- Fixed: EA Woo Product List | Popup style not working
- Improved: EA Testimonial | Added Image Height Control
- Improved: EA Testimonial | Removed default 10px padding
- Few minor bug fixes & improvements

= 6.1.11 - 22/04/2025 =

- Improved: EA Info Box | Issues & Enhancements – Phase 2
- Fixed: EA Quick Setup Wizard | Basic and Advanced settings not working
- Fixed: EA Event Calendar | Language translation issue
- Few minor bug fixes & improvements

= 6.1.10 - 10/04/2025 =

- Improved: Security Enhancement
- Few minor bug fixes & improvements

= 6.1.9 - 24/03/2025 =

- Improved: EA Testimonial | Controller Mechanism
- Improved: EA Creative Button | Controller Mechanism
- Improved: EA Call To Action | Controller Mechanism
- Fixed: Elementor editor Overlapping issue
- Fixed: EA Filterable Gallery | Load more Button functionality issue
- Fixed: EA Filterable Gallery | Lightbox Slide number discrepancy issue
- Few minor bug fixes & improvements

= 6.1.8 - 21/03/2025 =

- Few minor bug fixes & improvements

= 6.1.7 - 20/03/2025 =

- Fixed: Compatibility issue causing a critical error with Elementor Pro versions below 3.24.0
- Few minor bug fixes & improvements

= 6.1.6 - 19/03/2025 =

- Improved: Security Enhancement
- Improved: EA Facebook Feed | Load More button now functions correctly
- Improved: EA Info Box | Added Subtitle Option
- Improved: EA Sticky Video | Video now plays when clicked
- Improved: EA Woo Account Dashboard | Added additional controls inside tabs
- Fixed: Added support for Elementor 3.28.0 compatibility
- Fixed: EA SVG Draw | "Fill after draw" option now works correctly
- Fixed: EA Woo Product Images | Navigation arrows are now properly aligned
- Fixed: EA Filterable Gallery | Resolved masonry layout error
- Fixed: Resolved conflict with Elementor’s 404 page template
- Fixed: Woo Product Gallery | Now displays more than 4 products correctly
- Fixed: EA Login Register Form | Custom phone number field now appears in both user and admin emails
- Fixed: EA Team Member | Using an empty featured image from dynamic tags no longer causes a fatal error
- Fixed: EA Pricing Table | Icon color issue resolved
- Few minor bug fixes & improvements

= 6.1.5 - 13/03/2025 =

- Improved: Introduced Post Types selection mechanism for Widgets
- Improved: EA Product Grid | Added Stock Out products exclusion option
- Improved: EA Flip Box | Added option to control flip duration
- Fixed: EA Advanced Data Table | Issues with non English Special Characters
- Fixed: EA Woo Add to Cart | Multiple buttons double icon issue
- Few minor bug fixes & improvements

= 6.1.4 - 17/02/2025 =

- Few minor bug fixes & improvements

= 6.1.3 - 17/02/2025 =

- Fixed: EA Creative Icon | SVG icon color not applying from settings
- Fixed: EA Advanced Data Table | Large CSV file import causes unresponsive page during widget edit
- Fixed: EA Filterable Gallery | Popup redirects to YouTube instead of opening modal
- Fixed: EA Woo Product Images | Images do not change with product variations
- Few minor bug fixes & improvements

= 6.1.2 - 10/02/2025 =

- Fixed: EA Woo Product Carousel | Regular price strikethrough color is not changing
- Fixed: EA Woo Product Carousel | Not able to style the pop-up with the woo product carousel
- Fixed: EA Woo Product Grid | Not able to style the pop-up with the woo product grid
- Fixed: EA Woo Product Gallery | Product Gallery Display Issue on Chrome (Repeating Rows After Navigation)
- Fixed: EA Fancy text | - & character can not be used
- Fixed: EA Advanced Tabs | Editor not loading when Advanced Tabs element is enabled in some cases
- Fixed: EA Fancy Text | Animation Not Working Except for "Typing Text"
- Improved: EA Woo Product Images | Added responsive controls
- Few minor bug fixes & improvements

= 6.1.1 - 21/01/2025 =

- Improved: Added Edit Template support right inside the page itself for the widgets which got Saved Template option
- Fixed: EA Breadcrumbs | When used on the Container, the background image of the container doesn't show
- Fixed: Compatibility issue | WooCommerce Product Bundles | With bundle product quick view add to cart not working
- Fixed: EA Filterable Gallery | Accessibility issue with Filters
- Fixed: EA Filterable Gallery | Several fields from Filterable Gallery cannot be translated with WPML.
- Fixed: Advanced Data Table | open new tab does not work in link
- Fixed: EA Advanced Accordion | FAQ Schema Issue with Item Count
- Fixed: EA WPForms | Error text color is not being applied
- Fixed: EA Filterable Gallery | Load more is not showing properly in some cases
- Fixed: EA Event Calendar | Events overflowing the container if multiple events available on a day
- Improved: EA Team Member | Change Team Member name tag from H2 to span
- Fixed: EA Post timeline | Title font family can't be changed
- Fixed: EA Gravity Forms | "Next" Button Issue with Ajax and Toggle Settings
- Few minor bug fixes & improvements

= 6.1.0 - 29/12/2024 =

- Added: EA Woo Product Price
- Added: EA Woo Product Rating
- Added: EA Woo Product Images
- Added: EA Woo Add to Cart
- Added: EA Breadcrumbs
- Fixed: Uncaught Error | Class XD_Copy not found
- Fixed: EA WPForms | Calendar selection not working in Elementor Popup
- Fixed: EA Woo Product Gallery | "Quick View" button custom URL issue
- Few minor bug fixes & improvements


= 6.0.15 - 24/12/2024 =

- Improved: Security Enhancement
- Improved: Added Optimized Markup Support
- Few minor bug fixes & improvements

= 6.0.14 - 19/12/2024 =

- Few minor bug fixes & improvements

= 6.0.13 - 15/12/2024 =

- Fixed: EA Pricing Table | Throwing critical error when Pro version isn't up-to-date
- Few minor bug fixes & improvements

= 6.0.12 - 11/12/2024 =

- Added: Elementor 3.26 Compatibility
- Fixed: EA Woo Cart | Quantity icons are showing multiple times on the cart page
- Fixed: EA Event Calendar | Multiple Issues with Date Formatting, Time Input, and Language Options
- Fixed: EA Scroll To Top | Not working on Search Result page
- Fixed: PHP Deprecated Warnings during Installation
- Fixed: EA Pricing Table | Trying to access array offset on null
- Fixed: Mismatched Text Domains
- Few minor bug fixes & improvements

= 6.0.11 - 01/12/2024 =

- Fixed: Compatibility issue with FunnelKit
- Fixed: EA Woo Product Carousel | On load carousel first item visibility issue
- Fixed: PHP Notice: Function "_load_textdomain_just_in_time" error in WordPress 6.7
- Fixed: EA Post Grid | Added Nickname support for Author
- Fixed: EA Info Box | Image styling Control issue
- Fixed: EA Sticky Video | Multiple Sticky Video in Same Page UI issue
- Fixed: EA Feature List | PHP undefined array notice : "eael_feature_list_icon_individual_box_bg_color"
- Fixed: EA Woo Product Grid | Product Rating Types issue on Default Preset
- Improved: EA Woo Product Gallery | Added relation between Category and Tags on Query
- Few minor bug fixes & improvements

= 6.0.10 - 14/11/2024 =

- Fixed: EA Advanced Accordion | When click on the accordion it jumps to another section
- Fixed: Woo Product Grid | Pagination Typography Font Weight is not being applied properly
- Fixed: Woo Product Grid | Product pricing isn’t displaying after the recent update, despite "Show Price" being enabled
- Fixed: WPForms Widget | Form Container Alignment Does Not Work properly
- Fixed: EA Dual Color Header | The Gradient color doesn't work
- Fixed: EA Woo Product Grid | Price is not showing on list layout
- Fixed: EA Sticky Video | Sticky Video Misalignment Issue while scorlling
- Fixed: EA Login/Register Form | {password_reset_link} Shortcode Issue in Registration User Email Template
- Improved: EA Info Box | Width Controller for Image
- Improved: EA Woo Product Carrousel | Added rating count next to stars
- Improved: Select2 Ajax Control : Taxonomy - need to remove limit
- Improved: Security Enhancement
- Few minor bug fixes & improvements

= 6.0.9 - 06/11/2024 =

- Improved: Added Edit Template support right inside the page itself
- Improved: EA Post Grid | Added support to show custom post's field/taxonomy
- Improved: EA Team Member | Added a dynamic tag option for showing ACF images in Team Member image
- Improved: EA Filterable Gallery | Added support for YouTube Shorts
- Fixed: EA Data Table | Using text area Content is visibility issue under < a > tag
- Fixed: EA Gravity Forms | Layout UI issue on the editor page
- Fixed: EA Interactive Circle | Conflicting with Whiskers Theme Pro
- Fixed: EA Post Grid | Author name issue in Style 1 & 2
- Fixed: EA Scroll To Top | Added support on Archive page
- Fixed: EA Woo Checkout | PHP Deprecated issue
- Fixed: EA Product Grid | Image clickable issue
- Fixed: Conflict with Formstack plugin
- Few minor bug fixes & improvements

= 6.0.8 - 27/10/2024 =

- Fixed: EA Pricing Table | Font weight changes while tooltip is used
- Fixed: EA Flip Box | Flip box click Not working on frontend when using more than one flip boxes with saved templates
- Fixed: EA Filterable Gallery | Not working while being used as shortcode from site review plugin
- Fixed: EA Scroll To Top | Global feature isn't working after editing the page
- Fixed: EA Table of Contents | Undefined array key
- Fixed: EA Pricing Table | Dynamic tag for button is not working
- Fixed: Saved Template design breaks due to an issue with element caching
- Fixed: EA Woo Product Gallery | Show extra cart button on enabling woocommerce loop hooks
- Few minor bug fixes & improvements

= 6.0.7 - 09/10/2024 =

- Fixed: EA Advanced Tabs | While being hovered or active, the tab shows green border
- Fixed: EA Woo Cart | Style 2 - Remove product button size increase or decrease is not working for mobile
- Fixed: EA Contact form 7 | Form width doesn't change
- Fixed: EA Woo Product Grid | Search results isn't showing while being used as Search result template
- Fixed: EA Advanced Data Table | Sorting is not working
- Fixed: EA Advanced Tabs | JS compatibility issue
- Fixed: EA Advanced Data Table | Images are not showing
- Fixed: EA Advanced Tabs | Feature List as a saved template is broken
- Few minor bug fixes & improvements


= 6.0.6 - 29/09/2024 =

- Improved: EA Filterable Gallery | Pagination on video gallery
- Improved: EA Woo Product Carousel | Added Marquee support
- Improved: EA Wrapper Link | Add enable and disable option for traditional link
- Improved: EA Woo Product Carousel | Add option to select items count on slide
- Improved: EA Table of Contents | Position & Color
- Improved: Security enhancements
- Improved: EA Woo Checkout | Need an option to change Input field background color
- Improved: EA Interactive Circle | Interactive Circle content icon not displaying.
- Improved: EA Post Grid | Sticky posts visible at first
- Fixed: EA Flip Box | Purple Color Overlay Issue
- Fixed: EA Filterable Gallery | Field link not translatable with WPML
- Fixed: EA Login/Register Form | Absence of message validation
- Fixed: EA Advanced Tabs | Accessibility Issue, Screen Reader Focus Limited to First Tab
- Few minor bug fixes & improvements

= 6.0.5 - 18/09/2024 =

- Improved: EA Flip Box | UI for mobile devices
- Improved: EA Filterable Gallery | Added Overlay for the video gallery
- Improved: EA Woo Product Gallery | Added Archive Product Support for Product Category Page
- Improved: EA Woo Product Gallery | Added support to Turn off secondary image on hover for mobile
- Fixed: Animation conflicting issue with Elementor
- Fixed: EA Advanced Accordion | Conflict with Elementor 3.24.0
- Fixed: EA Woo Product List | HTML Tags Support for Product Excerpts
- Fixed: EA Woo Product Carousel | Added compatibility for Astra theme
- Fixed: EA Checkout | Conflict with USPS Shipping Method for WooCommerce
- Fixed: EA Countdown | Expiration issue with Saved Templates
- Fixed: EA Login Register Form | Warning message on reset password
- Fixed: EA Interactive Circle | UI issue when Scrolling
- Few minor bug fixes & improvements

= 6.0.4 - 09/09/2024 =

- Improved: Security enchancements in EA Fancy Chart & EA Filterably Gallery widgets
- Improved: EA Woo Product widgets | Added manual product selection option
- Added: Compatibility with Element Caching
- Fixed: Scroll position changing on click Elementor Tab when EA is activated
- Fixed: EA Interactive Circle | Link malfunction in interactive items
- Fixed: EA Filterable Gallery | Randomized Gallery option not randomizing the gallery items due to caching issue
- Few minor bug fixes & improvements

= 6.0.3 - 04/09/2024 =

- Fixed: EA Post Grid | Author related UI issues
- Fixed: EA Login Register Form | T&C Toggle Colour issue
- Fixed: EA Interactive Circle | Custom SVG icon's Colour issue
- Fixed: EA Wrapper Link conflicts with Elementor Loop Grid
- Fixed: EA Advanced Accordion empty data issue with ACF field
- Fixed: EA Advanced Tabs | Custom ID offset issue
- Fixed: EA Post Grid | Fallback image display issue
- Fixed: EA Post Duplicator | Page Layout styling issue
- Fixed: EA Advanced Tabs compatibility issue with Ad inserter plugin
- Fixed: EA Advanced Data Table | Alphanumeric Sorting issue
- Fixed: EA WPForms | Submit button hover colour issue
- Fixed: Uncaught TypeError: When accessing a string offset on a string variable
- Fixed: EA Woo Product Carousel | Mobile landscape visibility issue
- Fixed: EA Woo Product Grid | Quick view compatibility issue with YITH WooCommerce product filter
- Few minor bug fixes & improvements

= 6.0.2 - 29/08/2024 =

- Fixed: Enabling elements inside Elementor causing blank EA dashboard
- Fixed: EA Filterable Gallery | "Load More" button shows on those filters which doesn't have any items and less items
- Fixed: EA Interactive Circle | Add URL support for interactive items
- Fixed: EA Hover Interactions | Hover Interactions does not work on the front-end
- Fixed: EA Dashboard | Update CSS Print Method link
- Fixed: EA Post Grid | After Load More the terms are not displayed with Style 3
- Fixed: EA Flip Box | Add overlay for the background image
- Fixed: EA Woo Product Carousel | Carousel disappears when using Botiga theme
- Fixed: EA Post Grid | Child sections ignores 'Show Meta' parents
- Fixed: EA Scroll To Top | Scroll To Top icon doesn't show
- Fixed: EA WPForms | Submit button hover color stays grey
- Fixed: EA Filterable Gallery | Updated label to "Notice" on display content notice
- Fixed: EA NFT Gallery | Not working
- Improved: EA Dual Color Heading | Vulnerable to Cross Site Scripting (XSS)
- Improved: EA Woo Checkout | EA Woo Checkout is missing some styling options
- Improved: EA Wrapper link | Elementor popup doesn't work on wrapper link
- Improved: EA Table of Contents | Width option for mobile device
- Improved: Optimized Control Loading | EA Feature List, EA Sticky Video, EA Pricing Table, EA Progress Bar, EA Woo Product Compare
- Few minor bug fixes & improvements

= 6.0.1 - 19/08/2024 =

- Fixed: Elementor transform example rotate, skew etc features not working inside Elementor Editor
- Fixed: Custom Template shows broken inside Elementor Editor if EA is activated in some cases
- Fixed: Dashboard UI/UX related issues after EA 6.0 update
- Improved: Added async requests to all the actions inside EA Dashboard
- Few minor bug fixes & improvements

= 6.0.0 - 11/08/2024 =

- Revamped: New EA Dashboard for better UI/UX
- Revamped: New EA Quick Setup for better UI/UX
- Added: New Extension- EA Hover Interactions
- Added: New Skin Presets for different Post, WooCommerce & Other widgets
- Improved: Added proper sanitization inside EA Filterable Gallery widget
- Fixed: EA Pricing Table | Button Not Displaying and Getting Undefined index
- Few minor bug fixes & improvements

= 5.9.27 - 11/07/2024 =

- Updated: Custom Attributes inside EA Event Calendar
- Few minor bug fixes & improvements


= 5.9.26 - 11/07/2024 =

- Improved: Security Enhancement
- Fixed: EA Login / Register Form | Password form shows error when hit on "Forgot Password"
- Fixed: Cannot redeclare control with same name "eael_global_warning_text"
- Few minor bug fixes & improvements


= 5.9.25 - 02/07/2024 =

- Fixed: EA Simple Menu | Active menu not showing for archive pages
- Fixed: Missing translatable strings for EA Woo Product List & EA NFT Gallery
- Fixed: EA Call to action | Margin not being applied uniformly
- Updated: WPML Config file in some widgets
- Improved: EA Event Calendar for Security Enhancement
- Improved: EA Post Grid | Added option to show user's first & last name instead of just username
- Improved: EA Advanced Data Table | CSV Data import/export mechanism
- Few minor bug fixes & improvements


= 5.9.24 - 09/06/2024 =

- Improved: User role permissions to add/edit custom JS
- Few minor bug fixes & improvements

= 5.9.23 - 05/06/2024 =

- Improved: Security Enhancement
- Improved: EA Login/Register Form | Added Score Threshold option for reCAPTCHA & option to remove the branding
- Improved: EA Woo product Grid | Added option to filter by tags
- Improved: EA Login/Register Form | Added support for register_form action hook
- Improved: EA Login/Register Form | Added field for honeypot
- Improved: EA Interactive Circle | Added option to rotate the Interactive Circle around the middle and pause on hover
- Improved: EA Feature List | Added Horizontal layout option
- Improved: EA Filterable Gallery | Added support for vertical 9:16 video
- Fixed: EA Simple Menu | Using a hash link (#) on a sub-menu item within a hamburger menu will not open the dropdown item on mobile/tablet view
- Fixed: EA Woo Product Grid | Not working properly with WordPress and WooCommerce Filter
- Fixed: EA Filterable Gallery | Image Tag being changed from <a> tag to <img> tag after Version 5.9.15
- Fixed: EA Advanced Data Table | Header text reverting to Black if the Sort option in the advanced Features is turned off
- Fixed: EA Post Grid | Conflict with Ultimate Member plugin
- Fixed: EA Post Grid | On Style 3 Terms on Hover is not showing after clicking on the Load More button
- Fixed: EA WP Forms | EA WP Forms Widget Width Restriction in Latest Update
- Fixed: EA Woo Product Gallery | Error in code — data-template and data-terms attributes
- Fixed: EA Woo Product Grid | "Out of stock" badge showing on top of "Sale" badge
- Fixed: EA Login/Register Form | Spinner is not visible on the front-end
- Few minor bug fixes & improvements

= 5.9.22 - 22/05/2024 =

- Improved: Security Enhancement
- Few minor bug fixes & improvements

= 5.9.21 - 13/05/2024 =

- Revamped: NFT Gallery | OpenSea API version
- Fixed: Filterable Gallery | iframe not working issue
- Improved: Security Enhancement
- Few minor bug fixes & improvements

= 5.9.20 - 08/05/2024 =

- Improved: Security Enhancement
- Few minor bug fixes & improvements

= 5.9.19 - 05/05/2024 =

- Fixed: EA Sticky Video | Conflicts with the Blocksy theme
- Fixed: EA Login Register Form | Validation message doesn't show up while using the form in the popup
- Fixed: EA Login Register Form | Showing error message for not required field
- Fixed: EA Simple Menu | Dropdown icon is not showing on the desktop mode
- Fixed: EA Tooltip | Displaying the content after the hover is complete
- Fixed: EA Feature list | Icons don't show when the shape view is set to Framed
- Fixed: EA Contact Form 7 | Alignment of the container, title & description is not working
- Fixed: EA Advanced Accordion | Conflicts with the Specia Standard theme
- Fixed: EA Advanced Data Table | Checkbox is not showing when using TablePress
- Fixed: EA Advanced Tabs| Switching tabs causes the page to scroll up or down
- Fixed: EA Woo Checkout | 'User Account' & 'Coupon Percentage' icons are broken on checkout page
- Fixed: EA Advanced Data Table | Data sorting icons' colors don't change while using table as a template
- Fixed: EA Simple Menu | Menu item doesn't activate when scrolling the page
- Fixed: EA Advanced Data Table | Clearing the search values, all table contents appear when the rows per page is left blank
- Fixed: EA Woo Product Carousel | 'Add to Cart' button disappears from default shop page if EA Woo Carousel widget is present on the same page
- Improved: EA Event Calendar | Date format change option for week view
- Improved: EA Advanced Accordion | Stop auto-scrolling while anchoring tab
- Improved: EA Login Register Form | File max size description
- Improved: EA Facebook Feed | Graph API Upgrade
- Few minor bug fixes & improvements

[See changelog for all versions](https://essential-addons.com/changelog).


== Upgrade Notice ==
