!function(e){var o={};function n(r){if(o[r])return o[r].exports;var a=o[r]={i:r,l:!1,exports:{}};return e[r].call(a.exports,a,a.exports,n),a.l=!0,a.exports}n.m=e,n.c=o,n.d=function(e,o,r){n.o(e,o)||Object.defineProperty(e,o,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,o){if(1&o&&(e=n(e)),8&o)return e;if(4&o&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&o&&"string"!=typeof e)for(var a in e)n.d(r,a,function(o){return e[o]}.bind(null,a));return r},n.n=function(e){var o=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(o,"a",o),o},n.o=function(e,o){return Object.prototype.hasOwnProperty.call(e,o)},n.p="",n(n.s=17)}({17:function(e,o){var n=function(e,o){var n=e.find(".eael-img-accordion").eq(0);void 0!==n.data("img-accordion-id")&&n.data("img-accordion-id");function r(n,r){!1===r.hasClass("overlay-active")&&n.preventDefault();var a=o(".eael-image-accordion-hover",e);a.removeClass("overlay-active"),a.css("flex","1"),r.find(".overlay").parent(".eael-image-accordion-hover").addClass("overlay-active"),a.find(".overlay-inner").removeClass("overlay-inner-show"),r.find(".overlay-inner").addClass("overlay-inner-show"),r.css("flex","3")}"on-click"===(void 0!==n.data("img-accordion-type")?n.data("img-accordion-type"):"")?o(".eael-image-accordion-hover",e).on("click",(function(e){r(e,o(this))})):(o(".eael-image-accordion-hover",e).hover((function(e){r(e,o(this))})),o(".eael-image-accordion-hover",e).mouseleave((function(n){console.log("leave"),function(n,r){!1===r.hasClass("overlay-active")&&n.preventDefault();var a=o(".eael-image-accordion-hover",e);a.removeClass("overlay-active"),a.css("flex","1"),a.find(".overlay-inner").removeClass("overlay-inner-show")}(n,o(this))})))};eael.hooks.addAction("init","ea",(function(){elementorFrontend.hooks.addAction("frontend/element_ready/eael-image-accordion.default",n)}))}});