!function(e){var t={};function i(r){if(t[r])return t[r].exports;var n=t[r]={i:r,l:!1,exports:{}};return e[r].call(n.exports,n,n.exports,i),n.l=!0,n.exports}i.m=e,i.c=t,i.d=function(e,t,r){i.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},i.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},i.t=function(e,t){if(1&t&&(e=i(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)i.d(r,n,function(t){return e[t]}.bind(null,n));return r},i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,"a",t),t},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},i.p="",i(i.s=39)}({39:function(e,t){function i(e){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function r(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,r)}return i}function n(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?r(Object(i),!0).forEach((function(t){o(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):r(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function o(e,t,r){return(t=function(e){var t=function(e,t){if("object"!=i(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==i(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var a=function(e,t){var i=t(".eael-single-product-images"),r=t(".product_image_slider__thumbs",e),o=r.data("pi_thumb"),a=t(".swiper-wrapper .swiper-slide:first-child .image_slider__image > img",i),s=t(".swiper-wrapper .swiper-slide:first-child .product_image_slider__thumbs__image > img",i),l=c(a),u=c(s);function c(e){return{src:e.attr("src"),srcset:e.attr("srcset"),sizes:e.attr("sizes")}}function d(i){t(".swiper-container",e).each((function(e,t){t.swiper.autoplay[i](),t.swiper.slideTo(0)}))}function m(e,t){e.fadeOut(100,(function(){e.attr("src",t.src).attr("srcset",t.srcset).attr("sizes",t.sizes).removeAttr("data-src").removeAttr("data-large_image"),e.fadeIn(100,(function(){e.hasClass("image_slider__image")&&setTimeout((function(){initializeZoomLens(e)}),50)}))}))}t(".variations_form").on("show_variation",(function(e,t){var i;null!=t&&null!==(i=t.image)&&void 0!==i&&i.src&&(r=t.image,n=r,a.attr("src",n.src).attr("srcset",n.srcset).attr("sizes",n.sizes).attr("data-src",n.src).attr("data-large_image",n.full_src),function(e,t){e.attr("src",t.gallery_thumbnail_src).attr("srcset",t.gallery_thumbnail_src).attr("sizes",t.gallery_thumbnail_src_h)}(s,r),setTimeout((function(){initializeZoomLens(a)}),100),d("stop"));var r,n})),t(".variations_form").on("hide_variation reset_image",(function(){m(a,l),m(s,u),void 0!==o.autoplay&&d("start")}));var f=function(e,t){return"undefined"==typeof Swiper||"function"==typeof Swiper?new(0,elementorFrontend.utils.swiper)(e,t).then((function(e){return e})):_(e,t)},_=function(e,t){return new Promise((function(i,r){i(new Swiper(e,t))}))},p=e.data("id"),g="#slider-container-".concat(p," .product_image_slider__thumbs .swiper-container"),b="#slider-container-".concat(p," .product_image_slider__container .swiper-container"),h=r.data("for_mobile"),y=t(".product_image_slider__container",e).data("pi_image");t(window).on("load",(function(){if(window.matchMedia("(max-width: 767px)").matches){var i=t(".image_slider__image",e).height(),r=o.slidesPerView*h,n=Math.min(r,i);t(".eael-pi-thumb-left .product_image_slider .product_image_slider__thumbs, .eael-pi-thumb-right .product_image_slider .product_image_slider__thumbs",e).css("height",n),e.find(".eael-pi-thumb-bottom .product_image_slider .product_image_slider__thumbs").css("width",n)}else{var a=t(".image_slider__image",e).height(),s=100*o.slidesPerView,l=Math.min(s,a);t(".eael-pi-thumb-left .product_image_slider .product_image_slider__thumbs, .eael-pi-thumb-right .product_image_slider .product_image_slider__thumbs",e).css("height",l)}})),f(t(g),o).then((function(e){var i=n(n({},y),"yes"===o.thumbnail&&{thumbs:{swiper:e}});f(t(b),i).then((function(e){e&&e.on("slideChange",(function(){setTimeout((function(){var i=t(e.slides[e.activeIndex]).find(".image_slider__image img");i.length&&initializeZoomLens(i)}),100)}))})).catch((function(e){console.log("Error initializing main Swiper:",e)}))})).catch((function(e){console.log("Error initializing Swiper thumbs:",e)})),t(".product_image_slider__trigger a",e).on("click",(function(i){i.preventDefault();var r=[];e.find(".swiper-slide .image_slider__image img").each((function(e){r.push({src:t(this).attr("src")})})),t.magnificPopup.open({items:r,mainClass:"eael-pi",gallery:{enabled:!0},type:"image"})}));var v=y.zoomEffect;window.isEditMode&&t(".eael-magnify-lens").remove(),"yes"===(null==v?void 0:v.enabled)&&("lense"===(null==v?void 0:v.type)?function(){var i={lensWidth:(null==v?void 0:v.lensSize)||100,lensHeight:(null==v?void 0:v.lensSize)||100,borderRadius:(null==v?void 0:v.lensBorderRadius)||"8px",lensBorder:null==v?void 0:v.lensBorder,autoResize:!0};function r(r){r||(r=t(".image_slider__image img",e)),r.each((function(){var e=t(this);e.off(".zoom"),t(".eael-lens-zoom, .eael-result-zoom").remove(),this.complete&&0!==this.naturalHeight?e.eaelZoomLense(i):e.on("load.zoom",(function(){t(this).eaelZoomLense(i)}))}))}r(),t(window).on("load",(function(){r()})),"function"==typeof t.fn.imagesLoaded&&t(".image_slider__image img",e).imagesLoaded((function(){r()}))}():"magnify"===(null==v?void 0:v.type)?t(".image_slider__image img",e).eaelMagnify({lensSize:(null==v?void 0:v.lensSize)||200,lensBorder:null==v?void 0:v.lensBorder}):null==v||v.type)};jQuery(window).on("elementor/frontend/init",(function(){elementorFrontend.hooks.addAction("frontend/element_ready/eael-woo-product-images.default",a)}))}});