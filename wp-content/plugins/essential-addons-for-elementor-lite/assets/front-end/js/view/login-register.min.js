!function(e){var a={};function t(n){if(a[n])return a[n].exports;var o=a[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,t),o.l=!0,o.exports}t.m=e,t.c=a,t.d=function(e,a,n){t.o(e,a)||Object.defineProperty(e,a,{enumerable:!0,get:n})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,a){if(1&a&&(e=t(e)),8&a)return e;if(4&a&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(t.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&a&&"string"!=typeof e)for(var o in e)t.d(n,o,function(a){return e[a]}.bind(null,o));return n},t.n=function(e){var a=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(a,"a",a),a},t.o=function(e,a){return Object.prototype.hasOwnProperty.call(e,a)},t.p="",t(t.s=20)}({20:function(e,a){eael.hooks.addAction("init","ea",(function(){if(eael.elementStatusCheck("eaelLoginRegister"))return!1;elementorFrontend.hooks.addAction("frontend/element_ready/eael-login-register.default",(function(e,a){var t=e.find(".eael-login-registration-wrapper"),n=t.data("widget-id"),o=t.data("recaptcha-sitekey"),r=t.data("recaptcha-sitekey-v3"),i=void 0!==t.data("is-ajax")&&"yes"==t.data("is-ajax"),l=e.find("[data-logged-in-location]").data("logged-in-location"),c=e.find("#eael-login-form-wrapper"),s=e.find("#eael-lostpassword-form-wrapper"),d=e.find("#eael-resetpassword-form-wrapper"),p=c.data("recaptcha-theme"),f=c.data("recaptcha-size"),g=e.find("#eael-register-form-wrapper"),h=g.data("recaptcha-theme"),u=g.data("recaptcha-size"),m=s.data("recaptcha-theme"),v=s.data("recaptcha-size"),w=t.data("login-recaptcha-version"),y=t.data("register-recaptcha-version"),b=t.data("lostpassword-recaptcha-version"),k=e.find("#eael-lr-reg-toggle"),_=e.find("#eael-lr-login-toggle"),S=e.find("#eael-lr-lostpassword-toggle"),I=e.find("#eael-lr-login-toggle-lostpassword"),x=c.find("#eael-user-password"),j=g.find("#form-field-password"),z=g.find("#form-field-confirm_pass"),C=d.find("#eael-pass1"),O=d.find("#eael-pass2"),E="undefined"!=typeof grecaptcha&&null!==grecaptcha,P=new URLSearchParams(location.search),M=document.getElementById("login-recaptcha-node-"+n),T=document.getElementById("register-recaptcha-node-"+n),A=document.getElementById("lostpassword-recaptcha-node-"+n);function B(e){var a="text"===e.attr("type")?"password":"text";e.attr("type",a),$icon=e.parent().find("span"),"password"===a?$icon.removeClass("dashicons-hidden").addClass("dashicons-visibility"):$icon.removeClass("dashicons-visibility").addClass("dashicons-hidden")}function D(e){for(var a=e+"=",t=decodeURIComponent(document.cookie).split(";"),n=0;n<t.length;n++){for(var o=t[n];" "==o.charAt(0);)o=o.substring(1);if(0==o.indexOf(a))return o.substring(a.length,o.length)}return""}function R(e){document.cookie=e+"=;Max-Age=0;"}function $(){if("function"!=typeof grecaptcha.render)return!1;if(M&&"v3"!==y&&"v3"!==b)try{grecaptcha.render(M,{sitekey:o,theme:p,size:f})}catch(e){}if(T&&"v3"!==w&&"v3"!==b)try{grecaptcha.render(T,{sitekey:o,theme:h,size:u})}catch(e){}if(A&&"v3"!==w&&"v3"!==y)try{grecaptcha.render(A,{sitekey:o,theme:m,size:v})}catch(e){}}if(void 0!==l&&""!==l&&location.replace(l),"form"===k.data("action")&&k.on("click",(function(e){e.preventDefault(),P.has("eael-lostpassword")&&P.delete("eael-lostpassword"),P.has("eael-register")||P.append("eael-register",1),window.history.replaceState({},"","".concat(location.pathname,"?").concat(P)),c.hide(),s.hide(),g.fadeIn()})),"form"===_.data("action")&&_.on("click",(function(e){P.has("eael-register")?P.delete("eael-register"):P.has("eael-lostpassword")&&P.delete("eael-lostpassword"),window.history.replaceState({},"","".concat(location.pathname,"?").concat(P)),e.preventDefault(),g.hide(),g.find(".eael-form-validation-container").html(""),s.hide(),c.fadeIn()})),"form"===I.data("action")&&I.on("click",(function(e){P.has("eael-register")?P.delete("eael-register"):P.has("eael-lostpassword")&&P.delete("eael-lostpassword"),window.history.replaceState({},"","".concat(location.pathname,"?").concat(P)),e.preventDefault(),s.hide(),g.hide(),c.fadeIn()})),"form"===S.data("action")&&S.on("click",(function(e){e.preventDefault(),P.has("eael-lostpassword")||P.append("eael-lostpassword",1),window.history.replaceState({},"","".concat(location.pathname,"?").concat(P)),s.find(".eael-form-validation-container").html(""),s.find(".eael-lr-form-group").css("display","bloock").removeClass("eael-d-none"),s.find("#eael-lostpassword-submit").css("display","bloock").removeClass("eael-d-none"),g.hide(),c.hide(),s.fadeIn()})),a(document).on("click","#wp-hide-pw, #wp-hide-pw1, #wp-hide-pw2, #wp-hide-pw-register",(function(e){switch(a(this).attr("id")){case"wp-hide-pw1":B(C),B(O);break;case"wp-hide-pw2":B(O);break;case"wp-hide-pw-register":B(j),z&&B(z);break;default:B(x)}})),a(document).ready((function(){if(a("[name='eael-login-submit']").on("click",(function(){localStorage.setItem("eael-is-login-form","true")})),"true"===localStorage.getItem("eael-is-login-form")&&(localStorage.removeItem("eael-is-login-form"),setTimeout((function(){a("#eael-lr-login-toggle").trigger("click")}),100)),new Promise((function(e,a){eael.getToken();var t=setInterval((function(){!0===eael.noncegenerated&&void 0!==localize.nonce&&(e(localize.nonce),clearInterval(t))}),100)})).then((function(e){a("#eael-login-nonce, #eael-register-nonce, #eael-lostpassword-nonce, #eael-resetpassword-nonce").val(e)})),!i){E&&("v3"===w||"v3"===y||"v3"===b)&&grecaptcha.ready((function(){grecaptcha.execute(r,{action:"eael_login_register_form"}).then((function(t){0===a('form input[name="g-recaptcha-response"]',e).length?a("form",e).append('<input type="hidden" name="g-recaptcha-response" value="'+t+'">'):a('form input[name="g-recaptcha-response"]',e).val(t)}))}))}var t=D("eael_login_error_"+n);t&&(a(".eael-form-validation-container",e).html('<p class="eael-form-msg invalid">'.concat(t,"</p>")),R("eael_login_error_"+n));var o=D("eael_register_errors_"+n);o&&(a(".eael-form-validation-container",e).html('<div class="eael-form-msg invalid">'.concat(o,"</div>")),R("eael_register_errors_"+n))})),E&&isEditMode)$();else{var L=window.performance.getEntriesByType("navigation");L.length>0&&L[0].loadEventEnd>0?E&&$():a(window).on("load",(function(){E&&$()}))}}))}))}});