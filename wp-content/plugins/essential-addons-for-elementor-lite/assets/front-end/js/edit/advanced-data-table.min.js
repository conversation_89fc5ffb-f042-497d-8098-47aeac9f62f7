!function(e){var a={};function t(l){if(a[l])return a[l].exports;var n=a[l]={i:l,l:!1,exports:{}};return e[l].call(n.exports,n,n.exports,t),n.l=!0,n.exports}t.m=e,t.c=a,t.d=function(e,a,l){t.o(e,a)||Object.defineProperty(e,a,{enumerable:!0,get:l})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,a){if(1&a&&(e=t(e)),8&a)return e;if(4&a&&"object"==typeof e&&e&&e.__esModule)return e;var l=Object.create(null);if(t.r(l),Object.defineProperty(l,"default",{enumerable:!0,value:e}),2&a&&"string"!=typeof e)for(var n in e)t.d(l,n,function(a){return e[a]}.bind(null,n));return l},t.n=function(e){var a=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(a,"a",a),a},t.o=function(e,a){return Object.prototype.hasOwnProperty.call(e,a)},t.p="",t(t.s=42)}({42:function(e,a){function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function l(e,a){for(var t=0;t<a.length;t++){var l=a[t];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(e,n(l.key),l)}}function n(e){var a=function(e,a){if("object"!=t(e)||!e)return e;var l=e[Symbol.toPrimitive];if(void 0!==l){var n=l.call(e,a||"default");if("object"!=t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(e)}(e,"string");return"symbol"==t(a)?a:a+""}var o=function(){return e=function e(){!function(e,a){if(!(e instanceof a))throw new TypeError("Cannot call a class as a function")}(this,e),this.panel=null,this.model=null,this.view=null,this.table=null,this.tableInnerHTML=null,this.timeout=null,this.activeCell=null,this.dragStartX=null,this.dragStartWidth=null,this.dragEl=null,this.dragging=!1,eael.hooks.addFilter("advancedDataTable.getClassProps","ea",this.getClassProps.bind(this)),eael.hooks.addFilter("advancedDataTable.setClassProps","ea",this.setClassProps.bind(this)),eael.hooks.addFilter("advancedDataTable.parseHTML","ea",this.parseHTML),eael.hooks.addAction("advancedDataTable.initEditor","ea",this.initEditor.bind(this)),eael.hooks.addAction("advancedDataTable.updateFromView","ea",this.updateFromView.bind(this)),eael.hooks.addAction("advancedDataTable.initInlineEdit","ea",this.initInlineEdit.bind(this)),eael.hooks.addAction("advancedDataTable.initPanelAction","ea",this.initPanelAction.bind(this)),eael.hooks.addAction("advancedDataTable.triggerTextChange","ea",this.triggerTextChange.bind(this)),elementor.hooks.addFilter("elements/widget/contextMenuGroups",this.initContextMenu),elementor.hooks.addAction("panel/open_editor/widget/eael-advanced-data-table",this.initPanel.bind(this))},(a=[{key:"updateFromView",value:function(e,a){var t=arguments.length>2&&void 0!==arguments[2]&&arguments[2],l=e.model;if(l.remoteRender=t,elementor.config.version>"2.7.6"){var n=e.getContainer(),o=e.getContainer().settings.attributes;Object.keys(a).forEach((function(e){o[e]=a[e]})),parent.window.$e.run("document/elements/settings",{container:n,settings:o,options:{external:t}})}else Object.keys(a).forEach((function(e){l.setSetting(e,a[e])}));this.timeout=setTimeout((function(){l.remoteRender=!0}),1001)}},{key:"getClassProps",value:function(){return{view:this.view,model:this.model,table:this.table,activeCell:this.activeCell}}},{key:"setClassProps",value:function(e){var a=this;Object.keys(e).forEach((function(t){a[t]=e[t]}))}},{key:"parseHTML",value:function(e){return e.querySelectorAll("th, td").forEach((function(e){null!==e.querySelector(".inline-editor")&&(e.innerHTML=decodeURI(e.dataset.quill||""),delete e.dataset.quill)})),e}},{key:"initEditor",value:function(e){var a=this;e.dataset.quill=encodeURI(e.innerHTML),e.innerHTML='<div class="inline-editor">'.concat(e.innerHTML,"</div>");var t=new Quill(e.querySelector(".inline-editor"),{theme:"bubble",modules:{toolbar:["bold","italic","underline","strike","link",{list:"ordered"},{list:"bullet"}]}});t.on("text-change",(function(l,n,o){clearTimeout(a.timeout),e.dataset.quill=encodeURI(t.root.innerHTML);var i=a.parseHTML(a.table.cloneNode(!0));a.tableInnerHTML=i.innerHTML,a.updateFromView(a.view,{ea_adv_data_table_static_html:i.innerHTML})}))}},{key:"initInlineEdit",value:function(){var e=this,a=setInterval((function(){e.view.el.querySelector(".ea-advanced-data-table")&&(e.table!==e.view.el.querySelector(".ea-advanced-data-table")&&(e.table=e.view.el.querySelector(".ea-advanced-data-table"),e.table.classList.contains("ea-advanced-data-table-static")&&e.table.querySelectorAll("th, td").forEach((function(a){e.initEditor(a)})),e.table.addEventListener("mousedown",(function(a){a.stopPropagation(),"th"===a.target.tagName.toLowerCase()&&(e.dragging=!0,e.dragEl=a.target,e.dragStartX=a.pageX,e.dragStartWidth=a.target.offsetWidth),"th"===a.target.tagName.toLowerCase()||"td"===a.target.tagName.toLowerCase()?e.activeCell=a.target:"th"===a.target.parentNode.tagName.toLowerCase()||"td"===a.target.parentNode.tagName.toLowerCase()?e.activeCell=a.target.parentNode:"th"===a.target.parentNode.parentNode.tagName.toLowerCase()||"td"===a.target.parentNode.parentNode.tagName.toLowerCase()?e.activeCell=a.target.parentNode.parentNode:"th"!==a.target.parentNode.parentNode.parentNode.tagName.toLowerCase()&&"td"!==a.target.parentNode.parentNode.parentNode.tagName.toLowerCase()||(e.activeCell=a.target.parentNode.parentNode.parentNode)})),e.table.addEventListener("mousemove",(function(a){e.dragging&&(e.dragEl.style.width="".concat(e.dragStartWidth+(event.pageX-e.dragStartX),"px"))})),e.table.addEventListener("mouseup",(function(a){if(e.dragging)if(e.dragging=!1,clearTimeout(e.timeout),e.table.classList.contains("ea-advanced-data-table-static")){var t=e.parseHTML(e.table.cloneNode(!0));e.updateFromView(e.view,{ea_adv_data_table_static_html:t.innerHTML})}else{var l=[];e.table.querySelectorAll("th").forEach((function(e,a){l[a]=e.style.width})),e.updateFromView(e.view,{ea_adv_data_table_dynamic_th_width:l})}})),e.table.addEventListener("dblclick",(function(a){if("th"===a.target.tagName.toLowerCase())if(a.stopPropagation(),clearTimeout(e.timeout),e.table.classList.contains("ea-advanced-data-table-static")){var t=e.parseHTML(e.table.cloneNode(!0));e.updateFromView(e.view,{ea_adv_data_table_static_html:t.innerHTML})}else{var l=[];e.table.querySelectorAll("th").forEach((function(e,a){l[a]=e.style.width})),e.updateFromView(e.view,{ea_adv_data_table_dynamic_th_width:l})}}))),clearInterval(a))}),500)}},{key:"initPanelAction",value:function(){var e=this;this.panel.content.el.onclick=function(a){if("ea:advTable:export"==a.target.dataset.event){for(var t=e.table.querySelectorAll("table tr"),l=[],n=0;n<t.length;n++){var o=[],i=t[n].querySelectorAll("th, td");if(e.table.classList.contains("ea-advanced-data-table-static"))for(var r=0;r<i.length;r++){var d=decodeURI(i[r].dataset.quill).replace(/"/g,'""');d='"'.concat(d,'"'),o.push(d)}else for(var s=0;s<i.length;s++)o.push(JSON.stringify(i[s].innerHTML.replace(/,"""([^"]+)""",/g,',"$1",').trim()));l.push(o.join(","))}var c=new Blob([l.join("\n")],{type:"text/csv"}),u=parent.document.createElement("a");u.classList.add("ea-adv-data-table-download-".concat(e.model.attributes.id)),u.download="ea-adv-data-table-".concat(e.model.attributes.id,".csv"),u.href=window.URL.createObjectURL(c),u.style.display="none",parent.document.body.appendChild(u),u.click(),parent.document.querySelector(".ea-adv-data-table-download-".concat(e.model.attributes.id)).remove()}else if("ea:advTable:import"==a.target.dataset.event){var v=e.panel.content.el.querySelector(".ea_adv_table_csv_string"),h=e.panel.content.el.querySelector(".ea_adv_table_csv_string_table").checked,b=v.value.split("\n"),p="",f="";if(v.value.length>0&&(f+="<tbody>",b.forEach((function(e,a){for(var t=[],l="",n=!1,o=0;o<e.length;){var i=e[o];'"'===i?n&&'"'===e[o+1]?(l+='"',o++):n=!n:","!==i||n?l+=i:(t.push(l),l=""),o++}t.push(l),t.length>0&&(h&&0==a?(p+="<thead><tr>",t.forEach((function(e){p+="<th>".concat(e,"</th>")})),p+="</tr></thead>"):(f+="<tr>",t.forEach((function(e){f+="<td>".concat(e,"</td>")})),f+="</tr>"))})),f+="</tbody>",p.length>0||f.length>0)){e.tableInnerHTML=p+f,e.updateFromView(e.view,{ea_adv_data_table_csv_html:p+f},!0);var g=setInterval((function(){e.view.el.querySelector(".ea-advanced-data-table").innerHTML==p+f&&(clearInterval(g),eael.hooks.doAction("advancedDataTable.initInlineEdit"))}),500)}v.value=""}eael.hooks.doAction("advancedDataTable.panelAction",e.panel,e.model,e.view,a)}}},{key:"initPanel",value:function(e,a,t){var l=this;this.panel=e,this.model=a,this.view=t;var n=".ea-advanced-data-table-".concat(this.view.container.args.id),o=this.view.el.querySelector(".ea-advanced-data-table"+n);eael.hooks.doAction("advancedDataTable.initInlineEdit"),eael.hooks.doAction("advancedDataTable.initPanelAction"),eael.hooks.doAction("advancedDataTable.afterInitPanel",e,a,t),a.once("editor:close",(function(){if(!o)return!1;var e=l.parseHTML(o.cloneNode(!0));l.tableInnerHTML=e.innerHTML}))}},{key:"initContextMenu",value:function(e,a){return"eael-advanced-data-table"==a.options.model.attributes.widgetType&&"static"==a.options.model.attributes.settings.attributes.ea_adv_data_table_source&&e.push({name:"ea_advanced_data_table",actions:[{name:"add_row_above",title:"Add Row Above",callback:function(){var e=eael.hooks.applyFilters("advancedDataTable.getClassProps"),a=e.view,t=e.table,l=e.activeCell;if(jQuery(t).find("tr:empty").each((function(){0==jQuery(this).find("td").length&&this.remove()})),null!==l&&"th"!=l.tagName.toLowerCase()&&l.parentNode.rowIndex){for(var n=l.parentNode.rowIndex,o=t.insertRow(n),i=0;i<t.rows[0].cells.length;i++){var r=o.insertCell(i);eael.hooks.doAction("advancedDataTable.initEditor",r)}eael.hooks.applyFilters("advancedDataTable.setClassProps",{activeCell:null});var d=eael.hooks.applyFilters("advancedDataTable.parseHTML",t.cloneNode(!0));eael.hooks.doAction("advancedDataTable.updateFromView",a,{ea_adv_data_table_static_html:d.innerHTML}),eael.hooks.doAction("advancedDataTable.triggerTextChange",t)}}},{name:"add_row_below",title:"Add Row Below",callback:function(){var e=eael.hooks.applyFilters("advancedDataTable.getClassProps"),a=e.view,t=e.table,l=e.activeCell;if(null!==l){for(var n=l.parentNode.rowIndex+1,o=t.insertRow(n),i=0;i<t.rows[0].cells.length;i++){var r=o.insertCell(i);eael.hooks.doAction("advancedDataTable.initEditor",r)}eael.hooks.applyFilters("advancedDataTable.setClassProps",{activeCell:null});var d=eael.hooks.applyFilters("advancedDataTable.parseHTML",t.cloneNode(!0));eael.hooks.doAction("advancedDataTable.updateFromView",a,{ea_adv_data_table_static_html:d.innerHTML}),eael.hooks.doAction("advancedDataTable.triggerTextChange",t)}}},{name:"add_column_left",title:"Add Column Left",callback:function(){var e=eael.hooks.applyFilters("advancedDataTable.getClassProps"),a=e.view,t=e.table,l=e.activeCell;if(null!==l){for(var n=l.cellIndex,o=0;o<t.rows.length;o++)if("th"==t.rows[o].cells[0].tagName.toLowerCase()){var i=t.rows[o].insertBefore(document.createElement("th"),t.rows[o].cells[n]);eael.hooks.doAction("advancedDataTable.initEditor",i)}else{var r=t.rows[o].insertCell(n);eael.hooks.doAction("advancedDataTable.initEditor",r)}eael.hooks.applyFilters("advancedDataTable.setClassProps",{activeCell:null});var d=eael.hooks.applyFilters("advancedDataTable.parseHTML",t.cloneNode(!0));eael.hooks.doAction("advancedDataTable.updateFromView",a,{ea_adv_data_table_static_html:d.innerHTML}),eael.hooks.doAction("advancedDataTable.triggerTextChange",t)}}},{name:"add_column_right",title:"Add Column Right",callback:function(){var e=eael.hooks.applyFilters("advancedDataTable.getClassProps"),a=e.view,t=e.table,l=e.activeCell;if(null!==l){for(var n=l.cellIndex+1,o=0;o<t.rows.length;o++)if("th"==t.rows[o].cells[0].tagName.toLowerCase()){var i=t.rows[o].insertBefore(document.createElement("th"),t.rows[o].cells[n]);eael.hooks.doAction("advancedDataTable.initEditor",i)}else{var r=t.rows[o].insertCell(n);eael.hooks.doAction("advancedDataTable.initEditor",r)}eael.hooks.applyFilters("advancedDataTable.setClassProps",{activeCell:null});var d=eael.hooks.applyFilters("advancedDataTable.parseHTML",t.cloneNode(!0));eael.hooks.doAction("advancedDataTable.updateFromView",a,{ea_adv_data_table_static_html:d.innerHTML}),eael.hooks.doAction("advancedDataTable.triggerTextChange",t)}}},{name:"delete_row",title:"Delete Row",callback:function(){var e=eael.hooks.applyFilters("advancedDataTable.getClassProps"),a=e.view,t=e.table,l=e.activeCell;if(null!==l){var n=l.parentNode.rowIndex;t.deleteRow(n),eael.hooks.applyFilters("advancedDataTable.setClassProps",{activeCell:null});var o=eael.hooks.applyFilters("advancedDataTable.parseHTML",t.cloneNode(!0));eael.hooks.doAction("advancedDataTable.updateFromView",a,{ea_adv_data_table_static_html:o.innerHTML}),eael.hooks.doAction("advancedDataTable.triggerTextChange",t)}}},{name:"delete_column",title:"Delete Column",callback:function(){var e=eael.hooks.applyFilters("advancedDataTable.getClassProps"),a=e.view,t=e.table,l=e.activeCell;if(null!==l){for(var n=l.cellIndex,o=0;o<t.rows.length;o++)t.rows[o].deleteCell(n);eael.hooks.applyFilters("advancedDataTable.setClassProps",{activeCell:null});var i=eael.hooks.applyFilters("advancedDataTable.parseHTML",t.cloneNode(!0));eael.hooks.doAction("advancedDataTable.updateFromView",a,{ea_adv_data_table_static_html:i.innerHTML}),eael.hooks.doAction("advancedDataTable.triggerTextChange",t)}}}]}),e}},{key:"triggerTextChange",value:function(e){if(e.classList.contains("ea-advanced-data-table-static")){var a,t=(a=(a=jQuery("thead tr:first-child th:first-child .ql-editor p",e)).length?a:jQuery("tbody tr:first-child td:first-child .ql-editor p",e)).html();a.html(t+" "),setTimeout((function(){a.html(t)}),1100)}}}])&&l(e.prototype,a),t&&l(e,t),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,a,t}();eael.hooks.addAction("editMode.init","ea",(function(){new o}))}});