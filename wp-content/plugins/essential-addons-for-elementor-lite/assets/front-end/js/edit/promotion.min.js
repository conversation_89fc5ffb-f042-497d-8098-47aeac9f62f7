!function(e){var t={};function o(n){if(t[n])return t[n].exports;var r=t[n]={i:n,l:!1,exports:{}};return e[n].call(r.exports,r,r.exports,o),r.l=!0,r.exports}o.m=e,o.c=t,o.d=function(e,t,n){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(o.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)o.d(n,r,function(t){return e[t]}.bind(null,r));return n},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="",o(o.s=44)}({44:function(e,t){eael.hooks.addAction("editMode.init","ea",(function(){if(void 0===parent.document)return!1;parent.document.addEventListener("mousedown",(function(e){var t=parent.document.querySelectorAll(".elementor-element--promotion");if(t.length>0)for(var o=0;o<t.length;o++)if(t[o].contains(e.target)){var n=parent.document.querySelector("#elementor-element--promotion__dialog");if(t[o].querySelector(".icon > i").classList.toString().indexOf("eaicon")>=0)if(n.querySelector(".dialog-buttons-action").style.display="none",e.stopImmediatePropagation(),null===n.querySelector(".ea-dialog-buttons-action")){var r=document.createElement("a"),a=document.createTextNode("Upgrade Essential Addons");r.setAttribute("href","https://wpdeveloper.com/upgrade/ea-pro"),r.setAttribute("target","_blank"),r.classList.add("dialog-button","dialog-action","dialog-buttons-action","elementor-button","go-pro","elementor-button-success","ea-dialog-buttons-action"),r.appendChild(a),n.querySelector(".dialog-buttons-action").insertAdjacentHTML("afterend",r.outerHTML)}else n.querySelector(".ea-dialog-buttons-action").style.display="";else n.querySelector(".dialog-buttons-action").style.display="",null!==n.querySelector(".ea-dialog-buttons-action")&&(n.querySelector(".ea-dialog-buttons-action").style.display="none");break}}))}))}});